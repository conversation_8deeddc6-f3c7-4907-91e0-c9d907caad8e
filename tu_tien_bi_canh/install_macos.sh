#!/bin/bash
# Auto install script for macOS

echo "🍎 Tu Tien Bi Canh - macOS Auto Installer"
echo "========================================"

# Possible Steam paths on macOS
STEAM_PATH1="$HOME/Library/Application Support/Steam/steamapps/common/Don't Starve Together/mods"
STEAM_PATH2="$HOME/Library/Containers/com.valvesoftware.steam/Data/Library/Application Support/Steam/steamapps/common/Don't Starve Together/mods"

# Check which path exists
if [ -d "$STEAM_PATH1" ]; then
    MODS_PATH="$STEAM_PATH1"
    echo "✅ Found Steam at: $STEAM_PATH1"
elif [ -d "$STEAM_PATH2" ]; then
    MODS_PATH="$STEAM_PATH2"
    echo "✅ Found Steam at: $STEAM_PATH2"
else
    echo "❌ Could not find Don't Starve Together mods folder!"
    echo "Please make sure DST is installed and try manually:"
    echo "1. Open Steam"
    echo "2. Right-click Don't Starve Together → Properties → Local Files → Browse"
    echo "3. Look for 'mods' folder"
    exit 1
fi

# Create mods folder if it doesn't exist
mkdir -p "$MODS_PATH"

# Copy mod
echo "📁 Copying mod to: $MODS_PATH/tu_tien_bi_canh"
cp -r . "$MODS_PATH/tu_tien_bi_canh"

# Set permissions
echo "🔧 Setting permissions..."
chmod -R 755 "$MODS_PATH/tu_tien_bi_canh"

# Verify installation
if [ -f "$MODS_PATH/tu_tien_bi_canh/modinfo.lua" ]; then
    echo "✅ Mod installed successfully!"
    echo "📍 Location: $MODS_PATH/tu_tien_bi_canh"
    echo ""
    echo "🎮 Next steps:"
    echo "1. Restart Steam completely"
    echo "2. Open Don't Starve Together"
    echo "3. Go to Mods menu"
    echo "4. Look for 'Test Tu Tien'"
    echo "5. Enable the mod"
else
    echo "❌ Installation failed!"
    echo "Please try manual installation"
fi
