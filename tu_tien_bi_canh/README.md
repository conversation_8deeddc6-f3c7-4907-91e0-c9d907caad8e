# Tu Tiên B<PERSON> (Cultivation Secret Realms) Mod

## 📖 Tổng Quan

**Tu Tiên Bí <PERSON>** là một mod đầy tham vọng cho Don't Starve Together, mang đến trải nghiệm tu tiên authentic và sâu sắc. Mod kết hợp văn hóa phương Đông với gameplay phương Tây, tạo ra một thế giới song song phong phú bên cạnh gameplay gốc của DST.

## 🌟 Tính Năng Chính

### 🏔️ Hệ Thống Bí Cảnh
- **7 loại bí cảnh độc đáo**: Từ Đào Hoa Bí Cảnh yên bình đến Cổ Chiến Trường nguy hiểm
- **Xuất hiện ngẫu nhiên**: Mỗi 7-12 ngày với cơ chế thông minh
- **Thời gian tồn tại**: 3-5 ngày với cảnh báo trước khi đóng
- **Điều kiện kích hoạ<PERSON>**: Dựa trên level player và tiến độ thế giới

### ⚔️ Hệ Thống Tu Luyện
- **8 cấp tu vi**: Từ Luyện Khí Kỳ đến Đại Thừa Kỳ
- **Hệ thống kinh nghiệm**: Tăng qua meditation, combat, và quest
- **Linh khí (Spiritual Energy)**: Hệ thống mana cho phép thuật
- **Breakthrough**: Quá trình đột phá cấp độ với hiệu ứng đặc biệt

### 🔥 Hệ Thống Pháp Thuật
- **24 pháp thuật**: Từ cơ bản đến tối thượng
- **Ngũ Hành**: Kim, Mộc, Thủy, Hỏa, Thổ
- **Thiên Địa**: Sấm sét, trọng lực, thời gian
- **Âm Dương**: Sinh tử, thanh tẩy, bóng tối

### 🎭 Hệ Thống NPC
- **AI thông minh**: NPCs với tính cách và memory system
- **3 phe**: Chính đạo, Tà đạo, Trung lập
- **Dialogue system**: Lựa chọn ảnh hưởng reputation
- **Trade & Quest**: Trao đổi vật phẩm và nhiệm vụ

## 🚀 Trạng Thái Phát Triển

### ✅ Phase 1: Core Foundation (HOÀN THÀNH)
- [x] Cấu trúc mod cơ bản
- [x] Cultivation component với 8 levels
- [x] Realm Manager system
- [x] Portal spawning mechanics
- [x] Basic spell system (5 spells)
- [x] Spiritual energy system
- [x] Basic items (spiritual stones, pills, herbs)

### ✅ Phase 2: Content Expansion (HOÀN THÀNH)
- [x] 2 realm types hoàn chỉnh (Peach Blossom, Heavenly Mountain)
- [x] Advanced NPC AI với dialogue system
- [x] Memory system cho NPCs
- [x] 16 pháp thuật đầy đủ (từ basic đến ultimate)
- [x] Alchemy system foundation với pill crafting
- [x] Reputation & relationship system
- [x] Realm-specific resources và mechanics

### 📋 Phase 3: Advanced Features (KẾ HOẠCH)
- [ ] 5 realm types còn lại (Ancient Battlefield, Fire Realm, Moon Realm, Thunder Realm, Medicine Valley)
- [ ] Sect system implementation
- [ ] Advanced alchemy với mini-game
- [ ] Quest system với complex storylines
- [ ] Seasonal events và world events
- [ ] PvP mechanics trong certain realms
- [ ] Advanced UI system

## 🎮 Cách Sử Dụng

### Cài Đặt
1. Copy folder `tu_tien_bi_canh` vào thư mục mods của DST
2. Kích hoạt mod trong game
3. Tạo world mới hoặc load world hiện có

### Bắt Đầu Tu Luyện
1. **Meditation**: Ngồi yên để tăng kinh nghiệm và linh khí
2. **Explore Realms**: Tìm và khám phá các bí cảnh xuất hiện
3. **Collect Resources**: Thu thập linh thạch, thảo dược, và nguyên liệu
4. **Level Up**: Đột phá cấp độ để mở khóa pháp thuật mới

### Phím Tắt
- **U**: Mở giao diện tu luyện (sẽ có trong Phase 2)
- **Right Click**: Meditation khi không cầm gì
- **Left Click**: Tương tác với portal và NPCs

## 🔧 Cấu Hình Mod

### Tùy Chọn Cơ Bản
- **Realm Spawn Rate**: Tần suất xuất hiện bí cảnh (0.5x - 2.0x)
- **Cultivation Speed**: Tốc độ tu luyện (0.5x - 2.0x)
- **NPC Aggression**: Mức độ thù địch của NPCs
- **Realm Difficulty**: Độ khó của bí cảnh

### Tùy Chọn Nâng Cao
- **Enable PvP Realms**: Cho phép PvP trong một số bí cảnh
- **Enable Sect System**: Kích hoạt hệ thống tông phái
- **Debug Mode**: Hiển thị thông tin debug

## 🧪 Testing & Debug

### Test Functions
Sử dụng console để test các tính năng:

```lua
-- Load test script
dofile("mods/tu_tien_bi_canh/test_mod.lua")

-- Spawn test items
SpawnCultivationTestItems()

-- Test cultivation component
TestCultivationComponent()

-- Spawn test portal
SpawnTestPortal()
```

### Debug Mode
Bật debug mode trong cấu hình mod để xem:
- Thông tin spawn bí cảnh
- Tiến độ tu luyện
- Trạng thái NPCs
- Lỗi hệ thống

## 📁 Cấu Trúc Mod

```
tu_tien_bi_canh/
├── modinfo.lua                 # Thông tin mod
├── modmain.lua                 # File chính
├── scripts/
│   ├── cultivation_core/       # Hệ thống cốt lõi
│   │   ├── 01_basic_modifications/
│   │   ├── 02_components/      # Components chính
│   │   ├── 03_realm_generation/
│   │   ├── 04_spell_system/
│   │   ├── 05_npc_system/
│   │   └── 06_ui_system/
│   └── prefabs/               # Prefabs
├── images/                    # Textures và UI
├── sounds/                    # Sound effects
└── anim/                     # Animations
```

## 🤝 Đóng Góp

### Báo Lỗi
- Sử dụng GitHub Issues
- Cung cấp log file và steps to reproduce
- Bao gồm mod configuration

### Đề Xuất Tính Năng
- Mô tả chi tiết tính năng
- Giải thích tại sao cần thiết
- Đề xuất implementation approach

### Code Contribution
- Fork repository
- Tạo feature branch
- Submit pull request với mô tả chi tiết

## 📜 License

Mod này được phát triển cho cộng đồng Don't Starve Together. Vui lòng tôn trọng quyền tác giả và không sử dụng cho mục đích thương mại.

## 🙏 Credits

- **Development Team**: Tu Tiên Development Team
- **Inspiration**: Chinese cultivation novels và mythology
- **Reference Mods**: 山海秘藏, 神话：傲来神仙境
- **Community**: DST modding community

---

**"Từ survival đến immortality - hành trình tu tiên bắt đầu từ đây!"** 🌟
