-- Tu Tien Bi Canh - Simple Working Version
print("Tu Tien Bi Canh mod loading...")

-- Basic setup
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Get mod configuration
local REALM_SPAWN_RATE = GetModConfigData("realm_spawn_rate") or 1.0
local CULTIVATION_SPEED = GetModConfigData("cultivation_speed") or 1.0
local DEBUG_MODE = GetModConfigData("debug_mode") or false

-- Set up tuning
TUNING.CULTIVATION_MOD = {
    REALM_SPAWN_RATE = REALM_SPAWN_RATE,
    CULTIVATION_SPEED = CULTIVATION_SPEED,
    DEBUG_MODE = DEBUG_MODE,
}

-- Debug print function
local function DebugPrint(...)
    if DEBUG_MODE then
        print("[Tu Tien]", ...)
    end
end

-- Load core systems (simplified)
local function LoadCoreComponents()
    DebugPrint("Loading core components...")
    
    -- Try to load components safely
    local success, error_msg = pcall(function()
        modimport("scripts/cultivation_core/02_components/00_components_init.lua")
    end)
    
    if not success then
        print("[Tu Tien] Warning: Could not load all components:", error_msg)
    end
end

-- Load prefabs (simplified)
local function LoadPrefabs()
    DebugPrint("Loading prefabs...")
    
    -- Try to load prefabs safely
    local success, error_msg = pcall(function()
        modimport("scripts/prefabs/cultivation_portal.lua")
        modimport("scripts/prefabs/cultivation_items.lua")
    end)
    
    if not success then
        print("[Tu Tien] Warning: Could not load all prefabs:", error_msg)
    end
end

-- Initialize mod
local function InitializeMod()
    DebugPrint("Initializing Tu Tien Bi Canh mod...")
    
    -- Load components
    LoadCoreComponents()
    
    -- Load prefabs
    LoadPrefabs()
    
    DebugPrint("Mod initialization complete")
end

-- Player initialization
AddPlayerPostInit(function(inst)
    DebugPrint("Player spawned, adding cultivation components...")
    
    -- Add basic components safely
    local success, error_msg = pcall(function()
        if not inst.components.cultivation then
            inst:AddComponent("cultivation")
        end
        if not inst.components.spell_caster then
            inst:AddComponent("spell_caster")
        end
        if not inst.components.sect_member then
            inst:AddComponent("sect_member")
        end
        if not inst.components.quest_manager then
            inst:AddComponent("quest_manager")
        end
    end)
    
    if success then
        DebugPrint("Player components added successfully")
        
        -- Show welcome message
        inst:DoTaskInTime(2, function()
            if inst.components.talker then
                inst.components.talker:Say("Tu Tien Bi Canh mod loaded!", 3)
            end
        end)
    else
        print("[Tu Tien] Warning: Could not add all player components:", error_msg)
    end
end)

-- World initialization
AddPrefabPostInit("world", function(inst)
    DebugPrint("World spawned, adding realm manager...")
    
    local success, error_msg = pcall(function()
        if not inst.components.realm_manager then
            inst:AddComponent("realm_manager")
        end
    end)
    
    if success then
        DebugPrint("World components added successfully")
    else
        print("[Tu Tien] Warning: Could not add world components:", error_msg)
    end
end)

-- Test functions for console
GLOBAL.TestTuTien = function()
    print("=== Tu Tien Bi Canh Test ===")
    
    local player = ThePlayer
    if player then
        print("Player found:", player)
        
        if player.components.cultivation then
            print("✓ Cultivation component working")
        else
            print("✗ Cultivation component missing")
        end
        
        if player.components.spell_caster then
            print("✓ Spell caster component working")
        else
            print("✗ Spell caster component missing")
        end
        
        -- Test spawn portal
        local pos = player:GetPosition()
        local portal = SpawnPrefab("cultivation_portal")
        if portal then
            portal.Transform:SetPosition(pos.x + 5, 0, pos.z + 5)
            print("✓ Test portal spawned")
        else
            print("✗ Could not spawn portal")
        end
    else
        print("✗ No player found")
    end
    
    print("=== Test Complete ===")
end

-- Initialize the mod
InitializeMod()

print("Tu Tien Bi Canh mod loaded successfully!")
