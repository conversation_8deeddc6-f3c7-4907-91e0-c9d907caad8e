-- <PERSON> (Cultivation Secret Realms) - Main Mod File
-- Version 1.0.0

print("🌟 Tu Tiên B<PERSON> - Cultivation Secret Realms loading...")

-- Set up global environment
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Get mod configuration
local LANGUAGE = GetModConfigData("language") or "vietnamese"
local REALM_SPAWN_RATE = GetModConfigData("realm_spawn_rate") or 1.0
local REALM_DURATION = GetModConfigData("realm_duration") or 1.0
local REALM_DIFFICULTY = GetModConfigData("realm_difficulty") or 1.0
local CULTIVATION_SPEED = GetModConfigData("cultivation_speed") or 1.0
local SPIRITUAL_ENERGY_RATE = GetModConfigData("spiritual_energy_rate") or 1.0
local SPELL_POWER = GetModConfigData("spell_power") or 1.0
local ENABLE_SECT_SYSTEM = GetModConfigData("enable_sect_system")
local NPC_AGGRESSION = GetModConfigData("npc_aggression") or 1.0
local ENABLE_PVP_REALMS = GetModConfigData("enable_pvp_realms")
local RESOURCE_ABUNDANCE = GetModConfigData("resource_abundance") or 1.0
local DEBUG_MODE = GetModConfigData("debug_mode") or false

-- Set up tuning values
TUNING.CULTIVATION_MOD = {
    LANGUAGE = LANGUAGE,
    REALM_SPAWN_RATE = REALM_SPAWN_RATE,
    REALM_DURATION = REALM_DURATION,
    REALM_DIFFICULTY = REALM_DIFFICULTY,
    CULTIVATION_SPEED = CULTIVATION_SPEED,
    SPIRITUAL_ENERGY_RATE = SPIRITUAL_ENERGY_RATE,
    SPELL_POWER = SPELL_POWER,
    ENABLE_SECT_SYSTEM = ENABLE_SECT_SYSTEM,
    NPC_AGGRESSION = NPC_AGGRESSION,
    ENABLE_PVP_REALMS = ENABLE_PVP_REALMS,
    RESOURCE_ABUNDANCE = RESOURCE_ABUNDANCE,
    DEBUG_MODE = DEBUG_MODE,
}

-- Debug print function
local function DebugPrint(...)
    if DEBUG_MODE then
        print("[Tu Tiên]", ...)
    end
end

-- Welcome message based on language
local function GetWelcomeMessage()
    if LANGUAGE == "english" then
        return "Tu Tien Bi Canh - Cultivation Secret Realms loaded!"
    else
        return "Tu Tiên Bí Cảnh đã tải thành công!"
    end
end

-- Load core systems safely
local function LoadCoreComponents()
    DebugPrint("Loading core components...")

    local success, error_msg = pcall(function()
        modimport("scripts/cultivation_core/02_components/00_components_init.lua")
    end)

    if success then
        DebugPrint("✓ Core components loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load all components:", error_msg)
    end
end

-- Load prefabs safely
local function LoadPrefabs()
    DebugPrint("Loading prefabs...")

    local success, error_msg = pcall(function()
        modimport("scripts/prefabs/cultivation_portal.lua")
        modimport("scripts/prefabs/cultivation_items.lua")
        modimport("scripts/prefabs/cultivation_npcs.lua")
    end)

    if success then
        DebugPrint("✓ Prefabs loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load all prefabs:", error_msg)
    end
end

-- Load localization
local function LoadLocalization()
    DebugPrint("Loading localization for language:", LANGUAGE)

    local success, error_msg = pcall(function()
        modimport("scripts/localization/localization.lua")
    end)

    if success then
        DebugPrint("✓ Localization loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load localization:", error_msg)
    end
end

-- Initialize mod
local function InitializeMod()
    DebugPrint("Initializing Tu Tiên Bí Cảnh mod...")
    DebugPrint("Configuration:")
    DebugPrint("- Language:", LANGUAGE)
    DebugPrint("- Realm Spawn Rate:", REALM_SPAWN_RATE)
    DebugPrint("- Cultivation Speed:", CULTIVATION_SPEED)
    DebugPrint("- Debug Mode:", DEBUG_MODE)

    -- Load systems
    LoadLocalization()
    LoadCoreComponents()
    LoadPrefabs()

    DebugPrint("✓ Mod initialization complete")
end

-- Player initialization
AddPlayerPostInit(function(inst)
    DebugPrint("Player spawned, adding cultivation components...")

    local success, error_msg = pcall(function()
        -- Add core components
        if not inst.components.cultivation then
            inst:AddComponent("cultivation")
        end
        if not inst.components.spell_caster then
            inst:AddComponent("spell_caster")
        end
        if not inst.components.quest_manager then
            inst:AddComponent("quest_manager")
        end

        -- Add sect system if enabled
        if ENABLE_SECT_SYSTEM and not inst.components.sect_member then
            inst:AddComponent("sect_member")
        end

        -- Add realm status tracking
        if not inst.components.realm_status then
            inst:AddComponent("realm_status")
        end
    end)

    if success then
        DebugPrint("✓ Player components added successfully")

        -- Show welcome message
        inst:DoTaskInTime(3, function()
            if inst.components.talker then
                local message = GetWelcomeMessage()
                inst.components.talker:Say(message, 4)
            end
        end)
    else
        print("[Tu Tiên] Warning: Could not add all player components:", error_msg)
    end
end)

-- World initialization
AddPrefabPostInit("world", function(inst)
    DebugPrint("World spawned, adding realm manager...")

    local success, error_msg = pcall(function()
        if not inst.components.realm_manager then
            inst:AddComponent("realm_manager")
        end
    end)

    if success then
        DebugPrint("✓ World components added successfully")
    else
        print("[Tu Tiên] Warning: Could not add world components:", error_msg)
    end
end)

-- Console commands for testing
GLOBAL.TuTienTest = function()
    print("=== Tu Tiên Bí Cảnh Test ===")
    print("Language:", LANGUAGE)
    print("Realm Spawn Rate:", REALM_SPAWN_RATE)
    print("Cultivation Speed:", CULTIVATION_SPEED)
    print("Debug Mode:", DEBUG_MODE)

    local player = ThePlayer
    if player then
        print("✓ Player found")

        -- Test components
        local components = {"cultivation", "spell_caster", "quest_manager", "realm_status"}
        for _, comp in ipairs(components) do
            if player.components[comp] then
                print("✓", comp, "component working")
            else
                print("✗", comp, "component missing")
            end
        end

        -- Test spawn portal
        if player.components.cultivation then
            local pos = player:GetPosition()
            local portal = SpawnPrefab("cultivation_portal")
            if portal then
                portal.Transform:SetPosition(pos.x + 5, 0, pos.z + 5)
                if portal.components.cultivation_portal then
                    portal.components.cultivation_portal:SetRealmType("peach_blossom")
                    portal.components.cultivation_portal:Activate()
                end
                print("✓ Test portal spawned")
            else
                print("✗ Could not spawn portal")
            end
        end
    else
        print("✗ No player found")
    end

    print("=== Test Complete ===")
end

-- Quick realm test
GLOBAL.TuTienQuickTest = function()
    if ThePlayer then
        local pos = ThePlayer:GetPosition()
        local portal = SpawnPrefab("cultivation_portal")
        if portal then
            portal.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)
            if portal.components.cultivation_portal then
                portal.components.cultivation_portal:SetRealmType("peach_blossom")
                portal.components.cultivation_portal:Activate()
                print("✓ Quick test portal spawned - right-click to enter!")
            end
        end
    end
end

-- Initialize the mod
InitializeMod()

print("🌟 Tu Tiên Bí Cảnh - Cultivation Secret Realms loaded successfully!")
