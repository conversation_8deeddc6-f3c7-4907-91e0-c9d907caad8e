-- <PERSON> (Cultivation Secret Realms) - Main Mod File
-- Version 1.0.0

print("🌟 Tu Tiên <PERSON> - Cultivation Secret Realms loading...")

-- Set up global environment
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Register prefabs with DST (start with simple test only)
PrefabFiles = {
    "test_portal",
    -- "cultivation_portal",  -- Disable complex prefabs for now
    -- "cultivation_items",
    -- "cultivation_npcs",
}

-- Get mod configuration
local LANGUAGE = GetModConfigData("language") or "vietnamese"
local REALM_SPAWN_RATE = GetModConfigData("realm_spawn_rate") or 1.0
local REALM_DURATION = GetModConfigData("realm_duration") or 1.0
local REALM_DIFFICULTY = GetModConfigData("realm_difficulty") or 1.0
local CULTIVATION_SPEED = GetModConfigData("cultivation_speed") or 1.0
local SPIRITUAL_ENERGY_RATE = GetModConfigData("spiritual_energy_rate") or 1.0
local SPELL_POWER = GetModConfigData("spell_power") or 1.0
local ENABLE_SECT_SYSTEM = GetModConfigData("enable_sect_system")
local NPC_AGGRESSION = GetModConfigData("npc_aggression") or 1.0
local ENABLE_PVP_REALMS = GetModConfigData("enable_pvp_realms")
local RESOURCE_ABUNDANCE = GetModConfigData("resource_abundance") or 1.0
local DEBUG_MODE = GetModConfigData("debug_mode") or false

-- Set up tuning values
TUNING.CULTIVATION_MOD = {
    LANGUAGE = LANGUAGE,
    REALM_SPAWN_RATE = REALM_SPAWN_RATE,
    REALM_DURATION = REALM_DURATION,
    REALM_DIFFICULTY = REALM_DIFFICULTY,
    CULTIVATION_SPEED = CULTIVATION_SPEED,
    SPIRITUAL_ENERGY_RATE = SPIRITUAL_ENERGY_RATE,
    SPELL_POWER = SPELL_POWER,
    ENABLE_SECT_SYSTEM = ENABLE_SECT_SYSTEM,
    NPC_AGGRESSION = NPC_AGGRESSION,
    ENABLE_PVP_REALMS = ENABLE_PVP_REALMS,
    RESOURCE_ABUNDANCE = RESOURCE_ABUNDANCE,
    DEBUG_MODE = DEBUG_MODE,
}

-- Debug print function
local function DebugPrint(...)
    if DEBUG_MODE then
        print("[Tu Tiên]", ...)
    end
end

-- Welcome message based on language
local function GetWelcomeMessage()
    if LANGUAGE == "english" then
        return "Tu Tien Bi Canh - Cultivation Secret Realms loaded!"
    else
        return "Tu Tiên Bí Cảnh đã tải thành công!"
    end
end

-- Load core systems safely
local function LoadCoreComponents()
    DebugPrint("Loading core components...")

    local success, error_msg = pcall(function()
        modimport("scripts/cultivation_core/02_components/00_components_init.lua")
    end)

    if success then
        DebugPrint("✓ Core components loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load all components:", error_msg)
    end
end

-- Load prefabs safely
local function LoadPrefabs()
    DebugPrint("Loading prefabs...")

    local success, error_msg = pcall(function()
        modimport("scripts/prefabs/test_portal.lua")
        -- Only load test portal for now to avoid asset errors
        -- modimport("scripts/prefabs/cultivation_portal.lua")
        -- modimport("scripts/prefabs/cultivation_items.lua")
        -- modimport("scripts/prefabs/cultivation_npcs.lua")
    end)

    if success then
        DebugPrint("✓ Prefabs loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load all prefabs:", error_msg)
    end
end

-- Load localization
local function LoadLocalization()
    DebugPrint("Loading localization for language:", LANGUAGE)

    local success, error_msg = pcall(function()
        modimport("scripts/localization/localization.lua")
    end)

    if success then
        DebugPrint("✓ Localization loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load localization:", error_msg)
    end
end

-- Initialize mod
local function InitializeMod()
    DebugPrint("Initializing Tu Tiên Bí Cảnh mod...")
    DebugPrint("Configuration:")
    DebugPrint("- Language:", LANGUAGE)
    DebugPrint("- Realm Spawn Rate:", REALM_SPAWN_RATE)
    DebugPrint("- Cultivation Speed:", CULTIVATION_SPEED)
    DebugPrint("- Debug Mode:", DEBUG_MODE)

    -- Load systems
    LoadLocalization()
    LoadCoreComponents()
    LoadPrefabs()

    DebugPrint("✓ Mod initialization complete")
end

-- Player initialization
AddPlayerPostInit(function(inst)
    DebugPrint("Player spawned, adding cultivation components...")

    local success, error_msg = pcall(function()
        -- Add core components
        if not inst.components.cultivation then
            inst:AddComponent("cultivation")
        end
        if not inst.components.spell_caster then
            inst:AddComponent("spell_caster")
        end
        if not inst.components.quest_manager then
            inst:AddComponent("quest_manager")
        end

        -- Add sect system if enabled
        if ENABLE_SECT_SYSTEM and not inst.components.sect_member then
            inst:AddComponent("sect_member")
        end

        -- Add realm status tracking
        if not inst.components.realm_status then
            inst:AddComponent("realm_status")
        end
    end)

    if success then
        DebugPrint("✓ Player components added successfully")

        -- Show welcome message
        inst:DoTaskInTime(3, function()
            if inst.components.talker then
                local message = GetWelcomeMessage()
                inst.components.talker:Say(message, 4)
            end
        end)
    else
        print("[Tu Tiên] Warning: Could not add all player components:", error_msg)
    end
end)

-- World initialization
AddPrefabPostInit("world", function(inst)
    DebugPrint("World spawned, adding realm manager...")

    local success, error_msg = pcall(function()
        if not inst.components.realm_manager then
            inst:AddComponent("realm_manager")
        end
    end)

    if success then
        DebugPrint("✓ World components added successfully")
    else
        print("[Tu Tiên] Warning: Could not add world components:", error_msg)
    end
end)

-- Console commands for testing
GLOBAL.TuTienTest = function()
    print("=== Tu Tiên Bí Cảnh Test ===")
    print("Language:", LANGUAGE)
    print("Realm Spawn Rate:", REALM_SPAWN_RATE)
    print("Cultivation Speed:", CULTIVATION_SPEED)
    print("Debug Mode:", DEBUG_MODE)

    local player = ThePlayer
    if player then
        print("✓ Player found")

        -- Test components
        local components = {"cultivation", "spell_caster", "quest_manager", "realm_status"}
        for _, comp in ipairs(components) do
            if player.components[comp] then
                print("✓", comp, "component working")
            else
                print("✗", comp, "component missing")
            end
        end

        -- Test spawn portal
        if player.components.cultivation then
            local pos = player:GetPosition()
            local portal = SpawnPrefab("cultivation_portal")
            if portal then
                portal.Transform:SetPosition(pos.x + 5, 0, pos.z + 5)
                if portal.components.cultivation_portal then
                    portal.components.cultivation_portal:SetRealmType("peach_blossom")
                    portal.components.cultivation_portal:Activate()
                end
                print("✓ Test portal spawned")
            else
                print("✗ Could not spawn portal")
            end
        end
    else
        print("✗ No player found")
    end

    print("=== Test Complete ===")
end

-- Quick realm test
GLOBAL.TuTienQuickTest = function()
    if ThePlayer then
        local pos = ThePlayer:GetPosition()

        -- Try to spawn portal
        local portal = SpawnPrefab("cultivation_portal")
        if portal then
            portal.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)
            if portal.components.cultivation_portal then
                portal.components.cultivation_portal:SetRealmType("peach_blossom")
                portal.components.cultivation_portal:Activate()
                print("✓ Quick test portal spawned - right-click to enter!")
            else
                print("✗ Portal spawned but missing cultivation_portal component")
            end
        else
            print("✗ Could not spawn cultivation_portal prefab")
            print("Available prefabs:")

            -- Try alternative - spawn a simple test object
            local test_obj = SpawnPrefab("campfire")
            if test_obj then
                test_obj.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)
                print("✓ Test object (campfire) spawned instead")
                print("This means SpawnPrefab works, but cultivation_portal prefab is not registered")
            end
        end
    else
        print("✗ No player found")
    end
end

-- Simple test without prefabs
GLOBAL.TuTienSimpleTest = function()
    print("=== Simple Tu Tien Test ===")

    if ThePlayer then
        print("✓ Player found")

        -- Test components
        if ThePlayer.components.cultivation then
            print("✓ Cultivation component exists")
            print("  Level:", ThePlayer.components.cultivation.level)
            print("  Experience:", ThePlayer.components.cultivation.experience)
        else
            print("✗ Cultivation component missing")
        end

        if ThePlayer.components.spell_caster then
            print("✓ Spell caster component exists")
        else
            print("✗ Spell caster component missing")
        end

        -- Test basic game functions
        local pos = ThePlayer:GetPosition()
        print("✓ Player position:", pos.x, pos.z)

        -- Test spawning basic items
        local item = SpawnPrefab("log")
        if item then
            item.Transform:SetPosition(pos.x + 2, 0, pos.z + 2)
            print("✓ Basic spawning works (log spawned)")
        end

    else
        print("✗ No player found")
    end

    print("=== Simple Test Complete ===")
end

-- Test with simple portal
GLOBAL.TuTienTestPortal = function()
    print("=== Portal Test ===")

    if ThePlayer then
        local pos = ThePlayer:GetPosition()

        -- Try simple test portal first
        print("Trying to spawn test_portal...")
        local test_portal = SpawnPrefab("test_portal")
        if test_portal then
            test_portal.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)
            print("✓ Test portal spawned successfully!")
            print("Right-click it to test interaction")
        else
            print("✗ Could not spawn test_portal")
        end

        -- Try cultivation portal
        print("Trying to spawn cultivation_portal...")
        local cult_portal = SpawnPrefab("cultivation_portal")
        if cult_portal then
            cult_portal.Transform:SetPosition(pos.x + 6, 0, pos.z + 6)
            print("✓ Cultivation portal spawned successfully!")
        else
            print("✗ Could not spawn cultivation_portal")
            print("This means the prefab is not properly registered")
        end

    else
        print("✗ No player found")
    end

    print("=== Portal Test Complete ===")
end

-- Test realm system without custom prefabs
GLOBAL.TuTienRealmTest = function()
    print("=== Realm System Test (No Custom Prefabs) ===")

    if ThePlayer then
        local pos = ThePlayer:GetPosition()

        -- Test 1: Create a fake "realm" using existing objects
        print("Creating fake realm with existing objects...")

        -- Spawn some "mystical" objects using existing prefabs
        local objects = {
            {prefab = "flower", name = "Mystical Flower", offset = {2, 0}},
            {prefab = "berrybush", name = "Spirit Berry", offset = {-2, 2}},
            {prefab = "sapling", name = "Cultivation Tree", offset = {0, 3}},
            {prefab = "rock1", name = "Spirit Stone", offset = {3, -1}},
        }

        for _, obj_data in ipairs(objects) do
            local obj = SpawnPrefab(obj_data.prefab)
            if obj then
                obj.Transform:SetPosition(pos.x + obj_data.offset[1], 0, pos.z + obj_data.offset[2])

                -- Add mystical glow effect
                if obj.AnimState then
                    obj.AnimState:SetMultColour(0.7, 0.9, 1.2, 1.0) -- Blue mystical tint
                end

                -- Add custom description
                if obj.components.inspectable then
                    obj.components.inspectable:SetDescription("A mystical " .. obj_data.name .. " from the cultivation realm!")
                end

                print("✓ Spawned", obj_data.name, "at", obj_data.offset[1], obj_data.offset[2])
            end
        end

        -- Test 2: Test player cultivation components
        print("Testing cultivation components...")
        if ThePlayer.components.cultivation then
            print("✓ Cultivation component exists")

            -- Test meditation
            print("Testing meditation...")
            ThePlayer.components.cultivation:StartMeditation()
            print("✓ Meditation started")

            -- Test experience gain
            local old_exp = ThePlayer.components.cultivation.experience
            ThePlayer.components.cultivation:AddExperience(50)
            local new_exp = ThePlayer.components.cultivation.experience
            print("✓ Experience gained:", new_exp - old_exp)

            -- Test level info
            print("Current level:", ThePlayer.components.cultivation:GetLevelName())
            print("Current experience:", new_exp)

        else
            print("✗ Cultivation component missing")
        end

        -- Test 3: Test spell system
        if ThePlayer.components.spell_caster then
            print("✓ Spell caster component exists")

            -- Give some spiritual energy for testing
            if ThePlayer.components.cultivation then
                ThePlayer.components.cultivation.spiritual_energy = 100
                print("✓ Set spiritual energy to 100 for testing")
            end

            -- Test spell casting (this might not work without spell prefabs, but we can try)
            print("Testing spell casting...")
            local success = pcall(function()
                ThePlayer.components.spell_caster:CastSpell("metal_spike")
            end)

            if success then
                print("✓ Spell casting works")
            else
                print("⚠ Spell casting needs spell prefabs (expected)")
            end

        else
            print("✗ Spell caster component missing")
        end

        -- Test 4: Show player a "realm entered" message
        if ThePlayer.components.talker then
            ThePlayer.components.talker:Say("Entered mystical test realm!", 3)
            print("✓ Realm entry message displayed")
        end

        print("✓ Fake realm created successfully!")
        print("Look around - you should see mystical glowing objects!")

    else
        print("✗ No player found")
    end

    print("=== Realm System Test Complete ===")
end

-- Hot reload function for development
GLOBAL.TuTienReload = function()
    print("🔄 Reloading Tu Tien mod...")

    -- Reload core functions
    local success, error_msg = pcall(function()
        InitializeMod()

        -- Re-add components to current player if exists
        if ThePlayer then
            if not ThePlayer.components.cultivation then
                ThePlayer:AddComponent("cultivation")
            end
            if not ThePlayer.components.spell_caster then
                ThePlayer:AddComponent("spell_caster")
            end
            if not ThePlayer.components.realm_status then
                ThePlayer:AddComponent("realm_status")
            end

            print("✓ Player components refreshed")
        end

        print("✓ Mod reloaded successfully!")
    end)

    if not success then
        print("✗ Reload failed:", error_msg)
    end
end

-- Quick development test
GLOBAL.TuTienDevTest = function()
    print("🧪 Development Test Mode")

    -- Auto reload
    TuTienReload()

    -- Run tests
    TuTienSimpleTest()
    TuTienRealmTest()

    print("🎯 Development test complete!")
end

-- Initialize the mod
InitializeMod()

print("🌟 Tu Tiên Bí Cảnh - Cultivation Secret Realms loaded successfully!")
print("🔧 Development commands available:")
print("   TuTienReload() - Hot reload mod")
print("   TuTienDevTest() - Full development test")
