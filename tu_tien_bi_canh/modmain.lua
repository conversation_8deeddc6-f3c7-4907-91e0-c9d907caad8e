-- Tu <PERSON> (Cultivation Secret Realms) - Main Mod File
-- Version 1.0.0

-- Set up global environment
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Initialize assets table
Assets = {}

-- Initialize prefab files table
PrefabFiles = {}

-- Get mod configuration
local REALM_SPAWN_RATE = GetModConfigData("realm_spawn_rate") or 1.0
local CULTIVATION_SPEED = GetModConfigData("cultivation_speed") or 1.0
local NPC_AGGRESSION = GetModConfigData("npc_aggression") or 1.0
local ENABLE_PVP_REALMS = GetModConfigData("enable_pvp_realms")
local REALM_DIFFICULTY = GetModConfigData("realm_difficulty") or 1.0
local ENABLE_SECT_SYSTEM = GetModConfigData("enable_sect_system")
local DEBUG_MODE = GetModConfigData("debug_mode") or false

-- Store config in global for access by other files
GLOBAL.TUNING.CULTIVATION_MOD = {
    REALM_SPAWN_RATE = REALM_SPAWN_RATE,
    CULTIVATION_SPEED = CULTIVATION_SPEED,
    NPC_AGGRESSION = NPC_AGGRESSION,
    ENABLE_PVP_REALMS = ENABLE_PVP_REALMS,
    REALM_DIFFICULTY = REALM_DIFFICULTY,
    ENABLE_SECT_SYSTEM = ENABLE_SECT_SYSTEM,
    DEBUG_MODE = DEBUG_MODE,
}

-- Debug print function
local function DebugPrint(...)
    if DEBUG_MODE then
        print("[Tu Tiên Bí Cảnh]", ...)
    end
end

DebugPrint("Mod loading started...")
DebugPrint("Config - Realm Spawn Rate:", REALM_SPAWN_RATE)
DebugPrint("Config - Cultivation Speed:", CULTIVATION_SPEED)

-- Load localization system first
modimport("scripts/localization/localization.lua")

-- Load core modules
modimport("scripts/cultivation_core/00_cultivation_core_init.lua")

-- Load basic assets and components
modimport("scripts/cultivation_assets.lua")

-- Load prefabs
PrefabFiles = {
    -- Core systems
    "cultivation_portal",
    "cultivation_ui",

    -- Test realm (Đào Hoa Bí Cảnh)
    "realm_peach_blossom",

    -- Basic cultivation items
    "cultivation_items",

    -- Peach Blossom Realm resources
    "peach_blossom_resources",

    -- Basic spells
    "cultivation_spells",

    -- NPCs
    "cultivation_npcs",
}

-- Add basic assets
table.insert(Assets, Asset("ATLAS", "images/modicon.xml"))
table.insert(Assets, Asset("IMAGE", "images/modicon.tex"))

-- Load UI assets
table.insert(Assets, Asset("ATLAS", "images/ui/cultivation_ui.xml"))
table.insert(Assets, Asset("IMAGE", "images/ui/cultivation_ui.tex"))

-- Load cultivation system components
AddReplicableComponent("cultivation")
AddReplicableComponent("realm_manager")
AddReplicableComponent("spell_caster")

-- Initialize cultivation tuning values
GLOBAL.TUNING.CULTIVATION = {
    -- Realm spawn settings
    REALM_SPAWN_CHANCE = 0.25 * REALM_SPAWN_RATE, -- 25% base chance
    REALM_SPAWN_INTERVAL = math.max(5, 10 / REALM_SPAWN_RATE), -- days
    REALM_DURATION = 4, -- days
    REALM_WARNING_TIME = 1, -- days before closing
    
    -- Cultivation levels
    LEVELS = {
        "qi_refining",      -- 练气期
        "foundation",       -- 筑基期  
        "golden_core",      -- 金丹期
        "nascent_soul",     -- 元婴期
        "spirit_transform", -- 化神期
        "void_refining",    -- 炼虚期
        "integration",      -- 合体期
        "great_vehicle",    -- 大乘期
    },
    
    -- Experience requirements for each level
    EXP_REQUIREMENTS = {
        100,   -- qi_refining -> foundation
        300,   -- foundation -> golden_core
        600,   -- golden_core -> nascent_soul
        1000,  -- nascent_soul -> spirit_transform
        1500,  -- spirit_transform -> void_refining
        2200,  -- void_refining -> integration
        3000,  -- integration -> great_vehicle
    },
    
    -- Cultivation speed modifier
    SPEED_MODIFIER = CULTIVATION_SPEED,
    
    -- Spiritual energy (Linh Khí) settings
    SPIRITUAL_ENERGY = {
        BASE_REGEN = 1, -- per minute
        MAX_CAPACITY = 100,
        MEDITATION_BONUS = 3,
        COMBAT_DRAIN = 5,
    },
    
    -- Spell costs and cooldowns
    SPELLS = {
        BASIC_COST = 10,
        INTERMEDIATE_COST = 20,
        ADVANCED_COST = 35,
        MASTER_COST = 50,
        ULTIMATE_COST = 75,
        
        BASIC_COOLDOWN = 5,
        INTERMEDIATE_COOLDOWN = 10,
        ADVANCED_COOLDOWN = 15,
        MASTER_COOLDOWN = 20,
        ULTIMATE_COOLDOWN = 30,
    },
}

-- World generation modifications
AddPrefabPostInit("world", function(inst)
    if not TheWorld.ismastersim then
        return
    end
    
    -- Add realm manager component to world
    inst:AddComponent("realm_manager")

    -- Add world event manager
    inst:AddComponent("world_event_manager")

    DebugPrint("World initialized with realm manager and world events")
end)

-- Player initialization
AddPlayerPostInit(function(inst)
    -- Add cultivation component to all players
    inst:AddComponent("cultivation")
    inst:AddComponent("spell_caster")
    inst:AddComponent("sect_member")
    inst:AddComponent("quest_manager")
    inst:AddComponent("cultivation_ui")

    DebugPrint("Player initialized with cultivation components")
end)

-- Key binding for UI (Client-side only)
AddClassPostConstruct("widgets/controls", function(self)
    local old_oncontrol = self.OnControl
    self.OnControl = function(self, control, down)
        if old_oncontrol then
            old_oncontrol(self, control, down)
        end

        -- Open cultivation UI with C key
        if control == CONTROL_INVENTORY and down and TheInput:IsKeyDown(KEY_C) then
            local player = ThePlayer
            if player and player.components.cultivation_ui then
                player.components.cultivation_ui:ToggleUI()
                return true
            end
        end

        return false
    end
end)

-- Add cultivation UI to player HUD (placeholder for now)
-- AddClassPostConstruct("screens/playerhud", function(PlayerHud)
--     -- Add cultivation status display
--     function PlayerHud:ShowCultivationUI()
--         if self.cultivation_ui == nil then
--             local CultivationUI = require("screens/cultivation_ui")
--             self.cultivation_ui = CultivationUI(self.owner)
--             self:OpenScreenUnderPause(self.cultivation_ui)
--         end
--         return self.cultivation_ui
--     end
--
--     function PlayerHud:CloseCultivationUI()
--         if self.cultivation_ui ~= nil then
--             if self.cultivation_ui.inst:IsValid() then
--                 TheFrontEnd:PopScreen(self.cultivation_ui)
--             end
--             self.cultivation_ui = nil
--         end
--     end
-- end)

-- Add key bindings for cultivation UI (placeholder)
-- TheInput:AddKeyUpHandler(GLOBAL.KEY_U, function()
--     if ThePlayer and ThePlayer.HUD then
--         ThePlayer.HUD:ShowCultivationUI()
--     end
-- end)

-- Seasonal events (basic framework)
AddStategraphPostInit("wilson", function(sg)
    -- Add meditation state
    local meditation_state = State{
        name = "cultivation_meditate",
        tags = {"busy", "meditating"},
        
        onenter = function(inst)
            inst.AnimState:PlayAnimation("sit")
            inst.AnimState:PushAnimation("sit_loop", true)
            
            -- Start meditation effects
            if inst.components.cultivation then
                inst.components.cultivation:StartMeditation()
            end
        end,
        
        onexit = function(inst)
            if inst.components.cultivation then
                inst.components.cultivation:StopMeditation()
            end
        end,
        
        events = {
            EventHandler("animover", function(inst)
                if inst.AnimState:AnimDone() then
                    inst.sg:GoToState("idle")
                end
            end),
        },
    }
    
    sg.states["cultivation_meditate"] = meditation_state
end)

-- Add actions for cultivation
AddAction("CULTIVATE_MEDITATE", "Meditate", function(act)
    if act.doer and act.doer.components.cultivation then
        act.doer.sg:GoToState("cultivation_meditate")
        return true
    end
    return false
end)

-- Add strings for localization
GLOBAL.STRINGS.ACTIONS.CULTIVATE_MEDITATE = "Meditate"
GLOBAL.STRINGS.NAMES.CULTIVATION_PORTAL = "Cultivation Portal"
GLOBAL.STRINGS.RECIPE_DESC.CULTIVATION_PORTAL = "A mysterious portal to secret realms"

DebugPrint("Mod loading completed!")

-- Print mod info
print("=== Tu Tiên Bí Cảnh (Cultivation Secret Realms) ===")
print("Version: 1.0.0")
print("Status: Core systems loaded")
print("Config: Realm spawn rate x" .. REALM_SPAWN_RATE)
print("===============================================")
