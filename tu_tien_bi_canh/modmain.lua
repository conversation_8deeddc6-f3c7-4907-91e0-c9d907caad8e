-- <PERSON> (Cultivation Secret Realms) - Main Mod File
-- Version 1.0.0

print("🌟 Tu Tiên B<PERSON> - Cultivation Secret Realms loading...")

-- Set up global environment
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Register prefabs with DST (start with simple test only)
PrefabFiles = {
    "test_portal",
    "cultivation_boss",
    -- "cultivation_portal",  -- Disable complex prefabs for now
    -- "cultivation_items",
    -- "cultivation_npcs",
}

-- Get mod configuration
local LANGUAGE = GetModConfigData("language") or "vietnamese"
local REALM_SPAWN_RATE = GetModConfigData("realm_spawn_rate") or 1.0
local REALM_DURATION = GetModConfigData("realm_duration") or 1.0
local REALM_DIFFICULTY = GetModConfigData("realm_difficulty") or 1.0
local CULTIVATION_SPEED = GetModConfigData("cultivation_speed") or 1.0
local SPIRITUAL_ENERGY_RATE = GetModConfigData("spiritual_energy_rate") or 1.0
local SPELL_POWER = GetModConfigData("spell_power") or 1.0
local ENABLE_SECT_SYSTEM = GetModConfigData("enable_sect_system")
local NPC_AGGRESSION = GetModConfigData("npc_aggression") or 1.0
local ENABLE_PVP_REALMS = GetModConfigData("enable_pvp_realms")
local RESOURCE_ABUNDANCE = GetModConfigData("resource_abundance") or 1.0
local DEBUG_MODE = GetModConfigData("debug_mode") or false

-- Set up tuning values
TUNING.CULTIVATION_MOD = {
    LANGUAGE = LANGUAGE,
    REALM_SPAWN_RATE = REALM_SPAWN_RATE,
    REALM_DURATION = REALM_DURATION,
    REALM_DIFFICULTY = REALM_DIFFICULTY,
    CULTIVATION_SPEED = CULTIVATION_SPEED,
    SPIRITUAL_ENERGY_RATE = SPIRITUAL_ENERGY_RATE,
    SPELL_POWER = SPELL_POWER,
    ENABLE_SECT_SYSTEM = ENABLE_SECT_SYSTEM,
    NPC_AGGRESSION = NPC_AGGRESSION,
    ENABLE_PVP_REALMS = ENABLE_PVP_REALMS,
    RESOURCE_ABUNDANCE = RESOURCE_ABUNDANCE,
    DEBUG_MODE = DEBUG_MODE,
}

-- Set up basic cultivation tuning (required by components)
TUNING.CULTIVATION = TUNING.CULTIVATION or {}
TUNING.CULTIVATION.LEVELS = {
    "qi_refining", "foundation", "golden_core", "nascent_soul",
    "soul_formation", "void_refinement", "body_integration", "great_ascension"
}
TUNING.CULTIVATION.MAX_SPIRITUAL_ENERGY = 100
TUNING.CULTIVATION.MEDITATION_RATE = 1.0
TUNING.CULTIVATION.EXP_MULTIPLIER = 1.0

-- Debug print function
local function DebugPrint(...)
    if DEBUG_MODE then
        print("[Tu Tiên]", ...)
    end
end

-- Welcome message based on language
local function GetWelcomeMessage()
    if LANGUAGE == "english" then
        return "Tu Tien Bi Canh - Cultivation Secret Realms loaded!"
    else
        return "Tu Tiên Bí Cảnh đã tải thành công!"
    end
end

-- Load safe components that won't crash
local function LoadSafeComponents()
    DebugPrint("Loading safe components...")

    local success, error_msg = pcall(function()
        -- Load safe versions of components
        modimport("scripts/cultivation_core/02_components/cultivation_safe.lua")
        modimport("scripts/cultivation_core/02_components/spell_caster_safe.lua")
        modimport("scripts/components/boss_ui_manager.lua")

        -- Try to load other components safely
        local optional_components = {
            "scripts/cultivation_core/02_components/realm_status.lua",
            "scripts/cultivation_core/02_components/quest_manager.lua",
            "scripts/cultivation_core/02_components/sect_member.lua",
            "scripts/cultivation_core/02_components/realm_manager.lua"
        }

        for _, component_path in ipairs(optional_components) do
            local comp_success, comp_error = pcall(function()
                modimport(component_path)
            end)
            if comp_success then
                DebugPrint("✓ Loaded optional component:", component_path)
            else
                DebugPrint("⚠ Could not load optional component:", component_path, comp_error)
            end
        end
    end)

    if success then
        DebugPrint("✓ Safe components loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load safe components:", error_msg)
    end
end

-- Load prefabs safely
local function LoadPrefabs()
    DebugPrint("Loading prefabs...")

    local success, error_msg = pcall(function()
        modimport("scripts/prefabs/test_portal.lua")
        modimport("scripts/prefabs/cultivation_boss.lua")
        -- Only load test portal and boss for now to avoid asset errors
        -- modimport("scripts/prefabs/cultivation_portal.lua")
        -- modimport("scripts/prefabs/cultivation_items.lua")
        -- modimport("scripts/prefabs/cultivation_npcs.lua")
    end)

    if success then
        DebugPrint("✓ Prefabs loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load all prefabs:", error_msg)
    end
end

-- Load localization
local function LoadLocalization()
    DebugPrint("Loading localization for language:", LANGUAGE)

    local success, error_msg = pcall(function()
        modimport("scripts/localization/localization.lua")
    end)

    if success then
        DebugPrint("✓ Localization loaded successfully")
    else
        print("[Tu Tiên] Warning: Could not load localization:", error_msg)
    end
end

-- Initialize mod
local function InitializeMod()
    DebugPrint("Initializing Tu Tiên Bí Cảnh mod...")
    DebugPrint("Configuration:")
    DebugPrint("- Language:", LANGUAGE)
    DebugPrint("- Realm Spawn Rate:", REALM_SPAWN_RATE)
    DebugPrint("- Cultivation Speed:", CULTIVATION_SPEED)
    DebugPrint("- Debug Mode:", DEBUG_MODE)

    -- Load systems
    LoadLocalization()
    LoadSafeComponents()
    LoadPrefabs()

    DebugPrint("✓ Mod initialization complete")
end

-- Player initialization
AddPlayerPostInit(function(inst)
    DebugPrint("Player spawned, adding cultivation components...")

    local success, error_msg = pcall(function()
        -- Add safe core components
        if not inst.components.cultivation then
            local cultivation_comp = require("scripts/cultivation_core/02_components/cultivation_safe")
            inst:AddComponent("cultivation")
            inst.components.cultivation = cultivation_comp(inst)
        end

        if not inst.components.spell_caster then
            local spell_comp = require("scripts/cultivation_core/02_components/spell_caster_safe")
            inst:AddComponent("spell_caster")
            inst.components.spell_caster = spell_comp(inst)
        end

        -- Add optional components safely
        local optional_components = {
            {name = "quest_manager", enabled = true},
            {name = "sect_member", enabled = ENABLE_SECT_SYSTEM},
            {name = "realm_status", enabled = true},
            {name = "boss_ui_manager", enabled = true}
        }

        for _, comp_data in ipairs(optional_components) do
            if comp_data.enabled and not inst.components[comp_data.name] then
                local comp_success, comp_error = pcall(function()
                    inst:AddComponent(comp_data.name)
                end)
                if comp_success then
                    DebugPrint("✓ Added component:", comp_data.name)
                else
                    DebugPrint("⚠ Could not add component:", comp_data.name, comp_error)
                end
            end
        end
    end)

    if success then
        DebugPrint("✓ Player components added successfully")

        -- Show welcome message
        inst:DoTaskInTime(3, function()
            if inst.components.talker then
                local message = GetWelcomeMessage()
                inst.components.talker:Say(message, 4)
            end
        end)
    else
        print("[Tu Tiên] Warning: Could not add all player components:", error_msg)
    end
end)

-- World initialization
AddPrefabPostInit("world", function(inst)
    DebugPrint("World spawned, adding realm manager...")

    local success, error_msg = pcall(function()
        if not inst.components.realm_manager then
            inst:AddComponent("realm_manager")
        end
    end)

    if success then
        DebugPrint("✓ World components added successfully")
    else
        print("[Tu Tiên] Warning: Could not add world components:", error_msg)
    end
end)

-- Console commands for testing
GLOBAL.TuTienTest = function()
    print("=== Tu Tiên Bí Cảnh Test ===")
    print("Language:", LANGUAGE)
    print("Realm Spawn Rate:", REALM_SPAWN_RATE)
    print("Cultivation Speed:", CULTIVATION_SPEED)
    print("Debug Mode:", DEBUG_MODE)

    local player = ThePlayer
    if player then
        print("✓ Player found")

        -- Test components
        local components = {"cultivation", "spell_caster", "quest_manager", "realm_status"}
        for _, comp in ipairs(components) do
            if player.components[comp] then
                print("✓", comp, "component working")

                -- Test component functions
                if comp == "cultivation" and player.components.cultivation.GetLevelName then
                    print("  Level:", player.components.cultivation:GetLevelName())
                    print("  Experience:", player.components.cultivation.experience)
                    print("  Spiritual Energy:", player.components.cultivation.spiritual_energy)
                elseif comp == "spell_caster" and player.components.spell_caster.GetKnownSpells then
                    local spells = player.components.spell_caster:GetKnownSpells()
                    local spell_names = {}
                    for spell_id, _ in pairs(spells) do
                        table.insert(spell_names, spell_id)
                    end
                    print("  Known spells:", table.concat(spell_names, ", "))
                end
            else
                print("✗", comp, "component missing")
            end
        end

        -- Test spawn portal
        if player.components.cultivation then
            local pos = player:GetPosition()
            local portal = SpawnPrefab("cultivation_portal")
            if portal then
                portal.Transform:SetPosition(pos.x + 5, 0, pos.z + 5)
                if portal.components.cultivation_portal then
                    portal.components.cultivation_portal:SetRealmType("peach_blossom")
                    portal.components.cultivation_portal:Activate()
                end
                print("✓ Test portal spawned")
            else
                print("✗ Could not spawn portal")
            end
        end
    else
        print("✗ No player found")
    end

    print("=== Test Complete ===")
end

-- Quick realm test
GLOBAL.TuTienQuickTest = function()
    if ThePlayer then
        local pos = ThePlayer:GetPosition()

        -- Try to spawn portal
        local portal = SpawnPrefab("cultivation_portal")
        if portal then
            portal.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)
            if portal.components.cultivation_portal then
                portal.components.cultivation_portal:SetRealmType("peach_blossom")
                portal.components.cultivation_portal:Activate()
                print("✓ Quick test portal spawned - right-click to enter!")
            else
                print("✗ Portal spawned but missing cultivation_portal component")
            end
        else
            print("✗ Could not spawn cultivation_portal prefab")
            print("Available prefabs:")

            -- Try alternative - spawn a simple test object
            local test_obj = SpawnPrefab("campfire")
            if test_obj then
                test_obj.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)
                print("✓ Test object (campfire) spawned instead")
                print("This means SpawnPrefab works, but cultivation_portal prefab is not registered")
            end
        end
    else
        print("✗ No player found")
    end
end

-- Simple test without prefabs
GLOBAL.TuTienSimpleTest = function()
    print("=== Simple Tu Tien Test ===")

    if ThePlayer then
        print("✓ Player found")

        -- Test components
        if ThePlayer.components.cultivation then
            print("✓ Cultivation component exists")
            print("  Level:", ThePlayer.components.cultivation.level)
            print("  Experience:", ThePlayer.components.cultivation.experience)
        else
            print("✗ Cultivation component missing")
        end

        if ThePlayer.components.spell_caster then
            print("✓ Spell caster component exists")
        else
            print("✗ Spell caster component missing")
        end

        -- Test basic game functions
        local pos = ThePlayer:GetPosition()
        print("✓ Player position:", pos.x, pos.z)

        -- Test spawning basic items
        local item = SpawnPrefab("log")
        if item then
            item.Transform:SetPosition(pos.x + 2, 0, pos.z + 2)
            print("✓ Basic spawning works (log spawned)")
        end

    else
        print("✗ No player found")
    end

    print("=== Simple Test Complete ===")
end

-- Safe portal test with error handling
GLOBAL.TuTienTestPortal = function()
    print("=== Safe Portal Test ===")

    if not ThePlayer then
        print("✗ No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    if not pos then
        print("✗ Could not get player position")
        return
    end

    print("Player position:", pos.x, pos.z)

    -- Test 1: Try to spawn test_portal with error handling
    print("Test 1: Trying to spawn test_portal...")
    local success1, result1 = pcall(function()
        return SpawnPrefab("test_portal")
    end)

    if success1 and result1 then
        local success_pos, error_pos = pcall(function()
            result1.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)
        end)

        if success_pos then
            print("✓ Test portal spawned successfully at", pos.x + 3, pos.z + 3)
            print("Right-click the blue glowing campfire to test interaction")
        else
            print("✗ Could not position test portal:", error_pos)
        end
    else
        print("✗ Could not spawn test_portal:", result1)
        print("This means the prefab is not registered or has errors")
    end

    -- Test 2: Try basic object spawn as fallback
    print("Test 2: Trying to spawn basic campfire as fallback...")
    local success2, result2 = pcall(function()
        return SpawnPrefab("campfire")
    end)

    if success2 and result2 then
        result2.Transform:SetPosition(pos.x - 3, 0, pos.z + 3)
        -- Make it blue to distinguish from normal campfire
        if result2.AnimState then
            result2.AnimState:SetMultColour(1.0, 0.5, 1.0, 1.0) -- Purple glow
        end
        print("✓ Fallback campfire spawned (purple glow)")
    else
        print("✗ Even basic spawning failed:", result2)
        print("This indicates a serious game state issue")
    end

    -- Test 3: Check prefab registration
    print("Test 3: Checking prefab registration...")
    if PrefabFiles then
        print("PrefabFiles contains:", table.concat(PrefabFiles, ", "))
    else
        print("✗ PrefabFiles is nil")
    end

    -- Test 4: Check if we can access Prefabs table
    local prefab_count = 0
    if Prefabs then
        for name, _ in pairs(Prefabs) do
            prefab_count = prefab_count + 1
            if string.find(name, "portal") or string.find(name, "test") then
                print("Found prefab:", name)
            end
        end
        print("Total prefabs registered:", prefab_count)
    else
        print("✗ Prefabs table is nil")
    end

    print("=== Safe Portal Test Complete ===")
end

-- Ultra minimal portal test
GLOBAL.TuTienMinimalPortalTest = function()
    print("=== Minimal Portal Test ===")

    if not ThePlayer then
        print("✗ No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    print("✓ Player position:", pos.x, pos.z)

    -- Just try to spawn a basic campfire first
    print("Testing basic spawn...")
    local campfire = SpawnPrefab("campfire")
    if campfire then
        campfire.Transform:SetPosition(pos.x + 2, 0, pos.z + 2)
        print("✓ Basic campfire spawned - SpawnPrefab works")

        -- Now try our test portal
        print("Testing test_portal...")
        local portal = SpawnPrefab("test_portal")
        if portal then
            portal.Transform:SetPosition(pos.x + 4, 0, pos.z + 4)
            print("✓ Test portal spawned successfully!")
        else
            print("✗ Test portal failed to spawn")
            print("Prefab 'test_portal' is not registered")
        end
    else
        print("✗ Even basic campfire failed - serious issue")
    end

    print("=== Minimal Portal Test Complete ===")
end

-- Create fake portal without prefab system
GLOBAL.TuTienFakePortalTest = function()
    print("=== Fake Portal Test (No Prefabs) ===")

    if not ThePlayer then
        print("✗ No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    print("✓ Player position:", pos.x, pos.z)

    -- Create a "portal" using existing objects
    print("Creating fake portal using campfire...")

    local portal = SpawnPrefab("campfire")
    if portal then
        portal.Transform:SetPosition(pos.x + 3, 0, pos.z + 3)

        -- Make it look mystical
        if portal.AnimState then
            portal.AnimState:SetMultColour(0.3, 0.8, 1.0, 1.0) -- Blue mystical glow
        end

        -- Add portal functionality
        if portal.components.inspectable then
            portal.components.inspectable:SetDescription("A mystical portal to cultivation realms!")
        end

        -- Add activation
        if not portal.components.activatable then
            portal:AddComponent("activatable")
        end

        portal.components.activatable.OnActivate = function(inst, doer)
            if doer and doer.components.talker then
                doer.components.talker:Say("Entering mystical realm...", 3)
            end

            -- Simulate realm entry
            TuTienEnterFakeRealm(doer)
        end
        portal.components.activatable:SetText("Enter Portal")

        print("✓ Fake portal created successfully!")
        print("Right-click the blue glowing campfire to 'enter realm'")

        return portal
    else
        print("✗ Could not create fake portal")
        return nil
    end
end

-- Simulate entering a realm
GLOBAL.TuTienEnterFakeRealm = function(player)
    if not player then
        player = ThePlayer
    end

    if not player then
        print("✗ No player to enter realm")
        return
    end

    print("=== Entering Fake Realm ===")

    -- Show entry message
    if player.components.talker then
        player.components.talker:Say("Entered Peach Blossom Realm!", 3)
    end

    -- Spawn realm resources around player
    local pos = player:GetPosition()
    local realm_objects = {
        {prefab = "flower", name = "Peach Blossom", offset = {2, 1}, color = {1.0, 0.7, 0.8}},
        {prefab = "flower", name = "Spirit Flower", offset = {-2, 1}, color = {0.8, 0.8, 1.0}},
        {prefab = "flower", name = "Mystic Bloom", offset = {1, -2}, color = {0.9, 1.0, 0.7}},
        {prefab = "berrybush", name = "Spirit Berry", offset = {-1, 2}, color = {0.7, 0.9, 1.0}},
        {prefab = "rock1", name = "Spirit Stone", offset = {3, 0}, color = {0.6, 0.8, 1.0}},
    }

    for _, obj_data in ipairs(realm_objects) do
        local obj = SpawnPrefab(obj_data.prefab)
        if obj then
            obj.Transform:SetPosition(pos.x + obj_data.offset[1], 0, pos.z + obj_data.offset[2])

            -- Add mystical glow
            if obj.AnimState and obj_data.color then
                obj.AnimState:SetMultColour(obj_data.color[1], obj_data.color[2], obj_data.color[3], 1.0)
            end

            -- Add custom description
            if obj.components.inspectable then
                obj.components.inspectable:SetDescription("A mystical " .. obj_data.name .. " from the cultivation realm!")
            end

            print("✓ Spawned", obj_data.name)
        end
    end

    -- Give player cultivation benefits
    if player.components.cultivation then
        player.components.cultivation:AddExperience(25)
        player.components.cultivation:AddSpiritualEnergy(20)
        print("✓ Player gained 25 experience and 20 spiritual energy")
    end

    -- Start auto-meditation in realm
    if player.components.cultivation and not player.components.cultivation.meditation_active then
        player.components.cultivation:StartMeditation()
        print("✓ Auto-meditation started in realm")

        -- Stop meditation after 30 seconds
        player:DoTaskInTime(30, function()
            if player.components.cultivation and player.components.cultivation.meditation_active then
                player.components.cultivation:StopMeditation()
                if player.components.talker then
                    player.components.talker:Say("Realm meditation complete.", 2)
                end
            end
        end)
    end

    print("=== Realm Entry Complete ===")
    print("Look around - you should see colorful mystical objects!")
end

-- Boss spawn and test functions
GLOBAL.TuTienSpawnBoss = function()
    print("=== Spawning Cultivation Boss ===")

    if not ThePlayer then
        print("✗ No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    print("✓ Player position:", pos.x, pos.z)

    -- Spawn boss at safe distance
    local boss_x = pos.x + 10
    local boss_z = pos.z + 10

    print("Attempting to spawn cultivation_boss...")
    local success, boss = pcall(function()
        return SpawnPrefab("cultivation_boss")
    end)

    if success and boss then
        boss.Transform:SetPosition(boss_x, 0, boss_z)
        print("✓ Ancient Cultivation Guardian spawned at", boss_x, boss_z)
        print("Boss Stats:")
        print("  Health:", boss.components.health and boss.components.health.maxhealth or "Unknown")
        print("  Damage:", boss.components.combat and boss.components.combat.defaultdamage or "Unknown")
        print("  Spiritual Energy:", boss.boss_brain and boss.boss_brain.spiritual_energy or "Unknown")

        -- Show warning message
        if ThePlayer.components.talker then
            ThePlayer.components.talker:Say("An ancient guardian has awakened!", 4)
        end

        -- Create warning area around boss
        for i = 1, 8 do
            local angle = (i / 8) * 2 * math.pi
            local radius = 6
            local marker_x = boss_x + math.cos(angle) * radius
            local marker_z = boss_z + math.sin(angle) * radius

            local marker = SpawnPrefab("campfire")
            if marker then
                marker.Transform:SetPosition(marker_x, 0, marker_z)
                if marker.AnimState then
                    marker.AnimState:SetMultColour(1.0, 0.3, 0.3, 1.0) -- Red warning
                end

                -- Remove markers after 30 seconds
                marker:DoTaskInTime(30, function()
                    if marker and marker:IsValid() then
                        marker:Remove()
                    end
                end)
            end
        end

        print("✓ Warning perimeter created")
        print("⚠️  DANGER: Approach with caution!")
        return boss
    else
        print("✗ Could not spawn cultivation_boss:", boss)
        print("Trying fallback boss (using deerclops)...")

        -- Fallback: spawn deerclops as boss
        local fallback_boss = SpawnPrefab("deerclops")
        if fallback_boss then
            fallback_boss.Transform:SetPosition(boss_x, 0, boss_z)
            if fallback_boss.AnimState then
                fallback_boss.AnimState:SetMultColour(0.8, 0.9, 1.0, 1.0) -- Blue tint
            end
            print("✓ Fallback boss (deerclops) spawned with mystical appearance")
            return fallback_boss
        else
            print("✗ Even fallback boss failed to spawn")
            return nil
        end
    end
end

-- Test boss abilities
GLOBAL.TuTienTestBoss = function()
    print("=== Testing Boss Abilities ===")

    -- First try to find existing boss
    local pos = ThePlayer:GetPosition()
    local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 20, {"cultivation_boss"})

    local boss = nil
    if #bosses > 0 then
        boss = bosses[1]
        print("✓ Found existing boss")
    else
        print("No boss found, spawning new one...")
        boss = TuTienSpawnBoss()
    end

    if not boss then
        print("✗ No boss available for testing")
        return
    end

    print("Testing boss abilities...")

    -- Test boss brain
    if boss.boss_brain then
        print("✓ Boss brain exists")
        print("  Spiritual Energy:", boss.boss_brain.spiritual_energy)
        print("  Available spells:")

        for spell_id, spell_data in pairs(boss.boss_brain.last_spell_time) do
            local can_cast = boss.boss_brain:CanCastSpell(spell_id)
            print(string.format("    %s: %s", spell_id, can_cast and "Ready" or "On cooldown"))
        end

        -- Force cast a spell for testing
        print("Forcing boss to cast random spell...")
        boss.boss_brain:CastRandomSpell()

    else
        print("✗ Boss brain missing")
    end

    -- Test boss combat
    if boss.components.combat then
        print("✓ Boss combat component exists")
        print("  Target:", boss.components.combat.target and boss.components.combat.target.name or "None")

        -- Make boss target player for testing
        boss.components.combat:SetTarget(ThePlayer)
        print("✓ Boss now targeting player")
    else
        print("✗ Boss combat component missing")
    end

    print("=== Boss Test Complete ===")
    print("Boss should now be active and may cast spells!")
end

-- Quick boss fight setup
GLOBAL.TuTienBossFight = function()
    print("=== Setting Up Boss Fight ===")

    -- Spawn boss
    local boss = TuTienSpawnBoss()
    if not boss then
        print("✗ Could not set up boss fight")
        return
    end

    -- Give player some preparation
    if ThePlayer.components.cultivation then
        ThePlayer.components.cultivation.spiritual_energy = ThePlayer.components.cultivation.max_spiritual_energy
        ThePlayer.components.cultivation:AddExperience(50)
        print("✓ Player spiritual energy restored and experience boosted")
    end

    if ThePlayer.components.health then
        ThePlayer.components.health:SetPercent(1.0)
        print("✓ Player health restored")
    end

    -- Start the fight
    if boss.components.combat then
        boss.components.combat:SetTarget(ThePlayer)
        print("✓ Boss fight initiated!")

        if ThePlayer.components.talker then
            ThePlayer.components.talker:Say("The ancient guardian challenges me!", 3)
        end
    end

    print("=== Boss Fight Ready ===")
    print("Good luck, cultivator!")
end

-- Test Boss UI
GLOBAL.TuTienTestBossUI = function()
    print("=== Testing Boss UI ===")

    if not ThePlayer then
        print("✗ No player found")
        return
    end

    -- Check if player has boss UI manager
    if ThePlayer.components.boss_ui_manager then
        print("✓ Boss UI Manager component exists")

        -- Check for nearby boss
        local pos = ThePlayer:GetPosition()
        local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"cultivation_boss"})

        if #bosses > 0 then
            local boss = bosses[1]
            print("✓ Found boss:", boss.name or "Unknown")

            -- Force UI to show boss
            ThePlayer.components.boss_ui_manager:SetActiveBoss(boss)
            print("✓ Boss UI should now be visible")

            -- Test boss stats
            if boss.components.health then
                print("  Boss Health:", boss.components.health.currenthealth, "/", boss.components.health.maxhealth)
            end

            if boss.boss_brain then
                print("  Boss Spiritual Energy:", boss.boss_brain.spiritual_energy)
                print("  Boss Phase:", boss.boss_brain.current_phase)
                print("  Boss Enrage:", boss.boss_brain.enrage_mode and "Yes" or "No")
            end

        else
            print("✗ No boss found nearby")
            print("Spawning boss for UI test...")
            local boss = TuTienSpawnBoss()
            if boss then
                print("✓ Boss spawned, UI should appear automatically")
            end
        end

    else
        print("✗ Boss UI Manager component missing")
        print("Adding component manually...")

        local success, error_msg = pcall(function()
            local BossUIManager = require("scripts/components/boss_ui_manager")
            ThePlayer:AddComponent("boss_ui_manager")
            ThePlayer.components.boss_ui_manager = BossUIManager(ThePlayer)
        end)

        if success then
            print("✓ Boss UI Manager added manually")
        else
            print("✗ Could not add Boss UI Manager:", error_msg)
        end
    end

    print("=== Boss UI Test Complete ===")
end

-- Toggle Boss UI
GLOBAL.TuTienToggleBossUI = function()
    if not ThePlayer or not ThePlayer.components.boss_ui_manager then
        print("✗ No boss UI manager available")
        return
    end

    local ui_manager = ThePlayer.components.boss_ui_manager

    if ui_manager.active_boss then
        ui_manager:SetActiveBoss(nil)
        print("✓ Boss UI hidden")
    else
        -- Find a boss to show
        local pos = ThePlayer:GetPosition()
        local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 50, {"cultivation_boss"})

        if #bosses > 0 then
            ui_manager:SetActiveBoss(bosses[1])
            print("✓ Boss UI shown")
        else
            print("✗ No boss found to show UI for")
        end
    end
end

-- Test realm system without custom prefabs
GLOBAL.TuTienRealmTest = function()
    print("=== Realm System Test (No Custom Prefabs) ===")

    if ThePlayer then
        local pos = ThePlayer:GetPosition()

        -- Test 1: Create a fake "realm" using existing objects
        print("Creating fake realm with existing objects...")

        -- Spawn some "mystical" objects using existing prefabs
        local objects = {
            {prefab = "flower", name = "Mystical Flower", offset = {2, 0}},
            {prefab = "berrybush", name = "Spirit Berry", offset = {-2, 2}},
            {prefab = "sapling", name = "Cultivation Tree", offset = {0, 3}},
            {prefab = "rock1", name = "Spirit Stone", offset = {3, -1}},
        }

        for _, obj_data in ipairs(objects) do
            local obj = SpawnPrefab(obj_data.prefab)
            if obj then
                obj.Transform:SetPosition(pos.x + obj_data.offset[1], 0, pos.z + obj_data.offset[2])

                -- Add mystical glow effect
                if obj.AnimState then
                    obj.AnimState:SetMultColour(0.7, 0.9, 1.2, 1.0) -- Blue mystical tint
                end

                -- Add custom description
                if obj.components.inspectable then
                    obj.components.inspectable:SetDescription("A mystical " .. obj_data.name .. " from the cultivation realm!")
                end

                print("✓ Spawned", obj_data.name, "at", obj_data.offset[1], obj_data.offset[2])
            end
        end

        -- Test 2: Test player cultivation components
        print("Testing cultivation components...")
        if ThePlayer.components.cultivation then
            print("✓ Cultivation component exists")

            -- Test meditation
            print("Testing meditation...")
            ThePlayer.components.cultivation:StartMeditation()
            print("✓ Meditation started")

            -- Test experience gain
            local old_exp = ThePlayer.components.cultivation.experience
            ThePlayer.components.cultivation:AddExperience(50)
            local new_exp = ThePlayer.components.cultivation.experience
            print("✓ Experience gained:", new_exp - old_exp)

            -- Test level info
            print("Current level:", ThePlayer.components.cultivation:GetLevelName())
            print("Current experience:", new_exp)

        else
            print("✗ Cultivation component missing")
        end

        -- Test 3: Test spell system
        if ThePlayer.components.spell_caster then
            print("✓ Spell caster component exists")

            -- Give some spiritual energy for testing
            if ThePlayer.components.cultivation then
                ThePlayer.components.cultivation.spiritual_energy = 100
                print("✓ Set spiritual energy to 100 for testing")
            end

            -- Test spell casting (this might not work without spell prefabs, but we can try)
            print("Testing spell casting...")
            local success = pcall(function()
                ThePlayer.components.spell_caster:CastSpell("metal_spike")
            end)

            if success then
                print("✓ Spell casting works")
            else
                print("⚠ Spell casting needs spell prefabs (expected)")
            end

        else
            print("✗ Spell caster component missing")
        end

        -- Test 4: Show player a "realm entered" message
        if ThePlayer.components.talker then
            ThePlayer.components.talker:Say("Entered mystical test realm!", 3)
            print("✓ Realm entry message displayed")
        end

        print("✓ Fake realm created successfully!")
        print("Look around - you should see mystical glowing objects!")

    else
        print("✗ No player found")
    end

    print("=== Realm System Test Complete ===")
end

-- Hot reload function for development
GLOBAL.TuTienReload = function()
    print("🔄 Reloading Tu Tien mod...")

    -- Reload core functions
    local success, error_msg = pcall(function()
        InitializeMod()

        -- Re-add components to current player if exists
        if ThePlayer then
            if not ThePlayer.components.cultivation then
                ThePlayer:AddComponent("cultivation")
            end
            if not ThePlayer.components.spell_caster then
                ThePlayer:AddComponent("spell_caster")
            end
            if not ThePlayer.components.realm_status then
                ThePlayer:AddComponent("realm_status")
            end

            print("✓ Player components refreshed")
        end

        print("✓ Mod reloaded successfully!")
    end)

    if not success then
        print("✗ Reload failed:", error_msg)
    end
end

-- Quick development test
GLOBAL.TuTienDevTest = function()
    print("🧪 Development Test Mode")

    -- Auto reload
    TuTienReload()

    -- Run tests
    TuTienSimpleTest()
    TuTienRealmTest()

    print("🎯 Development test complete!")
end

-- Initialize the mod
InitializeMod()

print("🌟 Tu Tiên Bí Cảnh - Cultivation Secret Realms loaded successfully!")
print("🔧 Development commands available:")
print("   TuTienReload() - Hot reload mod")
print("   TuTienDevTest() - Full development test")
print("🐉 Boss commands:")
print("   TuTienSpawnBoss() - Spawn cultivation boss")
print("   TuTienTestBoss() - Test boss abilities")
print("   TuTienBossFight() - Setup boss fight")
print("🎨 Boss UI commands:")
print("   TuTienTestBossUI() - Test boss UI system")
print("   TuTienToggleBossUI() - Toggle boss UI on/off")
