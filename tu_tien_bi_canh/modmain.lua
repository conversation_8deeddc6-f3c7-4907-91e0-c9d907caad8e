-- Tu <PERSON>ien Boss Fight Mod - Clean Implementation
print("Loading Tu Tien Boss Fight Mod...")

-- Global environment setup
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Get mod configuration
local BOSS_DIFFICULTY = GetModConfigData("boss_difficulty") or 1.0
local DEBUG_MODE = GetModConfigData("debug_mode") or false

-- Debug print function
local function DebugPrint(...)
    if DEBUG_MODE then
        print("[Tu Tien Debug]", ...)
    end
end

-- Register prefabs (all versions)
PrefabFiles = {
    "cultivation_boss",
    "cultivation_boss_simple",
    "cultivation_boss_bulletproof"
}

-- Load components (simplified)
local function LoadComponents()
    DebugPrint("Loading components...")
    local success, error_msg = pcall(function()
        modimport("scripts/components/boss_ui_manager.lua")
        -- Note: cultivation_boss_brain removed - using simple data instead
    end)

    if success then
        DebugPrint("✓ Components loaded")
    else
        print("[Tu Tien] Error loading components:", error_msg)
    end
end

-- Add UI manager to players
AddPlayerPostInit(function(inst)
    DebugPrint("Adding boss UI manager to player")

    local success, error_msg = pcall(function()
        local BossUIManager = require("scripts/components/boss_ui_manager")
        inst:AddComponent("boss_ui_manager")
        inst.components.boss_ui_manager = BossUIManager(inst)
    end)

    if success then
        DebugPrint("✓ Boss UI manager added to player")
    else
        print("[Tu Tien] Error adding UI manager:", error_msg)
    end
end)

-- Initialize mod
local function InitializeMod()
    DebugPrint("Initializing mod...")
    LoadComponents()
end

-- Console commands
GLOBAL.SpawnCultivationBoss = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    local boss = SpawnPrefab("cultivation_boss")

    if boss then
        boss.Transform:SetPosition(pos.x + 10, 0, pos.z + 10)

        -- Apply difficulty scaling
        if boss.components.health then
            local new_health = boss.components.health.maxhealth * BOSS_DIFFICULTY
            boss.components.health:SetMaxHealth(new_health)
            boss.components.health:SetCurrentHealth(new_health)
        end

        if boss.components.combat then
            boss.components.combat.defaultdamage = boss.components.combat.defaultdamage * BOSS_DIFFICULTY
        end

        print("Cultivation Boss spawned! Difficulty:", BOSS_DIFFICULTY)
        return boss
    else
        print("Failed to spawn boss")
        return nil
    end
end

-- Debug boss command
GLOBAL.DebugBoss = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"cultivation_boss"})

    if #bosses == 0 then
        print("No boss found")
        return
    end

    local boss = bosses[1]
    print("=== 古修者 Debug Info ===")
    print("Boss valid:", boss:IsValid())
    print("Boss position:", boss:GetPosition())

    if boss.components.health then
        print("Health:", boss.components.health.currenthealth, "/", boss.components.health.maxhealth)
    else
        print("✗ No health component")
    end

    if boss.components.combat then
        print("Combat damage:", boss.components.combat.defaultdamage)
        print("Combat target:", boss.components.combat.target and boss.components.combat.target.name or "None")
    else
        print("✗ No combat component")
    end

    if boss.cultivation_data then
        print("=== Cultivation Data ===")
        print("Realm:", boss.cultivation_data.realm)
        print("Phase:", boss.cultivation_data.phase)
        print("Spiritual Energy:", boss.cultivation_data.spiritual_energy, "/", boss.cultivation_data.max_spiritual_energy)

        print("=== Abilities ===")
        local current_time = GetTime()
        for name, ability in pairs(boss.cultivation_data.abilities) do
            local cooldown_left = math.max(0, ability.cooldown - (current_time - ability.last_used))
            local status = cooldown_left > 0 and string.format("%.1fs", cooldown_left) or "Ready"
            print(string.format("%s: %s", name, status))
        end
    else
        print("✗ No cultivation data")
    end

    print("=== End Debug ===")
end

-- Safe spawn command
GLOBAL.SpawnSimpleBoss = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    local boss = SpawnPrefab("cultivation_boss_simple")

    if boss then
        boss.Transform:SetPosition(pos.x + 10, 0, pos.z + 10)

        -- Apply difficulty scaling safely
        if boss.components.health then
            local new_health = boss.components.health.maxhealth * BOSS_DIFFICULTY
            boss.components.health:SetMaxHealth(new_health)
            boss.components.health:SetCurrentHealth(new_health)
        end

        if boss.components.combat then
            boss.components.combat.defaultdamage = boss.components.combat.defaultdamage * BOSS_DIFFICULTY
        end

        print("Simple Cultivation Boss spawned! Difficulty:", BOSS_DIFFICULTY)
        return boss
    else
        print("Failed to spawn simple boss")
        return nil
    end
end

-- BULLETPROOF spawn command - GUARANTEED TO WORK
GLOBAL.SpawnBulletproofBoss = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    local boss = SpawnPrefab("cultivation_boss_bulletproof")

    if boss then
        boss.Transform:SetPosition(pos.x + 10, 0, pos.z + 10)

        -- Safe difficulty scaling
        local success = pcall(function()
            if boss.components and boss.components.health then
                local new_health = boss.components.health.maxhealth * BOSS_DIFFICULTY
                boss.components.health:SetMaxHealth(new_health)
                boss.components.health:SetCurrentHealth(new_health)
            end

            if boss.components and boss.components.combat then
                boss.components.combat.defaultdamage = boss.components.combat.defaultdamage * BOSS_DIFFICULTY
            end
        end)

        if success then
            print("✅ BULLETPROOF Cultivation Boss spawned! Difficulty:", BOSS_DIFFICULTY)
        else
            print("⚠️ Boss spawned but scaling failed - using defaults")
        end
        return boss
    else
        print("❌ Failed to spawn bulletproof boss")
        return nil
    end
end

-- Force boss to attack player
GLOBAL.BossAttackMe = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"cultivation_boss"})

    if #bosses == 0 then
        print("No boss found")
        return
    end

    local boss = bosses[1]
    if boss.components.combat then
        boss.components.combat:SetTarget(ThePlayer)
        print("Boss now targeting player!")

        -- Wake up if sleeping
        if boss.components.sleeper and boss.components.sleeper:IsAsleep() then
            boss.components.sleeper:WakeUp()
            print("Boss woken up!")
        end
    else
        print("Boss has no combat component")
    end
end

-- Test boss abilities
GLOBAL.TestBossAbility = function(ability_name)
    if not ThePlayer then
        print("No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"cultivation_boss"})

    if #bosses == 0 then
        print("No boss found")
        return
    end

    local boss = bosses[1]
    if boss.cultivation_data then
        if ability_name then
            if ability_name == "thunder_strike" then
                boss:CastThunderStrike()
                print("✓ Boss used ability:", ability_name)
            elseif ability_name == "vajra_protection" then
                boss:CastVajraProtection()
                print("✓ Boss used ability:", ability_name)
            else
                print("✗ Unknown ability:", ability_name)
            end
        else
            print("Available abilities:")
            for name, _ in pairs(boss.cultivation_data.abilities) do
                print("-", name)
            end
            print("Usage: TestBossAbility('thunder_strike') or TestBossAbility('vajra_protection')")
        end
    else
        print("Boss has no cultivation data")
    end
end

-- Test UI command
GLOBAL.TestBossUI = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    if ThePlayer.components.boss_ui_manager then
        print("✓ Boss UI Manager exists")

        -- Find nearby boss
        local pos = ThePlayer:GetPosition()
        local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"cultivation_boss"})

        if #bosses > 0 then
            print("✓ Found boss, UI should be visible")
        else
            print("No boss found, spawning one...")
            SpawnCultivationBoss()
        end
    else
        print("✗ Boss UI Manager missing")
    end
end

-- Initialize
InitializeMod()

print("Tu Tien Boss Fight Mod loaded successfully!")