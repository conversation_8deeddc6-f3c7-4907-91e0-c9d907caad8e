-- Tu <PERSON>ien Boss Fight Mod - Clean Implementation
print("Loading Tu Tien Boss Fight Mod...")

-- Global environment setup
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Get mod configuration
local BOSS_DIFFICULTY = GetModConfigData("boss_difficulty") or 1.0
local DEBUG_MODE = GetModConfigData("debug_mode") or false

-- Debug print function
local function DebugPrint(...)
    if DEBUG_MODE then
        print("[Tu Tien Debug]", ...)
    end
end

-- Register prefabs
PrefabFiles = {
    "cultivation_boss"
}

-- Load UI components
local function LoadUIComponents()
    DebugPrint("Loading UI components...")
    local success, error_msg = pcall(function()
        modimport("scripts/components/boss_ui_manager.lua")
    end)

    if success then
        DebugPrint("✓ UI components loaded")
    else
        print("[<PERSON> Tien] Error loading UI:", error_msg)
    end
end

-- Add UI manager to players
AddPlayerPostInit(function(inst)
    DebugPrint("Adding boss UI manager to player")

    local success, error_msg = pcall(function()
        local BossUIManager = require("scripts/components/boss_ui_manager")
        inst:AddComponent("boss_ui_manager")
        inst.components.boss_ui_manager = BossUIManager(inst)
    end)

    if success then
        DebugPrint("✓ Boss UI manager added to player")
    else
        print("[Tu Tien] Error adding UI manager:", error_msg)
    end
end)

-- Initialize mod
local function InitializeMod()
    DebugPrint("Initializing mod...")
    LoadUIComponents()
end

-- Console commands
GLOBAL.SpawnCultivationBoss = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    local pos = ThePlayer:GetPosition()
    local boss = SpawnPrefab("cultivation_boss")

    if boss then
        boss.Transform:SetPosition(pos.x + 10, 0, pos.z + 10)

        -- Apply difficulty scaling
        if boss.components.health then
            local new_health = boss.components.health.maxhealth * BOSS_DIFFICULTY
            boss.components.health:SetMaxHealth(new_health)
            boss.components.health:SetCurrentHealth(new_health)
        end

        if boss.components.combat then
            boss.components.combat.defaultdamage = boss.components.combat.defaultdamage * BOSS_DIFFICULTY
        end

        print("Cultivation Boss spawned! Difficulty:", BOSS_DIFFICULTY)
        return boss
    else
        print("Failed to spawn boss")
        return nil
    end
end

-- Test UI command
GLOBAL.TestBossUI = function()
    if not ThePlayer then
        print("No player found")
        return
    end

    if ThePlayer.components.boss_ui_manager then
        print("✓ Boss UI Manager exists")

        -- Find nearby boss
        local pos = ThePlayer:GetPosition()
        local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"cultivation_boss"})

        if #bosses > 0 then
            print("✓ Found boss, UI should be visible")
        else
            print("No boss found, spawning one...")
            SpawnCultivationBoss()
        end
    else
        print("✗ Boss UI Manager missing")
    end
end

-- Initialize
InitializeMod()

print("Tu Tien Boss Fight Mod loaded successfully!")