-- Localization System for Tu Tiên B<PERSON>
-- <PERSON>ệ thống đa ngôn ngữ cho mod Tu Tiên B<PERSON>

-- Get language setting from mod configuration
local LANGUAGE = GetModConfigData("language") or "vietnamese"

-- Localization tables
local LOCALIZATION = {
    vietnamese = {
        -- Cultivation levels
        cultivation_levels = {
            [1] = "Luyện Khí Kỳ",
            [2] = "Trúc Cơ Kỳ", 
            [3] = "Kim Đan <PERSON>",
            [4] = "Nguyên Anh Kỳ",
            [5] = "Hóa Thần Kỳ",
            [6] = "<PERSON>yệ<PERSON> Hư Kỳ",
            [7] = "<PERSON>ợ<PERSON>",
            [8] = "Đại Thừa Kỳ",
        },
        
        -- Spells
        spells = {
            metal_spike = "Kim Thích Thuật",
            wood_bind = "Mộc Phược Thuật",
            water_shield = "Thủy Thuẫn Thuật",
            fire_ball = "Hỏa Cầu Thuật",
            earth_spike = "Th<PERSON> Thích Thuật",
            metal_rain = "<PERSON>",
            wood_growth = "Thôi Sinh Thuật",
            water_prison = "Thủy Lao Thuật",
            fire_tornado = "Hỏa Long Quyển",
            earth_armor = "Thổ Giáp Thuật",
            lightning_strike = "Lôi Kích Thuật",
            void_step = "Hư Không Bộ",
            gravity_well = "Trọng Lực Tỉnh",
            time_slow = "Thời Gian Hoãn Lưu",
            divine_shield = "Thần Thánh Hộ Thuẫn",
            soul_burn = "Phần Hồn Thuật",
        },
        
        -- Spell descriptions
        spell_descriptions = {
            metal_spike = "Phát xạ sắc bén kim thích tấn công địch nhân",
            wood_bind = "Dùng đằng mân thúc phược địch nhân, khiến kỳ vô pháp di động",
            water_shield = "Tạo tạo thủy thuẫn bảo hộ tự kỷ, hấp thu thương hại",
            fire_ball = "Phát xạ hỏa cầu tấn công địch nhân, tạo thành hỏa diễm thương hại",
            earth_spike = "Từ địa diện thăng khởi sắc bén thổ thích",
            metal_rain = "Triệu hoán kim thuộc vũ tấn công đại phạm vi địch nhân",
            wood_growth = "Thôi sinh thực vật nhanh chóng sinh trưởng, hồi phục sinh mệnh lực",
            water_prison = "Dùng thủy lao khốn trụ địch nhân, tạo thành trì tục thương hại",
            fire_tornado = "Triệu hoán hỏa diễm long quyển phong, tạo thành đại lượng thương hại",
            earth_armor = "Dùng đại địa chi lực hình thành hộ giáp, đại phúc đề thăng phòng ngự",
            lightning_strike = "Triệu hoán thiên lôi tấn công địch nhân, tạo thành cự đại thương hại",
            void_step = "Thuấn gian di động đáo chỉ định vị trí",
            gravity_well = "Tạo tạo trọng lực trường, kéo chích và thương hại địch nhân",
            time_slow = "Giảm hoãn khu vực nội thời gian lưu tốc",
            divine_shield = "Thần thánh chi lực hộ thể, miễn dịch tất cả thương hại",
            soul_burn = "Trực tiếp tấn công địch nhân linh hồn, tạo thành chân thật thương hại",
        },
        
        -- Realms
        realms = {
            peach_blossom = "Đào Hoa Bí Cảnh",
            heavenly_mountain = "Thiên Sơn Bí Cảnh", 
            ancient_battlefield = "Cổ Chiến Trường",
            fire_realm = "Địa Hỏa Bí Cảnh",
            moon_realm = "Âm Nguyệt Bí Cảnh",
            thunder_realm = "Lôi Điện Bí Cảnh",
            medicine_valley = "Dược Vương Cốc",
        },
        
        -- Realm descriptions
        realm_descriptions = {
            peach_blossom = "Xuân ý ương nhiên đích đào hoa tiên cảnh, sung mãn trị dũ chi lực",
            heavenly_mountain = "Cao sơn vân vụ liêu nhiễu đích tiên cảnh, linh khí nùng uất",
            ancient_battlefield = "Đao quang kiếm ảnh, anh hồn bất tán đích cổ đại chiến trường",
            fire_realm = "Liệt hỏa phần thiên, nham tương phiên cổn đích luyện ngục chi địa",
            moon_realm = "Nguyệt hoa như thủy, âm khí sâm sâm đích thần bí dạ cảnh",
            thunder_realm = "Lôi đình vạn quân, điện thiểm lôi minh đích thiên vực",
            medicine_valley = "Bách thảo phong mậu, dược hương trận trận đích tiên cốc",
        },
        
        -- Sects
        sects = {
            heavenly_sword_sect = "Thiên Kiếm Tông",
            fire_cloud_sect = "Hỏa Vân Phái",
            blood_moon_sect = "Huyết Nguyệt Ma Tông",
            wandering_immortals = "Tiêu Dao Tiên Minh",
        },
        
        -- Sect descriptions
        sect_descriptions = {
            heavenly_sword_sect = "Chính đạo đệ nhất đại phái, dĩ kiếm pháp vô song trước xưng",
            fire_cloud_sect = "Chưởng khống liệt hỏa chi lực, môn hạ đệ tử đều thị hỏa pháp cao thủ",
            blood_moon_sect = "Ma đạo tông môn, tu luyện huyết tinh pháp thuật",
            wandering_immortals = "Tự do tu sĩ liên minh, bất câu nhất cách",
        },
        
        -- World Events
        world_events = {
            spring_awakening = "Xuân Hồi Đại Địa",
            summer_trials = "Hạ Nhật Thí Luyện",
            autumn_harvest = "Thu Thu Thời Tiết",
            winter_meditation = "Đông Nhật Tĩnh Tu",
            blood_moon = "Huyết Nguyệt Giáng Lâm",
            celestial_convergence = "Thiên Tượng Hội Tụ",
            sect_war = "Tông Môn Đại Chiến",
            immortal_ascension = "Phi Thăng Đại Hội",
        },
        
        -- World Event descriptions
        world_event_descriptions = {
            spring_awakening = "Xuân thiên đích linh khí phục tô, tất cả tu luyện giả hoạch đắc ngạch ngoại kinh nghiệm",
            summer_trials = "Liệt nhật đương không, hỏa hệ pháp thuật uy lực tăng cường",
            autumn_harvest = "Phong thu quý tiết, luyện đan thành công suất đề thăng",
            winter_meditation = "Hàn đông thời tiết, minh tưởng hiệu quả đại phúc đề thăng",
            blood_moon = "Huyết nguyệt đương không, ma đạo tu sĩ lực lượng đại tăng",
            celestial_convergence = "Thiên tượng kỳ quan, tất cả bí cảnh đồng thời khai khải",
            sect_war = "Tông môn chi gian đích đại quy mô xung đột",
            immortal_ascension = "Cao giai tu sĩ đích phi thăng thí luyện",
        },
        
        -- UI Elements
        ui = {
            cultivation_tab = "Tu Luyện",
            spells_tab = "Pháp Thuật",
            sect_tab = "Tông Môn",
            quests_tab = "Nhiệm Vụ",
            alchemy_tab = "Luyện Đan",
            level = "Cấp Độ",
            experience = "Kinh Nghiệm",
            spiritual_energy = "Linh Khí",
            meditation = "Minh Tưởng",
            cast_spell = "Thi Triển Pháp Thuật",
            join_sect = "Gia Nhập Tông Môn",
            leave_sect = "Rời Khỏi Tông Môn",
            start_quest = "Bắt Đầu Nhiệm Vụ",
            complete_quest = "Hoàn Thành Nhiệm Vụ",
            hello = "Chào ngươi",
            want_to_trade = "Ta muốn giao dịch",
            ask_cultivation_advice = "Có thể cho ta lời khuyên tu luyện không?",
        },
        
        -- Messages
        messages = {
            meditation_start = "Bắt đầu minh tưởng tu luyện.",
            meditation_stop = "Kết thúc minh tưởng.",
            level_up = "Tu vi đột phá! Đạt đến {level}!",
            spell_learned = "Học được pháp thuật mới: {spell}!",
            sect_joined = "Gia nhập {sect} thành công!",
            sect_left = "Rời khỏi tông môn.",
            quest_started = "Nhận nhiệm vụ: {quest}",
            quest_completed = "Hoàn thành nhiệm vụ: {quest}",
            realm_entered = "Tiến vào {realm}",
            realm_closed = "Bí cảnh đã đóng.",
            pvp_entered = "Tiến vào khu vực PvP!",
            pvp_left = "Rời khỏi khu vực PvP.",
            insufficient_spiritual_energy = "Linh khí không đủ!",
            spell_on_cooldown = "Pháp thuật đang hồi phục!",
            cultivation_level_required = "Tu vi không đủ!",
            unknown_spell = "Pháp thuật không xác định!",
            spell_not_unlocked = "Pháp thuật chưa mở khóa!",
            no_cultivation = "Chưa có tu vi!",
            casting_spell = "Đang thi triển pháp thuật!",
            bound_by_vines = "Bị đằng mân trói buộc!",
            water_shield_activated = "Thủy thuẫn hộ thể!",
            unknown_quest = "Nhiệm vụ không xác định!",
            quest_already_active = "Nhiệm vụ đã được kích hoạt!",
            quest_already_completed = "Nhiệm vụ đã hoàn thành!",
            quest_requirements_not_met = "Không đủ điều kiện nhận nhiệm vụ!",
            learned_new_recipe = "Học được công thức mới!",
            received_title = "Nhận được danh hiệu: {title}!",
            unknown_sect = "Tông môn không xác định!",
            already_in_sect = "Đã gia nhập tông môn rồi!",
            insufficient_righteous_karma = "Chính đạo thanh danh không đủ!",
            insufficient_evil_karma = "Ma đạo thanh danh không đủ!",
            promoted_to_rank = "Thăng cấp thành {rank}!",
            no_sect = "Vô môn vô phái",
            unknown = "Không rõ",
            world_event_started = "Sự kiện thế giới: {event}",
            world_event_ended = "{event} đã kết thúc.",
            consumed_pill = "Đã dùng thuốc, tu vi tăng trưởng!",
            consumed_ginseng = "Dược lực thiên sâm thật mạnh mẽ!",
            learned_new_spell = "Lĩnh ngộ được pháp thuật mới!",
            read_manual = "Đọc sách tu luyện, có được cảm ngộ.",
            unknown_recipe = "Công thức không xác định!",
            furnace_in_use = "Lò luyện đan đang được sử dụng!",
            insufficient_materials = "Nguyên liệu không đủ: {item}",
            cultivation_insufficient_for_alchemy = "Tu vi không đủ để luyện đan này!",
            recipe_error = "Lỗi công thức!",
            temperature_out_of_control = "Nhiệt độ mất kiểm soát!",
            furnace_unstable = "Lò luyện không ổn định!",
            crafting_failed = "Luyện đan thất bại!",
            -- NPC Messages
            npc_no_talk = "Tôi không muốn nói chuyện với ngươi.",
            npc_trade_response = "Hãy xem ngươi có gì hay ho.",
            npc_greeting_friend_1 = "Cố nhân, lại gặp nhau rồi!",
            npc_greeting_friend_2 = "Rất vui được gặp lại ngươi.",
            npc_greeting_friend_3 = "Tu vi của ngươi lại có tiến bộ rồi.",
            npc_greeting_neutral_1 = "Chào ngươi, tu sĩ trẻ tuổi.",
            npc_greeting_neutral_2 = "Chào mừng đến đây.",
            npc_greeting_neutral_3 = "Chúc ngươi tu hành thuận lợi.",
            npc_greeting_suspicious_1 = "Ngươi là ai?",
            npc_greeting_suspicious_2 = "Nơi này không hoan nghênh kẻ lạ mặt.",
            npc_greeting_suspicious_3 = "Cẩn thận lời nói và hành động của ngươi.",
            npc_greeting_hostile_1 = "Cút đi!",
            npc_greeting_hostile_2 = "Ta không muốn thấy ngươi!",
            npc_greeting_hostile_3 = "Ngươi là kẻ gây rối!",
            npc_advice_level_1 = "Tu luyện chi lộ bắt đầu từ minh tưởng, hãy tĩnh tâm cảm ngộ.",
            npc_advice_level_2 = "Trúc Cơ Kỳ cần vững chắc căn cơ, không thể vội vàng.",
            npc_advice_level_3 = "Kim Đan Kỳ là bước ngoặt quan trọng, cần nhiều linh khí.",
            npc_advice_level_4 = "Nguyên Anh Kỳ bắt đầu tiếp xúc pháp thuật cao thâm, phải cẩn thận kiểm soát.",
            npc_advice_level_5 = "Hóa Thần Kỳ cần lĩnh ngộ thiên địa chi đạo, hãy quan sát tự nhiên.",
            npc_advice_level_6 = "Luyện Hư Kỳ đã gần cảnh giới tiên nhân, cần siêu thoát thế tục.",
            npc_advice_level_7 = "Hợp Thể Kỳ cần hợp nhất với thiên địa, cần ngộ tính cực cao.",
            npc_advice_level_8 = "Đại Thừa Kỳ đã là cảnh giới tiên nhân, ta chỉ có thể ngưỡng vọng.",
            npc_advice_high_level = "Tu vi của ngươi đã rất cao, ta có thể dạy ngươi không nhiều.",
            npc_sect_info_peach_blossom = "Đào Hoa Tông nổi tiếng về trị liệu và tự nhiên chi đạo.",
            npc_sect_info_heaven_sword = "Thiên Kiếm Tông là chính đạo đệ nhất đại phái, nổi tiếng kiếm pháp vô song.",
            npc_sect_info_fire_cloud = "Hỏa Vân Phái chưởng khống liệt hỏa chi lực, môn hạ đệ tử đều là hỏa pháp cao thủ.",
            npc_sect_info_demonic = "Ma đạo tông môn... những người đó đi con đường tà ác, cẩn thận vi thượng.",
            npc_sect_info_default = "Tông môn của ta có lịch sử lâu đời và đáy uẩn sâu dày.",
            npc_thanks_gift = "Cảm ơn món quà của ngươi.",
            npc_angry_attack = "Ngươi dám tấn công ta!",
            npc_thanks_help = "Cảm ơn sự giúp đỡ của ngươi.",
            npc_trade_pleasant = "Giao dịch vui vẻ.",
            ask_about_sect = "Nói cho tôi nghe về {sect}",
            -- Portal Messages
            portal_closes_in_days = "{realm} ({days} ngày nữa đóng)",
            portal_closing_soon = "{realm} (sắp đóng)",
            portal_inactive = "Cổng Bí Cảnh (Chưa kích hoạt)",
            portal_default = "Cổng Bí Cảnh",
            mysterious_realm = "Bí cảnh tu tiên huyền bí.",
            portal_to_unknown = "Một cánh cửa huyền bí dẫn đến bí cảnh chưa biết.",
            what_is_this = "Đây là gì?",
            cultivation_insufficient_for_realm = "Tu vi không đủ để vào bí cảnh này.",
            entered_realm = "Đã vào {realm}!",
            left_realm = "Đã rời {realm}.",
            realm_closed_ejected = "Bí cảnh đóng, bị dịch chuyển ra ngoài.",
            realm_appeared = "Một {realm} đã xuất hiện!",
            enter_realm_prompt = "Vào {realm}",
        },
        
        -- Quest Names
        quest_names = {
            first_meditation = "Lần Đầu Minh Tưởng",
            collect_herbs = "Thu Thập Linh Thảo",
            first_spell = "Học Pháp Thuật Đầu Tiên",
            realm_explorer = "Thám Hiểm Bí Cảnh",
            alchemy_apprentice = "Học Việc Luyện Đan",
            sect_champion = "Anh Hùng Tông Môn",
            realm_master = "Chủ Nhân Bí Cảnh",
        },

        -- Quest Descriptions
        quest_descriptions = {
            first_meditation = "Thực hiện lần minh tưởng tu luyện đầu tiên",
            collect_herbs = "Thu thập các loại linh thảo cần thiết cho tu luyện",
            first_spell = "Học hỏi và sử dụng pháp thuật đầu tiên",
            realm_explorer = "Khám phá các bí cảnh tu luyện khác nhau",
            alchemy_apprentice = "Học hỏi những kiến thức cơ bản về luyện đan",
            sect_champion = "Trở thành đệ tử xuất sắc của tông môn",
            realm_master = "Chinh phục tất cả các bí cảnh tu luyện",
        },

        -- Titles
        titles = {
            realm_conqueror = "Chinh Phục Bí Cảnh",
        },

        -- Sect Ranks
        sect_ranks = {
            -- Heavenly Sword Sect
            heavenly_sword_sect_1 = "Ngoại Môn Đệ Tử",
            heavenly_sword_sect_2 = "Nội Môn Đệ Tử",
            heavenly_sword_sect_3 = "Hạt Nhân Đệ Tử",
            heavenly_sword_sect_4 = "Trưởng Lão",
            heavenly_sword_sect_5 = "Chưởng Môn",

            -- Fire Cloud Sect
            fire_cloud_sect_1 = "Ngoại Môn Đệ Tử",
            fire_cloud_sect_2 = "Nội Môn Đệ Tử",
            fire_cloud_sect_3 = "Hạt Nhân Đệ Tử",
            fire_cloud_sect_4 = "Trưởng Lão",
            fire_cloud_sect_5 = "Chưởng Môn",

            -- Blood Moon Sect
            blood_moon_sect_1 = "Ngoại Môn Ma Đồ",
            blood_moon_sect_2 = "Nội Môn Ma Đồ",
            blood_moon_sect_3 = "Hạt Nhân Ma Đồ",
            blood_moon_sect_4 = "Ma Trưởng Lão",
            blood_moon_sect_5 = "Ma Chủ",

            -- Wandering Immortals
            wandering_immortals_1 = "Tán Tu",
            wandering_immortals_2 = "Tiêu Dao Khách",
            wandering_immortals_3 = "Tiên Minh Thành Viên",
            wandering_immortals_4 = "Tiên Minh Trưởng Lão",
            wandering_immortals_5 = "Minh Chủ",
        },

        -- Items
        items = {
            spiritual_stone = "Linh Thạch",
            cultivation_pill = "Tu Luyện Đan",
            heaven_ginseng = "Thiên Sâm",
            fire_crystal = "Hỏa Tinh Thạch",
            peach_blossom_flower = "Đào Hoa",
            spring_water = "Linh Tuyền Thủy",
            healing_dew = "Trị Dũ Cam Lộ",
            butterfly_essence = "Điệp Linh Tinh Hoa",
        },
    },
    
    english = {
        -- Cultivation levels
        cultivation_levels = {
            [1] = "Qi Refining",
            [2] = "Foundation Building",
            [3] = "Golden Core",
            [4] = "Nascent Soul",
            [5] = "Spirit Transform",
            [6] = "Void Refining",
            [7] = "Integration",
            [8] = "Great Vehicle",
        },
        
        -- Spells
        spells = {
            metal_spike = "Metal Spike",
            wood_bind = "Wood Bind",
            water_shield = "Water Shield",
            fire_ball = "Fire Ball",
            earth_spike = "Earth Spike",
            metal_rain = "Metal Rain",
            wood_growth = "Wood Growth",
            water_prison = "Water Prison",
            fire_tornado = "Fire Tornado",
            earth_armor = "Earth Armor",
            lightning_strike = "Lightning Strike",
            void_step = "Void Step",
            gravity_well = "Gravity Well",
            time_slow = "Time Slow",
            divine_shield = "Divine Shield",
            soul_burn = "Soul Burn",
        },
        
        -- Spell descriptions
        spell_descriptions = {
            metal_spike = "Launch sharp metal spikes to attack enemies",
            wood_bind = "Use vines to bind enemies, making them unable to move",
            water_shield = "Create water shield to protect yourself, absorbing damage",
            fire_ball = "Launch fire ball to attack enemies, causing flame damage",
            earth_spike = "Raise sharp earth spikes from the ground",
            metal_rain = "Summon metal rain to attack large area enemies",
            wood_growth = "Accelerate plant growth, restoring life force",
            water_prison = "Use water prison to trap enemies, causing continuous damage",
            fire_tornado = "Summon flame tornado, causing massive damage",
            earth_armor = "Use earth's power to form armor, greatly improving defense",
            lightning_strike = "Summon heavenly thunder to attack enemies, causing huge damage",
            void_step = "Instantly teleport to designated location",
            gravity_well = "Create gravity field, pulling and damaging enemies",
            time_slow = "Slow down time flow in the area",
            divine_shield = "Divine power protection, immune to all damage",
            soul_burn = "Directly attack enemy's soul, causing true damage",
        },
        
        -- Realms
        realms = {
            peach_blossom = "Peach Blossom Realm",
            heavenly_mountain = "Heavenly Mountain Realm",
            ancient_battlefield = "Ancient Battlefield",
            fire_realm = "Fire Realm",
            moon_realm = "Moon Realm",
            thunder_realm = "Thunder Realm",
            medicine_valley = "Medicine Valley",
        },
        
        -- Realm descriptions
        realm_descriptions = {
            peach_blossom = "Spring-filled peach blossom fairyland, full of healing power",
            heavenly_mountain = "High mountain fairyland surrounded by clouds, rich spiritual energy",
            ancient_battlefield = "Ancient battlefield with sword lights and heroic souls",
            fire_realm = "Hellish land with blazing fire and rolling lava",
            moon_realm = "Mysterious night realm with moonlight like water and yin energy",
            thunder_realm = "Heavenly domain with thunderous power and lightning",
            medicine_valley = "Fairy valley with abundant herbs and medicinal fragrance",
        },
        
        -- Sects
        sects = {
            heavenly_sword_sect = "Heavenly Sword Sect",
            fire_cloud_sect = "Fire Cloud Sect",
            blood_moon_sect = "Blood Moon Sect",
            wandering_immortals = "Wandering Immortals",
        },
        
        -- Sect descriptions
        sect_descriptions = {
            heavenly_sword_sect = "The first righteous sect, famous for unparalleled sword techniques",
            fire_cloud_sect = "Masters fire power, disciples are all fire magic experts",
            blood_moon_sect = "Demonic sect practicing blood magic techniques",
            wandering_immortals = "Free cultivator alliance, unconventional",
        },
        
        -- World Events
        world_events = {
            spring_awakening = "Spring Awakening",
            summer_trials = "Summer Trials",
            autumn_harvest = "Autumn Harvest",
            winter_meditation = "Winter Meditation",
            blood_moon = "Blood Moon",
            celestial_convergence = "Celestial Convergence",
            sect_war = "Sect War",
            immortal_ascension = "Immortal Ascension",
        },
        
        -- World Event descriptions
        world_event_descriptions = {
            spring_awakening = "Spring's spiritual energy revival, all cultivators gain extra experience",
            summer_trials = "Blazing sun overhead, fire spell power enhanced",
            autumn_harvest = "Harvest season, alchemy success rate improved",
            winter_meditation = "Winter season, meditation effects greatly enhanced",
            blood_moon = "Blood moon overhead, demonic cultivators' power greatly increased",
            celestial_convergence = "Celestial wonder, all realms open simultaneously",
            sect_war = "Large-scale conflict between sects",
            immortal_ascension = "Ascension trials for high-level cultivators",
        },
        
        -- UI Elements
        ui = {
            cultivation_tab = "Cultivation",
            spells_tab = "Spells",
            sect_tab = "Sect",
            quests_tab = "Quests",
            alchemy_tab = "Alchemy",
            level = "Level",
            experience = "Experience",
            spiritual_energy = "Spiritual Energy",
            meditation = "Meditation",
            cast_spell = "Cast Spell",
            join_sect = "Join Sect",
            leave_sect = "Leave Sect",
            start_quest = "Start Quest",
            complete_quest = "Complete Quest",
            hello = "Hello",
            want_to_trade = "I want to trade",
            ask_cultivation_advice = "Can you give me cultivation advice?",
        },
        
        -- Messages
        messages = {
            meditation_start = "Started meditation cultivation.",
            meditation_stop = "Ended meditation.",
            level_up = "Cultivation breakthrough! Reached {level}!",
            spell_learned = "Learned new spell: {spell}!",
            sect_joined = "Successfully joined {sect}!",
            sect_left = "Left the sect.",
            quest_started = "Accepted quest: {quest}",
            quest_completed = "Completed quest: {quest}",
            realm_entered = "Entered {realm}",
            realm_closed = "The realm has closed.",
            pvp_entered = "Entered PvP zone!",
            pvp_left = "Left PvP zone.",
            insufficient_spiritual_energy = "Insufficient spiritual energy!",
            spell_on_cooldown = "Spell is on cooldown!",
            cultivation_level_required = "Cultivation level insufficient!",
            unknown_spell = "Unknown spell!",
            spell_not_unlocked = "Spell not unlocked!",
            no_cultivation = "No cultivation!",
            casting_spell = "Casting spell!",
            bound_by_vines = "Bound by vines!",
            water_shield_activated = "Water shield activated!",
            unknown_quest = "Unknown quest!",
            quest_already_active = "Quest already active!",
            quest_already_completed = "Quest already completed!",
            quest_requirements_not_met = "Quest requirements not met!",
            learned_new_recipe = "Learned new recipe!",
            received_title = "Received title: {title}!",
            unknown_sect = "Unknown sect!",
            already_in_sect = "Already in a sect!",
            insufficient_righteous_karma = "Insufficient righteous karma!",
            insufficient_evil_karma = "Insufficient evil karma!",
            promoted_to_rank = "Promoted to {rank}!",
            no_sect = "No sect",
            unknown = "Unknown",
            world_event_started = "World Event: {event}",
            world_event_ended = "{event} has ended.",
            consumed_pill = "Consumed pill, cultivation increased!",
            consumed_ginseng = "The power of heaven ginseng is truly strong!",
            learned_new_spell = "Comprehended a new spell!",
            read_manual = "Read cultivation manual, gained insights.",
            unknown_recipe = "Unknown recipe!",
            furnace_in_use = "Furnace is in use!",
            insufficient_materials = "Insufficient materials: {item}",
            cultivation_insufficient_for_alchemy = "Cultivation insufficient for this pill!",
            recipe_error = "Recipe error!",
            temperature_out_of_control = "Temperature out of control!",
            furnace_unstable = "Furnace unstable!",
            crafting_failed = "Crafting failed!",
            -- NPC Messages
            npc_no_talk = "I don't want to talk to you.",
            npc_trade_response = "Let's see what you have.",
            npc_greeting_friend_1 = "Old friend, we meet again!",
            npc_greeting_friend_2 = "Good to see you again.",
            npc_greeting_friend_3 = "Your cultivation has improved.",
            npc_greeting_neutral_1 = "Hello, young cultivator.",
            npc_greeting_neutral_2 = "Welcome here.",
            npc_greeting_neutral_3 = "May your cultivation go smoothly.",
            npc_greeting_suspicious_1 = "Who are you?",
            npc_greeting_suspicious_2 = "Strangers are not welcome here.",
            npc_greeting_suspicious_3 = "Watch your words and actions.",
            npc_greeting_hostile_1 = "Get lost!",
            npc_greeting_hostile_2 = "I don't want to see you!",
            npc_greeting_hostile_3 = "You troublemaker!",
            npc_advice_level_1 = "The cultivation path begins with meditation, focus on inner peace.",
            npc_advice_level_2 = "Foundation Building requires solid foundation, don't be hasty.",
            npc_advice_level_3 = "Golden Core is an important turning point, requires much spiritual energy.",
            npc_advice_level_4 = "Nascent Soul begins to touch advanced spells, be careful with control.",
            npc_advice_level_5 = "Spirit Transform requires understanding the way of heaven and earth.",
            npc_advice_level_6 = "Void Refining is close to immortal realm, need to transcend worldly matters.",
            npc_advice_level_7 = "Integration requires unity with heaven and earth, needs high comprehension.",
            npc_advice_level_8 = "Great Vehicle is immortal realm, I can only look up to it.",
            npc_advice_high_level = "Your cultivation is already high, I have little to teach.",
            npc_sect_info_peach_blossom = "Peach Blossom Sect is known for healing and natural way.",
            npc_sect_info_heaven_sword = "Heavenly Sword Sect is the first righteous sect, famous for unparalleled sword techniques.",
            npc_sect_info_fire_cloud = "Fire Cloud Sect controls the power of fire, disciples are all fire magic experts.",
            npc_sect_info_demonic = "Demonic sects... those people walk the evil path, be careful.",
            npc_sect_info_default = "My sect has a long history and deep foundation.",
            npc_thanks_gift = "Thank you for the gift.",
            npc_angry_attack = "How dare you attack me!",
            npc_thanks_help = "Thank you for your help.",
            npc_trade_pleasant = "Pleasant trade.",
            ask_about_sect = "Tell me about {sect}",
            -- Portal Messages
            portal_closes_in_days = "{realm} (closes in {days} days)",
            portal_closing_soon = "{realm} (closing soon)",
            portal_inactive = "Realm Portal (Inactive)",
            portal_default = "Realm Portal",
            mysterious_realm = "Mysterious cultivation realm.",
            portal_to_unknown = "A mysterious door to unknown realm.",
            what_is_this = "What is this?",
            cultivation_insufficient_for_realm = "Cultivation insufficient to enter this realm.",
            entered_realm = "Entered {realm}!",
            left_realm = "Left {realm}.",
            realm_closed_ejected = "Realm closed, teleported out.",
            realm_appeared = "A {realm} has appeared!",
            enter_realm_prompt = "Enter {realm}",
        },
        
        -- Quest Names
        quest_names = {
            first_meditation = "First Meditation",
            collect_herbs = "Collect Herbs",
            first_spell = "Learn First Spell",
            realm_explorer = "Realm Explorer",
            alchemy_apprentice = "Alchemy Apprentice",
            sect_champion = "Sect Champion",
            realm_master = "Realm Master",
        },

        -- Quest Descriptions
        quest_descriptions = {
            first_meditation = "Perform your first meditation cultivation",
            collect_herbs = "Collect spiritual herbs needed for cultivation",
            first_spell = "Learn and use your first spell",
            realm_explorer = "Explore different cultivation realms",
            alchemy_apprentice = "Learn the basics of alchemy",
            sect_champion = "Become an outstanding disciple of your sect",
            realm_master = "Conquer all cultivation realms",
        },

        -- Titles
        titles = {
            realm_conqueror = "Realm Conqueror",
        },

        -- Sect Ranks
        sect_ranks = {
            -- Heavenly Sword Sect
            heavenly_sword_sect_1 = "Outer Disciple",
            heavenly_sword_sect_2 = "Inner Disciple",
            heavenly_sword_sect_3 = "Core Disciple",
            heavenly_sword_sect_4 = "Elder",
            heavenly_sword_sect_5 = "Sect Master",

            -- Fire Cloud Sect
            fire_cloud_sect_1 = "Outer Disciple",
            fire_cloud_sect_2 = "Inner Disciple",
            fire_cloud_sect_3 = "Core Disciple",
            fire_cloud_sect_4 = "Elder",
            fire_cloud_sect_5 = "Sect Master",

            -- Blood Moon Sect
            blood_moon_sect_1 = "Outer Demon",
            blood_moon_sect_2 = "Inner Demon",
            blood_moon_sect_3 = "Core Demon",
            blood_moon_sect_4 = "Demon Elder",
            blood_moon_sect_5 = "Demon Lord",

            -- Wandering Immortals
            wandering_immortals_1 = "Rogue Cultivator",
            wandering_immortals_2 = "Free Wanderer",
            wandering_immortals_3 = "Alliance Member",
            wandering_immortals_4 = "Alliance Elder",
            wandering_immortals_5 = "Alliance Leader",
        },

        -- Items
        items = {
            spiritual_stone = "Spiritual Stone",
            cultivation_pill = "Cultivation Pill",
            heaven_ginseng = "Heaven Ginseng",
            fire_crystal = "Fire Crystal",
            peach_blossom_flower = "Peach Blossom Flower",
            spring_water = "Spring Water",
            healing_dew = "Healing Dew",
            butterfly_essence = "Butterfly Essence",
        },
    },
}

-- Get localized text function
function GetLocalizedText(category, key, params)
    local lang_data = LOCALIZATION[LANGUAGE]
    if not lang_data then
        lang_data = LOCALIZATION["vietnamese"] -- Fallback to Vietnamese
    end
    
    local category_data = lang_data[category]
    if not category_data then
        return key -- Return key if category not found
    end
    
    local text = category_data[key]
    if not text then
        return key -- Return key if text not found
    end
    
    -- Replace parameters if provided
    if params then
        for param_key, param_value in pairs(params) do
            text = string.gsub(text, "{" .. param_key .. "}", param_value)
        end
    end
    
    return text
end

-- Convenience functions for common categories
function GetCultivationLevelName(level)
    return GetLocalizedText("cultivation_levels", level) or "Unknown Level"
end

function GetSpellName(spell_id)
    return GetLocalizedText("spells", spell_id) or spell_id
end

function GetSpellDescription(spell_id)
    return GetLocalizedText("spell_descriptions", spell_id) or "No description"
end

function GetRealmName(realm_id)
    return GetLocalizedText("realms", realm_id) or realm_id
end

function GetRealmDescription(realm_id)
    return GetLocalizedText("realm_descriptions", realm_id) or "No description"
end

function GetSectName(sect_id)
    return GetLocalizedText("sects", sect_id) or sect_id
end

function GetSectDescription(sect_id)
    return GetLocalizedText("sect_descriptions", sect_id) or "No description"
end

function GetWorldEventName(event_id)
    return GetLocalizedText("world_events", event_id) or event_id
end

function GetWorldEventDescription(event_id)
    return GetLocalizedText("world_event_descriptions", event_id) or "No description"
end

function GetUIText(ui_key)
    return GetLocalizedText("ui", ui_key) or ui_key
end

function GetMessage(message_key, params)
    return GetLocalizedText("messages", message_key, params) or message_key
end

function GetItemName(item_id)
    return GetLocalizedText("items", item_id) or item_id
end

-- Export functions globally
GLOBAL.GetLocalizedText = GetLocalizedText
GLOBAL.GetCultivationLevelName = GetCultivationLevelName
GLOBAL.GetSpellName = GetSpellName
GLOBAL.GetSpellDescription = GetSpellDescription
GLOBAL.GetRealmName = GetRealmName
GLOBAL.GetRealmDescription = GetRealmDescription
GLOBAL.GetSectName = GetSectName
GLOBAL.GetSectDescription = GetSectDescription
GLOBAL.GetWorldEventName = GetWorldEventName
GLOBAL.GetWorldEventDescription = GetWorldEventDescription
GLOBAL.GetUIText = GetUIText
GLOBAL.GetMessage = GetMessage
GLOBAL.GetItemName = GetItemName

print("[Tu Tiên Bí Cảnh] Localization system loaded - Language: " .. LANGUAGE)
