-- Boss Health Bar Widget - Clean Implementation

local Widget = require "widgets/widget"
local Text = require "widgets/text"
local Image = require "widgets/image"

local BossHealthBar = Class(Widget, function(self, owner)
    Widget._ctor(self, "BossHealthBar")
    
    self.owner = owner
    self.boss = nil
    self.visible = false
    
    -- Main container
    self.root = self:AddChild(Widget("root"))
    self.root:SetPosition(0, 200, 0) -- Top center
    
    -- Background
    self.bg = self.root:AddChild(Image("images/ui.xml", "dialog_bg.tex"))
    self.bg:SetSize(350, 80)
    self.bg:SetTint(0.1, 0.1, 0.1, 0.8)
    
    -- Boss name
    self.boss_name = self.root:AddChild(Text(CHATFONT, 20))
    self.boss_name:SetPosition(0, 20, 0)
    self.boss_name:SetString("Ancient Cultivation Guardian")
    self.boss_name:SetColour(1, 0.8, 0.2, 1)
    
    -- Health bar background
    self.health_bg = self.root:AddChild(Image("images/ui.xml", "progressbar_bg.tex"))
    self.health_bg:SetPosition(0, -5, 0)
    self.health_bg:SetSize(280, 18)
    self.health_bg:SetTint(0.3, 0.1, 0.1, 1)
    
    -- Health bar fill
    self.health_fill = self.root:AddChild(Image("images/ui.xml", "progressbar_fill.tex"))
    self.health_fill:SetPosition(-140, -5, 0)
    self.health_fill:SetSize(280, 16)
    self.health_fill:SetTint(0.8, 0.2, 0.2, 1)
    
    -- Health text
    self.health_text = self.root:AddChild(Text(CHATFONT, 14))
    self.health_text:SetPosition(0, -5, 0)
    self.health_text:SetString("2000 / 2000")
    self.health_text:SetColour(1, 1, 1, 1)
    
    -- Phase indicator
    self.phase_text = self.root:AddChild(Text(CHATFONT, 12))
    self.phase_text:SetPosition(0, -25, 0)
    self.phase_text:SetString("Phase 1")
    self.phase_text:SetColour(1, 1, 0.5, 1)
    
    self:Hide()
end)

function BossHealthBar:SetBoss(boss)
    self.boss = boss
    if boss then
        self:Show()
        self:UpdateDisplay()
    else
        self:Hide()
    end
end

function BossHealthBar:UpdateDisplay()
    if not self.boss or not self.boss:IsValid() then
        self:Hide()
        return
    end
    
    -- Update health
    if self.boss.components.health then
        local current = self.boss.components.health.currenthealth
        local max = self.boss.components.health.maxhealth
        local percent = current / max
        
        -- Update health bar
        self.health_fill:SetSize(280 * percent, 16)
        self.health_fill:SetPosition(-140 + (280 * percent / 2), -5, 0)
        
        -- Update text
        self.health_text:SetString(string.format("%.0f / %.0f", current, max))
        
        -- Color based on health
        if percent > 0.6 then
            self.health_fill:SetTint(0.8, 0.2, 0.2, 1) -- Red
        elseif percent > 0.3 then
            self.health_fill:SetTint(1.0, 0.6, 0.2, 1) -- Orange
        else
            self.health_fill:SetTint(1.0, 0.2, 0.2, 1) -- Bright red
        end
        
        -- Update phase
        local phase = 1
        if percent <= 0.75 then phase = 2 end
        if percent <= 0.5 then phase = 3 end
        if percent <= 0.25 then phase = 4 end
        
        self.phase_text:SetString("Phase " .. phase)
    end
end

function BossHealthBar:OnUpdate(dt)
    if self.visible and self.boss then
        self:UpdateDisplay()
    end
end

function BossHealthBar:Show()
    self.visible = true
    self:SetPosition(0, 0, 0)
end

function BossHealthBar:Hide()
    self.visible = false
    self:SetPosition(0, -1000, 0)
end

return BossHealthBar
