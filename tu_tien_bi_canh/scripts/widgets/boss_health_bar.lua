-- Boss Health Bar Widget
-- Shows boss health, spiritual energy, and active effects

local Widget = require "widgets/widget"
local Text = require "widgets/text"
local Image = require "widgets/image"

local BossHealthBar = Class(Widget, function(self, owner)
    Widget._ctor(self, "BossHealthBar")
    
    self.owner = owner
    self.boss = nil
    self.visible = false
    
    -- Main container
    self.root = self:AddChild(Widget("root"))
    self.root:SetPosition(0, 200, 0) -- Top center of screen
    
    -- Background panel
    self.bg = self.root:AddChild(Image("images/ui.xml", "dialog_bg.tex"))
    self.bg:SetSize(400, 120)
    self.bg:SetTint(0.2, 0.2, 0.2, 0.8) -- Dark semi-transparent
    
    -- Boss name
    self.boss_name = self.root:AddChild(Text(CHATFONT, 24))
    self.boss_name:SetPosition(0, 35, 0)
    self.boss_name:<PERSON><PERSON><PERSON>("Ancient Cultivation Guardian")
    self.boss_name:SetColour(1, 0.8, 0.2, 1) -- Golden text
    
    -- Health bar background
    self.health_bg = self.root:AddChild(Image("images/ui.xml", "progressbar_bg.tex"))
    self.health_bg:SetPosition(0, 10, 0)
    self.health_bg:SetSize(300, 20)
    self.health_bg:SetTint(0.3, 0.1, 0.1, 1) -- Dark red
    
    -- Health bar fill
    self.health_fill = self.root:AddChild(Image("images/ui.xml", "progressbar_fill.tex"))
    self.health_fill:SetPosition(-150, 10, 0) -- Start from left
    self.health_fill:SetSize(300, 18)
    self.health_fill:SetTint(0.8, 0.2, 0.2, 1) -- Bright red
    
    -- Health text
    self.health_text = self.root:AddChild(Text(CHATFONT, 16))
    self.health_text:SetPosition(0, 10, 0)
    self.health_text:SetString("2500 / 2500")
    self.health_text:SetColour(1, 1, 1, 1)
    
    -- Spiritual energy bar background
    self.energy_bg = self.root:AddChild(Image("images/ui.xml", "progressbar_bg.tex"))
    self.energy_bg:SetPosition(0, -15, 0)
    self.energy_bg:SetSize(250, 16)
    self.energy_bg:SetTint(0.1, 0.1, 0.3, 1) -- Dark blue
    
    -- Spiritual energy bar fill
    self.energy_fill = self.root:AddChild(Image("images/ui.xml", "progressbar_fill.tex"))
    self.energy_fill:SetPosition(-125, -15, 0)
    self.energy_fill:SetSize(250, 14)
    self.energy_fill:SetTint(0.2, 0.5, 1.0, 1) -- Bright blue
    
    -- Energy text
    self.energy_text = self.root:AddChild(Text(CHATFONT, 14))
    self.energy_text:SetPosition(0, -15, 0)
    self.energy_text:SetString("Spiritual Energy: 500 / 500")
    self.energy_text:SetColour(0.7, 0.9, 1, 1) -- Light blue
    
    -- Active effects text
    self.effects_text = self.root:AddChild(Text(CHATFONT, 12))
    self.effects_text:SetPosition(0, -35, 0)
    self.effects_text:SetString("")
    self.effects_text:SetColour(1, 1, 0.5, 1) -- Yellow
    
    -- Spell cooldown indicators
    self.spell_indicators = {}
    local spell_names = {"Spiritual Blast", "Cultivation Drain", "Ancient Barrier", "Summon Spirits"}
    for i, spell_name in ipairs(spell_names) do
        local indicator = self.root:AddChild(Text(CHATFONT, 10))
        indicator:SetPosition(-150 + (i-1) * 100, -50, 0)
        indicator:SetString(spell_name)
        indicator:SetColour(0.5, 0.5, 0.5, 1) -- Gray when on cooldown
        self.spell_indicators[i] = indicator
    end
    
    -- Hide initially
    self:Hide()
end)

function BossHealthBar:SetBoss(boss)
    self.boss = boss
    if boss then
        self:Show()
        self:UpdateDisplay()
    else
        self:Hide()
    end
end

function BossHealthBar:UpdateDisplay()
    if not self.boss or not self.boss:IsValid() then
        self:Hide()
        return
    end
    
    -- Update health
    if self.boss.components.health then
        local current_health = self.boss.components.health.currenthealth
        local max_health = self.boss.components.health.maxhealth
        local health_percent = current_health / max_health
        
        -- Update health bar
        self.health_fill:SetSize(300 * health_percent, 18)
        self.health_fill:SetPosition(-150 + (300 * health_percent / 2), 10, 0)
        
        -- Update health text
        self.health_text:SetString(string.format("%.0f / %.0f", current_health, max_health))
        
        -- Change color based on health
        if health_percent > 0.6 then
            self.health_fill:SetTint(0.8, 0.2, 0.2, 1) -- Red
        elseif health_percent > 0.3 then
            self.health_fill:SetTint(1.0, 0.6, 0.2, 1) -- Orange
        else
            self.health_fill:SetTint(1.0, 0.2, 0.2, 1) -- Bright red (low health)
        end
    end
    
    -- Update spiritual energy
    if self.boss.boss_brain then
        local current_energy = self.boss.boss_brain.spiritual_energy
        local max_energy = 500 -- Boss max energy
        local energy_percent = current_energy / max_energy
        
        -- Update energy bar
        self.energy_fill:SetSize(250 * energy_percent, 14)
        self.energy_fill:SetPosition(-125 + (250 * energy_percent / 2), -15, 0)
        
        -- Update energy text
        self.energy_text:SetString(string.format("Spiritual Energy: %.0f / %.0f", current_energy, max_energy))
    end
    
    -- Update active effects
    local effects = {}
    if self.boss.boss_brain and self.boss.boss_brain.barrier_active then
        table.insert(effects, "Ancient Barrier Active")
    end
    
    local spirits_count = 0
    if self.boss.boss_brain and self.boss.boss_brain.summoned_spirits then
        for _, spirit in ipairs(self.boss.boss_brain.summoned_spirits) do
            if spirit and spirit:IsValid() then
                spirits_count = spirits_count + 1
            end
        end
    end
    if spirits_count > 0 then
        table.insert(effects, string.format("%d Spirits Active", spirits_count))
    end
    
    self.effects_text:SetString(table.concat(effects, " | "))
    
    -- Update spell cooldowns
    if self.boss.boss_brain then
        local spell_ids = {"spiritual_blast", "cultivation_drain", "ancient_barrier", "summon_spirits"}
        local current_time = GetTime()
        
        for i, spell_id in ipairs(spell_ids) do
            local indicator = self.spell_indicators[i]
            if indicator then
                local can_cast = self.boss.boss_brain:CanCastSpell(spell_id)
                if can_cast then
                    indicator:SetColour(0.2, 1.0, 0.2, 1) -- Green when ready
                else
                    -- Show cooldown time
                    local last_cast = self.boss.boss_brain.last_spell_time[spell_id] or 0
                    local cooldown = 0
                    if spell_id == "spiritual_blast" then cooldown = 8
                    elseif spell_id == "cultivation_drain" then cooldown = 12
                    elseif spell_id == "ancient_barrier" then cooldown = 20
                    elseif spell_id == "summon_spirits" then cooldown = 25
                    end
                    
                    local time_left = math.max(0, cooldown - (current_time - last_cast))
                    if time_left > 0 then
                        indicator:SetColour(1.0, 0.5, 0.2, 1) -- Orange when on cooldown
                        indicator:SetString(string.format("%.1fs", time_left))
                    else
                        indicator:SetColour(0.2, 1.0, 0.2, 1) -- Green when ready
                    end
                end
            end
        end
    end
end

function BossHealthBar:OnUpdate(dt)
    if self.visible and self.boss then
        self:UpdateDisplay()
    end
end

function BossHealthBar:Show()
    self.visible = true
    self:SetPosition(0, 0, 0)
end

function BossHealthBar:Hide()
    self.visible = false
    self:SetPosition(0, -1000, 0) -- Move off screen
end

return BossHealthBar
