-- Boss Health Bar Widget - Clean Implementation

local Widget = require "widgets/widget"
local Text = require "widgets/text"
local Image = require "widgets/image"

local BossHealthBar = Class(Widget, function(self, owner)
    Widget._ctor(self, "BossHealthBar")
    
    self.owner = owner
    self.boss = nil
    self.visible = false
    
    -- Main container
    self.root = self:AddChild(Widget("root"))
    self.root:SetPosition(0, 200, 0) -- Top center
    
    -- Background (use safe fallback)
    local bg_success = pcall(function()
        self.bg = self.root:AddChild(Image("images/ui.xml", "dialog_bg.tex"))
        self.bg:SetSize(350, 80)
        self.bg:SetTint(0.1, 0.1, 0.1, 0.8)
    end)

    if not bg_success then
        -- Fallback: create simple colored background
        self.bg = self.root:AddChild(Image())
        if self.bg.SetTexture then
            self.bg:SetTexture("images/ui.xml", "blank.tex")
        end
        self.bg:SetSize(350, 80)
        self.bg:SetTint(0.1, 0.1, 0.1, 0.8)
    end
    
    -- Boss name with cultivation realm
    self.boss_name = self.root:AddChild(Text(CHATFONT, 18))
    self.boss_name:SetPosition(0, 25, 0)
    self.boss_name:SetString("古修者 (Ancient Cultivator)")
    self.boss_name:SetColour(1, 0.8, 0.2, 1)

    -- Cultivation realm indicator
    self.realm_text = self.root:AddChild(Text(CHATFONT, 14))
    self.realm_text:SetPosition(0, 8, 0)
    self.realm_text:SetString("金丹期 (Golden Core Realm)")
    self.realm_text:SetColour(0.8, 1, 0.8, 1)
    
    -- Health bar background (safe)
    local health_bg_success = pcall(function()
        self.health_bg = self.root:AddChild(Image("images/ui.xml", "progressbar_bg.tex"))
    end)

    if not health_bg_success then
        self.health_bg = self.root:AddChild(Image())
        if self.health_bg.SetTexture then
            self.health_bg:SetTexture("images/ui.xml", "blank.tex")
        end
    end
    self.health_bg:SetPosition(0, -10, 0)
    self.health_bg:SetSize(300, 20)
    self.health_bg:SetTint(0.3, 0.1, 0.1, 1)

    -- Health bar fill (safe)
    local health_fill_success = pcall(function()
        self.health_fill = self.root:AddChild(Image("images/ui.xml", "progressbar_fill.tex"))
    end)

    if not health_fill_success then
        self.health_fill = self.root:AddChild(Image())
        if self.health_fill.SetTexture then
            self.health_fill:SetTexture("images/ui.xml", "blank.tex")
        end
    end
    self.health_fill:SetPosition(-150, -10, 0)
    self.health_fill:SetSize(300, 18)
    self.health_fill:SetTint(0.8, 0.2, 0.2, 1)

    -- Health text
    self.health_text = self.root:AddChild(Text(CHATFONT, 14))
    self.health_text:SetPosition(0, -10, 0)
    self.health_text:SetString("2000 / 2000")
    self.health_text:SetColour(1, 1, 1, 1)

    -- Spiritual energy bar (safe)
    local energy_bg_success = pcall(function()
        self.energy_bg = self.root:AddChild(Image("images/ui.xml", "progressbar_bg.tex"))
    end)

    if not energy_bg_success then
        self.energy_bg = self.root:AddChild(Image())
        if self.energy_bg.SetTexture then
            self.energy_bg:SetTexture("images/ui.xml", "blank.tex")
        end
    end
    self.energy_bg:SetPosition(0, -30, 0)
    self.energy_bg:SetSize(250, 16)
    self.energy_bg:SetTint(0.1, 0.1, 0.3, 1)

    local energy_fill_success = pcall(function()
        self.energy_fill = self.root:AddChild(Image("images/ui.xml", "progressbar_fill.tex"))
    end)

    if not energy_fill_success then
        self.energy_fill = self.root:AddChild(Image())
        if self.energy_fill.SetTexture then
            self.energy_fill:SetTexture("images/ui.xml", "blank.tex")
        end
    end
    self.energy_fill:SetPosition(-125, -30, 0)
    self.energy_fill:SetSize(250, 14)
    self.energy_fill:SetTint(0.3, 0.6, 1.0, 1)

    self.energy_text = self.root:AddChild(Text(CHATFONT, 12))
    self.energy_text:SetPosition(0, -30, 0)
    self.energy_text:SetString("灵气: 1000/1000")
    self.energy_text:SetColour(0.7, 0.9, 1, 1)

    -- Ability cooldowns
    self.abilities_text = self.root:AddChild(Text(CHATFONT, 10))
    self.abilities_text:SetPosition(0, -45, 0)
    self.abilities_text:SetString("")
    self.abilities_text:SetColour(1, 1, 0.5, 1)
    
    self:Hide()
end)

function BossHealthBar:SetBoss(boss)
    self.boss = boss
    if boss then
        self:Show()
        self:UpdateDisplay()
    else
        self:Hide()
    end
end

function BossHealthBar:UpdateDisplay()
    if not self.boss or not self.boss:IsValid() then
        self:Hide()
        return
    end

    -- Update health
    if self.boss.components.health then
        local current = self.boss.components.health.currenthealth
        local max = self.boss.components.health.maxhealth
        local percent = current / max

        -- Update health bar
        self.health_fill:SetSize(300 * percent, 18)
        self.health_fill:SetPosition(-150 + (300 * percent / 2), -10, 0)

        -- Update text
        self.health_text:SetString(string.format("%.0f / %.0f", current, max))

        -- Color based on health
        if percent > 0.6 then
            self.health_fill:SetTint(0.8, 0.2, 0.2, 1) -- Red
        elseif percent > 0.3 then
            self.health_fill:SetTint(1.0, 0.6, 0.2, 1) -- Orange
        else
            self.health_fill:SetTint(1.0, 0.2, 0.2, 1) -- Bright red
        end

        -- Update cultivation realm based on phase
        local phase = 1
        local realm_name = "金丹期 (Golden Core)"
        if percent <= 0.75 then
            phase = 2
            realm_name = "元婴期 (Nascent Soul)"
        end
        if percent <= 0.5 then
            phase = 3
            realm_name = "化神期 (Soul Formation)"
        end
        if percent <= 0.25 then
            phase = 4
            realm_name = "炼虚期 (Void Refinement)"
        end

        self.realm_text:SetString(realm_name)
    end

    -- Update spiritual energy
    if self.boss.cultivation_brain then
        local current_energy = self.boss.cultivation_brain.spiritual_energy or 0
        local max_energy = self.boss.cultivation_brain.max_spiritual_energy or 1000
        local energy_percent = current_energy / max_energy

        -- Update energy bar
        self.energy_fill:SetSize(250 * energy_percent, 14)
        self.energy_fill:SetPosition(-125 + (250 * energy_percent / 2), -30, 0)

        -- Update energy text
        self.energy_text:SetString(string.format("灵气: %.0f/%.0f", current_energy, max_energy))

        -- Update ability cooldowns
        local abilities_status = {}
        if self.boss.cultivation_brain.abilities then
            local current_time = GetTime()
            for name, ability in pairs(self.boss.cultivation_brain.abilities) do
                local time_left = math.max(0, ability.cooldown - (current_time - ability.last_used))
                if time_left > 0 then
                    table.insert(abilities_status, string.format("%s: %.1fs", name, time_left))
                else
                    table.insert(abilities_status, string.format("%s: Ready", name))
                end
            end
        end
        self.abilities_text:SetString(table.concat(abilities_status, " | "))
    end
end

function BossHealthBar:OnUpdate(dt)
    if self.visible and self.boss then
        self:UpdateDisplay()
    end
end

function BossHealthBar:Show()
    self.visible = true
    self:SetPosition(0, 0, 0)
end

function BossHealthBar:Hide()
    self.visible = false
    self:SetPosition(0, -1000, 0)
end

return BossHealthBar
