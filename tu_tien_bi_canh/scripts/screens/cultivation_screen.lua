-- Cultivation Main UI Screen
-- Main interface for the cultivation mod

local Screen = require "widgets/screen"
local Widget = require "widgets/widget"
local Image = require "widgets/image"
local Text = require "widgets/text"
local Button = require "widgets/button"
local ImageButton = require "widgets/imagebutton"

local CultivationScreen = Class(Screen, function(self, owner)
    Screen._ctor(self, "CultivationScreen")
    
    self.owner = owner
    self.current_tab = "cultivation"
    
    -- Create main UI container
    self.root = self:AddChild(Widget("ROOT"))
    self.root:SetVAnchor(ANCHOR_MIDDLE)
    self.root:SetHAnchor(ANCHOR_MIDDLE)
    self.root:SetPosition(0, 0)
    
    -- Main panel background
    self.bg = self.root:AddChild(Image("images/ui/cultivation_ui/main_panel.xml", "main_panel.tex"))
    self.bg:SetPosition(0, 0)
    self.bg:SetScale(1, 1)
    
    -- Create tab system
    self:CreateTabs()
    
    -- Create content areas
    self:CreateContent<PERSON>reas()
    
    -- Show default tab
    self:ShowTab("cultivation")
    
    -- Close button
    self.close_button = self.root:AddChild(Button())
    self.close_button:SetText("X")
    self.close_button:SetPosition(400, 300)
    self.close_button:SetOnClick(function() self:Close() end)
end)

function CultivationScreen:CreateTabs()
    self.tabs = {}
    self.tab_buttons = {}
    
    local tab_data = {
        {id = "cultivation", name = GetUIText("cultivation_tab") or "Tu Luyện", x = -240},
        {id = "spells", name = GetUIText("spells_tab") or "Pháp Thuật", x = -120},
        {id = "sect", name = GetUIText("sect_tab") or "Tông Môn", x = 0},
        {id = "quests", name = GetUIText("quests_tab") or "Nhiệm Vụ", x = 120},
        {id = "alchemy", name = GetUIText("alchemy_tab") or "Luyện Đan", x = 240},
    }
    
    for _, tab in ipairs(tab_data) do
        -- Tab button
        local button = self.root:AddChild(Button())
        button:SetText(tab.name)
        button:SetPosition(tab.x, 280)
        button:SetOnClick(function() self:ShowTab(tab.id) end)
        
        self.tab_buttons[tab.id] = button
    end
end

function CultivationScreen:CreateContentAreas()
    -- Content container
    self.content = self.root:AddChild(Widget("CONTENT"))
    self.content:SetPosition(0, 0)
    
    -- Create individual tab content
    self:CreateCultivationTab()
    self:CreateSpellsTab()
    self:CreateSectTab()
    self:CreateQuestsTab()
    self:CreateAlchemyTab()
end

function CultivationScreen:CreateCultivationTab()
    self.tabs.cultivation = self.content:AddChild(Widget("CULTIVATION_TAB"))
    
    -- Level display
    self.level_text = self.tabs.cultivation:AddChild(Text(CHATFONT, 32))
    self.level_text:SetPosition(0, 200)
    self.level_text:SetColour(1, 1, 1, 1)
    
    -- Experience bar background
    self.exp_bg = self.tabs.cultivation:AddChild(Image("images/ui/cultivation_ui/progress_bars.xml", "exp_bg.tex"))
    self.exp_bg:SetPosition(0, 150)
    
    -- Experience bar fill
    self.exp_fill = self.tabs.cultivation:AddChild(Image("images/ui/cultivation_ui/progress_bars.xml", "exp_fill.tex"))
    self.exp_fill:SetPosition(0, 150)
    
    -- Experience text
    self.exp_text = self.tabs.cultivation:AddChild(Text(CHATFONT, 24))
    self.exp_text:SetPosition(0, 150)
    self.exp_text:SetColour(1, 1, 1, 1)
    
    -- Spiritual energy bar
    self.energy_bg = self.tabs.cultivation:AddChild(Image("images/ui/cultivation_ui/progress_bars.xml", "energy_bg.tex"))
    self.energy_bg:SetPosition(0, 100)
    
    self.energy_fill = self.tabs.cultivation:AddChild(Image("images/ui/cultivation_ui/progress_bars.xml", "energy_fill.tex"))
    self.energy_fill:SetPosition(0, 100)
    
    self.energy_text = self.tabs.cultivation:AddChild(Text(CHATFONT, 24))
    self.energy_text:SetPosition(0, 100)
    self.energy_text:SetColour(0.5, 0.8, 1, 1)
    
    -- Meditation button
    self.meditate_button = self.tabs.cultivation:AddChild(Button())
    self.meditate_button:SetText(GetUIText("meditation") or "Minh Tưởng")
    self.meditate_button:SetPosition(0, 0)
    self.meditate_button:SetOnClick(function() self:StartMeditation() end)
    
    -- Hide by default
    self.tabs.cultivation:Hide()
end

function CultivationScreen:CreateSpellsTab()
    self.tabs.spells = self.content:AddChild(Widget("SPELLS_TAB"))
    
    -- Spell grid (4x4)
    self.spell_icons = {}
    local spell_list = {
        "metal_spike", "metal_rain", "wood_bind", "wood_growth",
        "water_shield", "water_prison", "fire_ball", "fire_tornado", 
        "earth_spike", "earth_armor", "lightning_strike", "void_step",
        "gravity_well", "time_slow", "divine_shield", "soul_burn"
    }
    
    for i, spell_id in ipairs(spell_list) do
        local row = math.floor((i-1) / 4)
        local col = (i-1) % 4
        local x = (col - 1.5) * 80
        local y = (1.5 - row) * 80
        
        -- Spell icon button
        local icon = self.tabs.spells:AddChild(ImageButton())
        icon:SetPosition(x, y)
        icon:SetOnClick(function() self:CastSpell(spell_id) end)
        
        -- Try to set spell icon texture
        local success = pcall(function()
            icon:SetTextures("images/ui/spell_icons/core_spells.xml", spell_id .. ".tex", spell_id .. "_hover.tex")
        end)
        
        if not success then
            -- Fallback to text button
            local text_button = self.tabs.spells:AddChild(Button())
            text_button:SetText(GetSpellName(spell_id) or spell_id)
            text_button:SetPosition(x, y)
            text_button:SetOnClick(function() self:CastSpell(spell_id) end)
            icon = text_button
        end
        
        self.spell_icons[spell_id] = icon
    end
    
    -- Hide by default
    self.tabs.spells:Hide()
end

function CultivationScreen:CreateSectTab()
    self.tabs.sect = self.content:AddChild(Widget("SECT_TAB"))
    
    -- Sect info will be added here
    local placeholder = self.tabs.sect:AddChild(Text(CHATFONT, 24))
    placeholder:SetString("Sect System - Coming Soon")
    placeholder:SetPosition(0, 0)
    
    self.tabs.sect:Hide()
end

function CultivationScreen:CreateQuestsTab()
    self.tabs.quests = self.content:AddChild(Widget("QUESTS_TAB"))
    
    -- Quest list will be added here
    local placeholder = self.tabs.quests:AddChild(Text(CHATFONT, 24))
    placeholder:SetString("Quest System - Coming Soon")
    placeholder:SetPosition(0, 0)
    
    self.tabs.quests:Hide()
end

function CultivationScreen:CreateAlchemyTab()
    self.tabs.alchemy = self.content:AddChild(Widget("ALCHEMY_TAB"))
    
    -- Alchemy interface will be added here
    local placeholder = self.tabs.alchemy:AddChild(Text(CHATFONT, 24))
    placeholder:SetString("Alchemy System - Coming Soon")
    placeholder:SetPosition(0, 0)
    
    self.tabs.alchemy:Hide()
end

function CultivationScreen:ShowTab(tab_id)
    -- Hide all tabs
    for id, tab in pairs(self.tabs) do
        tab:Hide()
    end
    
    -- Show selected tab
    if self.tabs[tab_id] then
        self.tabs[tab_id]:Show()
        self.current_tab = tab_id
    end
    
    -- Update tab button states
    for id, button in pairs(self.tab_buttons) do
        if id == tab_id then
            button:SetColour(1, 1, 1, 1) -- Active
        else
            button:SetColour(0.7, 0.7, 0.7, 1) -- Inactive
        end
    end
    
    -- Refresh tab content
    self:RefreshCurrentTab()
end

function CultivationScreen:RefreshCurrentTab()
    if self.current_tab == "cultivation" then
        self:RefreshCultivationTab()
    elseif self.current_tab == "spells" then
        self:RefreshSpellsTab()
    end
end

function CultivationScreen:RefreshCultivationTab()
    if not self.owner or not self.owner.components.cultivation then
        return
    end
    
    local cultivation = self.owner.components.cultivation
    
    -- Update level
    local level_name = cultivation:GetLevelName()
    self.level_text:SetString(level_name)
    
    -- Update experience
    local exp = cultivation.experience
    local exp_required = cultivation:GetExpRequiredForNextLevel()
    local exp_progress = exp_required > 0 and (exp / exp_required) or 1
    
    self.exp_fill:SetScale(exp_progress, 1)
    self.exp_text:SetString(string.format("%d / %d", exp, exp_required))
    
    -- Update spiritual energy
    local energy = cultivation.spiritual_energy
    local max_energy = cultivation.max_spiritual_energy
    local energy_progress = energy / max_energy
    
    self.energy_fill:SetScale(energy_progress, 1)
    self.energy_text:SetString(string.format("%d / %d", energy, max_energy))
end

function CultivationScreen:RefreshSpellsTab()
    -- Update spell availability and cooldowns
    for spell_id, icon in pairs(self.spell_icons) do
        if self.owner and self.owner.components.spell_caster then
            local can_cast = self.owner.components.spell_caster:CanCastSpell(spell_id)
            if can_cast then
                icon:SetColour(1, 1, 1, 1) -- Available
            else
                icon:SetColour(0.5, 0.5, 0.5, 1) -- Unavailable
            end
        end
    end
end

function CultivationScreen:StartMeditation()
    if self.owner and self.owner.components.cultivation then
        self.owner.components.cultivation:StartMeditation()
        self:RefreshCultivationTab()
    end
end

function CultivationScreen:CastSpell(spell_id)
    if self.owner and self.owner.components.spell_caster then
        self.owner.components.spell_caster:CastSpell(spell_id)
        self:RefreshSpellsTab()
    end
end

function CultivationScreen:Close()
    TheFrontEnd:PopScreen(self)
end

return CultivationScreen
