-- Cultivation Portal Prefab
-- Portal that leads to secret cultivation realms

local assets = {
    -- Use simple existing DST assets
    Asset("ANIM", "anim/campfire.zip"),
}

local prefabs = {
    -- Use existing DST prefabs for effects
    "teleport_fx",
    "lightning_rod_fx",
}

-- Portal component for handling realm entry
local function MakeCultivationPortal(inst)
    inst:AddComponent("cultivation_portal")
end

local function OnPortalActivated(inst, data)
    -- Visual effects when portal opens
    local fx = SpawnPrefab("cultivation_portal_fx")
    if fx then
        fx.Transform:SetPosition(inst.Transform:GetWorldPosition())
    end
    
    -- Sound effect
    if inst.SoundEmitter then
        inst.SoundEmitter:PlaySound("cultivation/portal_open")
    end
end

local function OnPortalDeactivated(inst, data)
    -- Sound effect
    if inst.SoundEmitter then
        inst.SoundEmitter:PlaySound("cultivation/portal_close")
    end
end

local function OnPlayerEntered(inst, data)
    if data and data.player then
        -- Track player entry for realm manager
        if TheWorld and TheWorld.components.realm_manager then
            -- Find this portal in active realms and record player entry
            local active_realms = TheWorld.components.realm_manager:GetActiveRealms()
            for _, realm in ipairs(active_realms) do
                if realm.portal == inst then
                    table.insert(realm.players_entered, data.player)
                    break
                end
            end
        end
    end
end

local function GetStatus(inst)
    if inst.components.cultivation_portal then
        local portal = inst.components.cultivation_portal
        if portal:IsActive() then
            local realm_name = portal:GetRealmDisplayName()
            local time_left = portal:GetTimeRemaining()
            if time_left > 1 then
                local message = GetMessage("portal_closes_in_days", {realm = realm_name, days = math.ceil(time_left)}) or string.format("%s (closes in %d days)", realm_name, math.ceil(time_left))
                return message
            else
                local message = GetMessage("portal_closing_soon", {realm = realm_name}) or realm_name .. " (closing soon)"
                return message
            end
        else
            return GetMessage("portal_inactive") or "Realm Portal (Inactive)"
        end
    end
    return GetMessage("portal_default") or "Realm Portal"
end

local function OnExamined(inst, examiner)
    if inst.components.cultivation_portal then
        local portal = inst.components.cultivation_portal
        if portal:IsActive() then
            local realm_type = portal.realm_type
            return GetRealmDescription(realm_type) or GetMessage("mysterious_realm") or "Mysterious cultivation realm."
        else
            return GetMessage("portal_to_unknown") or "A mysterious door to unknown realm."
        end
    end
    return GetMessage("what_is_this") or "What is this?"
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddMiniMapEntity()
    inst.entity:AddLight()
    inst.entity:AddNetwork()

    -- Physics
    MakeObstaclePhysics(inst, 1.5)

    -- Animation - use simple campfire (guaranteed to exist)
    inst.AnimState:SetBank("campfire")
    inst.AnimState:SetBuild("campfire")
    inst.AnimState:PlayAnimation("embers")
    inst.AnimState:SetLightOverride(0.5)

    -- Make it look mystical
    inst.AnimState:SetMultColour(0.5, 0.8, 1.0, 1.0) -- Blue tint for mystical effect

    -- Minimap
    inst.MiniMapEntity:SetIcon("cultivation_portal.png")
    inst.MiniMapEntity:SetPriority(5)

    -- Light
    inst.Light:SetFalloff(0.7)
    inst.Light:SetIntensity(0.8)
    inst.Light:SetRadius(6)
    inst.Light:SetColour(0.8, 0.9, 1.0) -- Spiritual blue-white light
    inst.Light:Enable(false) -- Start disabled

    -- Tags
    inst:AddTag("structure")
    inst:AddTag("cultivation_portal")
    inst:AddTag("antlion_sinkhole_blocker")

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable.getstatus = GetStatus
    inst.components.inspectable:RecordViews()

    -- Make it examinable
    inst:AddComponent("examiner")
    inst.OnExamined = OnExamined

    -- Network
    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Server-side components
    MakeCultivationPortal(inst)

    -- Health (can be destroyed by strong attacks)
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(500)
    inst.components.health.nofadeout = true

    -- Combat (can be attacked)
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(0)
    inst.components.combat:SetRetargetFunction(1, function() return nil end) -- Don't retaliate

    -- Loot (drops spiritual materials when destroyed)
    inst:AddComponent("lootdropper")
    inst.components.lootdropper:SetLoot({"spiritual_stone", "spiritual_stone"})

    -- Work (can be hammered down)
    inst:AddComponent("workable")
    inst.components.workable:SetWorkAction(ACTIONS.HAMMER)
    inst.components.workable:SetWorkLeft(4)
    inst.components.workable:SetOnFinishCallback(function(inst, worker)
        if inst.components.lootdropper then
            inst.components.lootdropper:DropLoot()
        end
        inst:Remove()
    end)

    -- Periodic effects
    inst:DoPeriodicTask(2, function()
        if inst.components.cultivation_portal and inst.components.cultivation_portal:IsActive() then
            -- Spawn spiritual energy particles
            local fx = SpawnPrefab("spiritual_energy_fx")
            if fx then
                local x, y, z = inst.Transform:GetWorldPosition()
                fx.Transform:SetPosition(x + math.random(-2, 2), y + 1, z + math.random(-2, 2))
            end
        end
    end)

    -- Event listeners
    inst:ListenForEvent("portal_activated", OnPortalActivated)
    inst:ListenForEvent("portal_deactivated", OnPortalDeactivated)
    inst:ListenForEvent("player_entered_realm", OnPlayerEntered)

    -- Ambient sound
    inst:DoTaskInTime(1, function()
        if inst.components.cultivation_portal and inst.components.cultivation_portal:IsActive() then
            inst.SoundEmitter:PlaySound("cultivation/portal_ambient", "portal_loop")
        end
    end)

    return inst
end

-- Portal component definition
local CultivationPortal = Class(function(self, inst)
    self.inst = inst
    self.realm_type = nil
    self.is_active = false
    self.duration = 0
    self.spawn_time = 0
    self.players_inside = {}
end)

function CultivationPortal:SetRealmType(realm_type)
    self.realm_type = realm_type
    self:Activate()

    -- Generate realm content using the realm system
    if GLOBAL.CULTIVATION_REALMS and GLOBAL.CULTIVATION_REALMS[realm_type] then
        local realm_data = GLOBAL.CULTIVATION_REALMS[realm_type]
        if realm_data.generate then
            realm_data.generate(self.inst)
        end
    end
end

function CultivationPortal:Activate()
    if self.is_active then
        return
    end
    
    self.is_active = true
    self.spawn_time = GetTime()
    
    -- Change animation - brighter fire for active state
    self.inst.AnimState:PlayAnimation("hi")
    self.inst.AnimState:PushAnimation("hi", true)

    -- Brighter mystical effect when active
    self.inst.AnimState:SetMultColour(0.3, 1.0, 1.0, 1.0) -- Brighter blue
    
    -- Enable light
    if self.inst.Light then
        self.inst.Light:Enable(true)
    end
    
    -- Add interaction
    self.inst:AddComponent("activatable")
    self.inst.components.activatable.OnActivate = function(inst, doer)
        self:OnPlayerActivate(doer)
    end
    self.inst.components.activatable.quickaction = true

    -- Set interaction text based on realm type
    local realm_name = GetRealmName(self.realm_type) or self.realm_type
    local interact_text = GetMessage("enter_realm_prompt", {realm = realm_name}) or "Enter " .. realm_name
    self.inst.components.activatable:SetText(interact_text)
    
    -- Start ambient sound
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("cultivation/portal_ambient", "portal_loop")
    end
    
    self.inst:PushEvent("portal_activated")
end

function CultivationPortal:Deactivate()
    if not self.is_active then
        return
    end

    self.is_active = false

    -- Cleanup realm content using the realm system
    if GLOBAL.CULTIVATION_REALMS and GLOBAL.CULTIVATION_REALMS[self.realm_type] then
        local realm_data = GLOBAL.CULTIVATION_REALMS[self.realm_type]
        if realm_data.cleanup then
            realm_data.cleanup(self.inst)
        end
    end

    -- Eject all players
    self:EjectAllPlayers()

    -- Change animation - back to dim embers
    self.inst.AnimState:PlayAnimation("embers")
    self.inst.AnimState:PushAnimation("embers", true)

    -- Back to dim mystical effect
    self.inst.AnimState:SetMultColour(0.5, 0.8, 1.0, 1.0) -- Dim blue

    -- Disable light
    if self.inst.Light then
        self.inst.Light:Enable(false)
    end

    -- Remove interaction
    self.inst:RemoveComponent("activatable")

    -- Stop ambient sound
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:KillSound("portal_loop")
    end

    self.inst:PushEvent("portal_deactivated")
end

function CultivationPortal:OnPlayerActivate(player)
    if not self.is_active or not player then
        return
    end
    
    -- Check if player meets requirements
    if not self:CanPlayerEnter(player) then
        if player.components.talker then
            local message = GetMessage("cultivation_insufficient_for_realm") or "Cultivation insufficient to enter this realm."
            player.components.talker:Say(message, 2)
        end
        return
    end
    
    -- Enter the realm
    self:TransportPlayerToRealm(player)
end

function CultivationPortal:CanPlayerEnter(player)
    if not player.components.cultivation then
        return false
    end
    
    -- Level requirements for different realms
    local level_requirements = {
        peach_blossom = 1,
        heavenly_mountain = 2,
        medicine_valley = 2,
        fire_realm = 3,
        moon_realm = 3,
        thunder_realm = 4,
        ancient_battlefield = 4,
    }
    
    local required_level = level_requirements[self.realm_type] or 1
    return player.components.cultivation.level >= required_level
end

function CultivationPortal:TransportPlayerToRealm(player)
    -- Enhanced visual effects for realm entry

    -- Screen flash effect
    if player == ThePlayer then
        TheFrontEnd:Fade(false, 0.3, function()
            TheFrontEnd:Fade(true, 0.3)
        end)
    end

    -- Particle effect at player position
    local player_pos = player:GetPosition()
    local fx = SpawnPrefab("teleport_fx")
    if fx then
        fx.Transform:SetPosition(player_pos:Get())
    end

    -- Sound effect - use existing game sounds
    if player.SoundEmitter then
        player.SoundEmitter:PlaySound("dontstarve/common/teleportato/teleportato_teleport")
    end

    -- Portal sound
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("dontstarve/common/together/celestial_orb/active")
    end

    -- Add player to realm tracking
    table.insert(self.players_inside, player)

    -- Generate realm content around portal
    self:GenerateRealmContent()

    -- Apply realm-specific effects
    self:ApplyRealmEffects(player)

    -- Add realm status to player
    if not player.components.realm_status then
        player:AddComponent("realm_status")
    end
    player.components.realm_status:EnterRealm(self.realm_type, self.inst)

    -- Notify events
    self.inst:PushEvent("player_entered_realm", {player = player, realm_type = self.realm_type})

    -- Success message
    if player.components.talker then
        local realm_name = GetRealmName(self.realm_type) or self:GetRealmDisplayName()
        local message = GetMessage("entered_realm", {realm = realm_name}) or "Entered " .. realm_name .. "!"
        player.components.talker:Say(message, 3)
    end

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Portal] Player", player, "entered realm", self.realm_type)
    end
end

function CultivationPortal:ApplyRealmEffects(player)
    -- Apply temporary buffs/effects based on realm type
    local effects = {
        peach_blossom = function(p)
            -- Healing aura
            if p.components.health then
                p.components.health:DoDelta(20)
            end
            -- Peaceful environment - no sanity drain
            if p.components.sanity then
                p.components.sanity:SetRate(0.1)
            end
        end,
        
        heavenly_mountain = function(p)
            -- Cultivation experience bonus
            if p.components.cultivation then
                p.components.cultivation:AddExperience(10)
            end
        end,
        
        fire_realm = function(p)
            -- Fire resistance but heat damage
            if p.components.temperature then
                p.components.temperature:SetTemp(70) -- Hot environment
            end
        end,
        
        moon_realm = function(p)
            -- Sanity drain but magic power boost
            if p.components.sanity then
                p.components.sanity:DoDelta(-10)
            end
            if p.components.spell_caster then
                -- Boost spell power temporarily
            end
        end,
    }
    
    local effect_fn = effects[self.realm_type]
    if effect_fn then
        effect_fn(player)
    end
end

function CultivationPortal:EjectAllPlayers()
    for _, player in ipairs(self.players_inside) do
        if player and player:IsValid() then
            -- Teleport back to portal
            player.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
            
            if player.components.talker then
                local message = GetMessage("realm_closed_ejected") or "Realm closed, teleported out."
                player.components.talker:Say(message, 2)
            end
        end
    end
    self.players_inside = {}
end

function CultivationPortal:IsActive()
    return self.is_active
end

function CultivationPortal:GetRealmDisplayName()
    local names = {
        peach_blossom = "桃花秘境",
        heavenly_mountain = "天山秘境",
        ancient_battlefield = "古战场",
        fire_realm = "地火秘境",
        moon_realm = "阴月秘境",
        thunder_realm = "雷电秘境",
        medicine_valley = "药王谷",
    }
    return names[self.realm_type] or "未知秘境"
end

function CultivationPortal:GenerateRealmContent()
    -- Generate realm-specific content around the portal
    if self.content_generated then
        return -- Already generated
    end

    local portal_pos = self.inst:GetPosition()

    -- Call realm-specific generation function
    if self.realm_type == "peach_blossom" then
        self:GeneratePeachBlossomContent(portal_pos)
    elseif self.realm_type == "heavenly_mountain" then
        self:GenerateHeavenlyMountainContent(portal_pos)
    elseif self.realm_type == "medicine_valley" then
        self:GenerateMedicineValleyContent(portal_pos)
    elseif self.realm_type == "fire_realm" then
        self:GenerateFireRealmContent(portal_pos)
    elseif self.realm_type == "moon_realm" then
        self:GenerateMoonRealmContent(portal_pos)
    elseif self.realm_type == "thunder_realm" then
        self:GenerateThunderRealmContent(portal_pos)
    elseif self.realm_type == "ancient_battlefield" then
        self:GenerateAncientBattlefieldContent(portal_pos)
    end

    self.content_generated = true

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Portal] Generated content for", self.realm_type)
    end
end

function CultivationPortal:GeneratePeachBlossomContent(center_pos)
    -- Spawn peach blossom resources in a circle around portal
    local resources = {
        {name = "peach_blossom_flower", count = {3, 6}, chance = 0.8},
        {name = "spring_water", count = {2, 4}, chance = 0.6},
        {name = "healing_dew", count = {1, 3}, chance = 0.4},
        {name = "butterfly_essence", count = {1, 2}, chance = 0.3},
    }

    for _, resource_data in ipairs(resources) do
        if math.random() < resource_data.chance then
            local count = math.random(resource_data.count[1], resource_data.count[2])

            for i = 1, count do
                local angle = (i / count) * 2 * math.pi + math.random() * 0.5
                local dist = 4 + math.random() * 8
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist

                -- Check if position is valid
                local ground = TheWorld.Map:GetTileAtPoint(x, 0, z)
                if ground and ground ~= GROUND.IMPASSABLE then
                    local resource = SpawnPrefab(resource_data.name)
                    if resource then
                        resource.Transform:SetPosition(x, 0, z)
                        resource:AddTag("realm_resource")
                        resource:AddTag("peach_blossom_realm")

                        -- Add gentle glow effect
                        if resource.Light then
                            resource.Light:SetRadius(2)
                            resource.Light:SetIntensity(0.5)
                            resource.Light:SetFalloff(0.7)
                            resource.Light:SetColour(1, 0.8, 0.8)
                            resource.Light:Enable(true)
                        end

                        -- Store reference for cleanup
                        table.insert(self.realm_objects, resource)
                    end
                end
            end
        end
    end

    -- Spawn peaceful butterflies
    for i = 1, math.random(3, 6) do
        local angle = math.random() * 2 * math.pi
        local dist = 6 + math.random() * 10
        local x = center_pos.x + math.cos(angle) * dist
        local z = center_pos.z + math.sin(angle) * dist

        local butterfly = SpawnPrefab("butterfly")
        if butterfly then
            butterfly.Transform:SetPosition(x, 0, z)
            butterfly:AddTag("realm_creature")
            butterfly:AddTag("peach_blossom_realm")
            table.insert(self.realm_objects, butterfly)
        end
    end
end

function CultivationPortal:GenerateHeavenlyMountainContent(center_pos)
    -- Mountain realm with cultivation resources
    local resources = {
        {name = "spiritual_stone", count = {4, 8}, chance = 0.9},
        {name = "cloud_talisman_grass", count = {2, 5}, chance = 0.7},
        {name = "heaven_ginseng", count = {1, 2}, chance = 0.3},
    }

    for _, resource_data in ipairs(resources) do
        if math.random() < resource_data.chance then
            local count = math.random(resource_data.count[1], resource_data.count[2])

            for i = 1, count do
                local angle = (i / count) * 2 * math.pi + math.random() * 0.5
                local dist = 5 + math.random() * 10
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist

                local resource = SpawnPrefab(resource_data.name)
                if resource then
                    resource.Transform:SetPosition(x, 0, z)
                    resource:AddTag("realm_resource")
                    resource:AddTag("heavenly_mountain_realm")
                    table.insert(self.realm_objects, resource)
                end
            end
        end
    end
end

function CultivationPortal:GenerateMedicineValleyContent(center_pos)
    -- Healing-focused realm
    local resources = {
        {name = "healing_dew", count = {3, 6}, chance = 0.8},
        {name = "heaven_ginseng", count = {2, 4}, chance = 0.6},
        {name = "spring_water", count = {2, 4}, chance = 0.7},
    }

    for _, resource_data in ipairs(resources) do
        if math.random() < resource_data.chance then
            local count = math.random(resource_data.count[1], resource_data.count[2])

            for i = 1, count do
                local angle = (i / count) * 2 * math.pi + math.random() * 0.5
                local dist = 4 + math.random() * 8
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist

                local resource = SpawnPrefab(resource_data.name)
                if resource then
                    resource.Transform:SetPosition(x, 0, z)
                    resource:AddTag("realm_resource")
                    resource:AddTag("medicine_valley_realm")
                    table.insert(self.realm_objects, resource)
                end
            end
        end
    end
end

function CultivationPortal:GenerateFireRealmContent(center_pos)
    -- Dangerous fire realm with valuable resources
    local resources = {
        {name = "fire_crystal", count = {2, 5}, chance = 0.7},
        {name = "spiritual_stone", count = {3, 6}, chance = 0.8},
    }

    for _, resource_data in ipairs(resources) do
        if math.random() < resource_data.chance then
            local count = math.random(resource_data.count[1], resource_data.count[2])

            for i = 1, count do
                local angle = (i / count) * 2 * math.pi + math.random() * 0.5
                local dist = 6 + math.random() * 8
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist

                local resource = SpawnPrefab(resource_data.name)
                if resource then
                    resource.Transform:SetPosition(x, 0, z)
                    resource:AddTag("realm_resource")
                    resource:AddTag("fire_realm")
                    table.insert(self.realm_objects, resource)
                end
            end
        end
    end

    -- Spawn some fire hounds for danger
    for i = 1, math.random(1, 3) do
        local angle = math.random() * 2 * math.pi
        local dist = 8 + math.random() * 6
        local x = center_pos.x + math.cos(angle) * dist
        local z = center_pos.z + math.sin(angle) * dist

        local hound = SpawnPrefab("firehound")
        if hound then
            hound.Transform:SetPosition(x, 0, z)
            hound:AddTag("realm_creature")
            hound:AddTag("fire_realm")
            table.insert(self.realm_objects, hound)
        end
    end
end

function CultivationPortal:SetDuration(days)
    self.duration = days
end

function CultivationPortal:GetTimeRemaining()
    if not self.is_active then
        return 0
    end
    
    local elapsed_time = GetTime() - self.spawn_time
    local elapsed_days = elapsed_time / TUNING.TOTAL_DAY_TIME
    return math.max(0, self.duration - elapsed_days)
end

function CultivationPortal:OnSave()
    return {
        realm_type = self.realm_type,
        is_active = self.is_active,
        duration = self.duration,
        spawn_time = self.spawn_time,
    }
end

function CultivationPortal:OnLoad(data)
    if data then
        self.realm_type = data.realm_type
        self.duration = data.duration or 0
        self.spawn_time = data.spawn_time or GetTime()
        
        if data.is_active then
            self:Activate()
        end
    end
end

-- Define the component properly
local function MakeCultivationPortalComponent(inst)
    local portal = CultivationPortal(inst)
    inst.components.cultivation_portal = portal
    return portal
end

-- Override the MakeCultivationPortal function
MakeCultivationPortal = MakeCultivationPortalComponent

return Prefab("cultivation_portal", fn, assets, prefabs)
