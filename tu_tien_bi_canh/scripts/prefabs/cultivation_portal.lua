-- Cultivation Portal Prefab
-- Portal that leads to secret cultivation realms

local assets = {
    Asset("ANIM", "anim/cultivation_portal.zip"),
    <PERSON><PERSON>("SOUND", "sound/cultivation/portal_open.fsb"),
    <PERSON><PERSON>("SOUND", "sound/cultivation/portal_close.fsb"),
    <PERSON>set("SOUND", "sound/cultivation/portal_ambient.fsb"),
}

local prefabs = {
    "cultivation_portal_fx",
    "spiritual_energy_fx",
}

-- Portal component for handling realm entry
local function MakeCultivationPortal(inst)
    inst:AddComponent("cultivation_portal")
end

local function OnPortalActivated(inst, data)
    -- Visual effects when portal opens
    local fx = SpawnPrefab("cultivation_portal_fx")
    if fx then
        fx.Transform:SetPosition(inst.Transform:GetWorldPosition())
    end
    
    -- Sound effect
    if inst.SoundEmitter then
        inst.SoundEmitter:PlaySound("cultivation/portal_open")
    end
end

local function OnPortalDeactivated(inst, data)
    -- Sound effect
    if inst.SoundEmitter then
        inst.SoundEmitter:PlaySound("cultivation/portal_close")
    end
end

local function OnPlayerEntered(inst, data)
    if data and data.player then
        -- Track player entry for realm manager
        if TheWorld and TheWorld.components.realm_manager then
            -- Find this portal in active realms and record player entry
            local active_realms = TheWorld.components.realm_manager:GetActiveRealms()
            for _, realm in ipairs(active_realms) do
                if realm.portal == inst then
                    table.insert(realm.players_entered, data.player)
                    break
                end
            end
        end
    end
end

local function GetStatus(inst)
    if inst.components.cultivation_portal then
        local portal = inst.components.cultivation_portal
        if portal:IsActive() then
            local realm_name = portal:GetRealmDisplayName()
            local time_left = portal:GetTimeRemaining()
            if time_left > 1 then
                return string.format("%s (%d天后关闭)", realm_name, math.ceil(time_left))
            else
                return realm_name .. " (即将关闭)"
            end
        else
            return "秘境之门 (未激活)"
        end
    end
    return "秘境之门"
end

local function OnExamined(inst, examiner)
    if inst.components.cultivation_portal then
        local portal = inst.components.cultivation_portal
        if portal:IsActive() then
            local realm_type = portal.realm_type
            local descriptions = {
                peach_blossom = "桃花飞舞，春意盎然的仙境。",
                heavenly_mountain = "高山云雾，仙鹤翱翔的圣地。",
                ancient_battlefield = "刀光剑影，英魂不散的战场。",
                fire_realm = "烈火焚天，岩浆翻滚的炼狱。",
                moon_realm = "月华如水，阴气森森的幽境。",
                thunder_realm = "雷霆万钧，电闪雷鸣的天域。",
                medicine_valley = "百草丰茂，药香阵阵的仙谷。",
            }
            return descriptions[realm_type] or "神秘的修仙秘境。"
        else
            return "一扇通往未知秘境的神秘之门。"
        end
    end
    return "这是什么？"
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddMiniMapEntity()
    inst.entity:AddLight()
    inst.entity:AddNetwork()

    -- Physics
    MakeObstaclePhysics(inst, 1.5)

    -- Animation
    inst.AnimState:SetBank("cultivation_portal")
    inst.AnimState:SetBuild("cultivation_portal")
    inst.AnimState:PlayAnimation("idle_closed")
    inst.AnimState:SetLightOverride(0.3)

    -- Minimap
    inst.MiniMapEntity:SetIcon("cultivation_portal.png")
    inst.MiniMapEntity:SetPriority(5)

    -- Light
    inst.Light:SetFalloff(0.7)
    inst.Light:SetIntensity(0.8)
    inst.Light:SetRadius(6)
    inst.Light:SetColour(0.8, 0.9, 1.0) -- Spiritual blue-white light
    inst.Light:Enable(false) -- Start disabled

    -- Tags
    inst:AddTag("structure")
    inst:AddTag("cultivation_portal")
    inst:AddTag("antlion_sinkhole_blocker")

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable.getstatus = GetStatus
    inst.components.inspectable:RecordViews()

    -- Make it examinable
    inst:AddComponent("examiner")
    inst.OnExamined = OnExamined

    -- Network
    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Server-side components
    MakeCultivationPortal(inst)

    -- Health (can be destroyed by strong attacks)
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(500)
    inst.components.health.nofadeout = true

    -- Combat (can be attacked)
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(0)
    inst.components.combat:SetRetargetFunction(1, function() return nil end) -- Don't retaliate

    -- Loot (drops spiritual materials when destroyed)
    inst:AddComponent("lootdropper")
    inst.components.lootdropper:SetLoot({"spiritual_stone", "spiritual_stone"})

    -- Work (can be hammered down)
    inst:AddComponent("workable")
    inst.components.workable:SetWorkAction(ACTIONS.HAMMER)
    inst.components.workable:SetWorkLeft(4)
    inst.components.workable:SetOnFinishCallback(function(inst, worker)
        if inst.components.lootdropper then
            inst.components.lootdropper:DropLoot()
        end
        inst:Remove()
    end)

    -- Periodic effects
    inst:DoPeriodicTask(2, function()
        if inst.components.cultivation_portal and inst.components.cultivation_portal:IsActive() then
            -- Spawn spiritual energy particles
            local fx = SpawnPrefab("spiritual_energy_fx")
            if fx then
                local x, y, z = inst.Transform:GetWorldPosition()
                fx.Transform:SetPosition(x + math.random(-2, 2), y + 1, z + math.random(-2, 2))
            end
        end
    end)

    -- Event listeners
    inst:ListenForEvent("portal_activated", OnPortalActivated)
    inst:ListenForEvent("portal_deactivated", OnPortalDeactivated)
    inst:ListenForEvent("player_entered_realm", OnPlayerEntered)

    -- Ambient sound
    inst:DoTaskInTime(1, function()
        if inst.components.cultivation_portal and inst.components.cultivation_portal:IsActive() then
            inst.SoundEmitter:PlaySound("cultivation/portal_ambient", "portal_loop")
        end
    end)

    return inst
end

-- Portal component definition
local CultivationPortal = Class(function(self, inst)
    self.inst = inst
    self.realm_type = nil
    self.is_active = false
    self.duration = 0
    self.spawn_time = 0
    self.players_inside = {}
end)

function CultivationPortal:SetRealmType(realm_type)
    self.realm_type = realm_type
    self:Activate()

    -- Generate realm content using the realm system
    if GLOBAL.CULTIVATION_REALMS and GLOBAL.CULTIVATION_REALMS[realm_type] then
        local realm_data = GLOBAL.CULTIVATION_REALMS[realm_type]
        if realm_data.generate then
            realm_data.generate(self.inst)
        end
    end
end

function CultivationPortal:Activate()
    if self.is_active then
        return
    end
    
    self.is_active = true
    self.spawn_time = GetTime()
    
    -- Change animation
    self.inst.AnimState:PlayAnimation("open")
    self.inst.AnimState:PushAnimation("idle_open", true)
    
    -- Enable light
    if self.inst.Light then
        self.inst.Light:Enable(true)
    end
    
    -- Add interaction
    self.inst:AddComponent("activatable")
    self.inst.components.activatable.OnActivate = function(inst, doer)
        self:OnPlayerActivate(doer)
    end
    self.inst.components.activatable.quickaction = true
    
    -- Start ambient sound
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("cultivation/portal_ambient", "portal_loop")
    end
    
    self.inst:PushEvent("portal_activated")
end

function CultivationPortal:Deactivate()
    if not self.is_active then
        return
    end

    self.is_active = false

    -- Cleanup realm content using the realm system
    if GLOBAL.CULTIVATION_REALMS and GLOBAL.CULTIVATION_REALMS[self.realm_type] then
        local realm_data = GLOBAL.CULTIVATION_REALMS[self.realm_type]
        if realm_data.cleanup then
            realm_data.cleanup(self.inst)
        end
    end

    -- Eject all players
    self:EjectAllPlayers()

    -- Change animation
    self.inst.AnimState:PlayAnimation("close")
    self.inst.AnimState:PushAnimation("idle_closed", true)

    -- Disable light
    if self.inst.Light then
        self.inst.Light:Enable(false)
    end

    -- Remove interaction
    self.inst:RemoveComponent("activatable")

    -- Stop ambient sound
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:KillSound("portal_loop")
    end

    self.inst:PushEvent("portal_deactivated")
end

function CultivationPortal:OnPlayerActivate(player)
    if not self.is_active or not player then
        return
    end
    
    -- Check if player meets requirements
    if not self:CanPlayerEnter(player) then
        if player.components.talker then
            player.components.talker:Say("修为不足，无法进入此秘境。", 2)
        end
        return
    end
    
    -- Enter the realm
    self:TransportPlayerToRealm(player)
end

function CultivationPortal:CanPlayerEnter(player)
    if not player.components.cultivation then
        return false
    end
    
    -- Level requirements for different realms
    local level_requirements = {
        peach_blossom = 1,
        heavenly_mountain = 2,
        medicine_valley = 2,
        fire_realm = 3,
        moon_realm = 3,
        thunder_realm = 4,
        ancient_battlefield = 4,
    }
    
    local required_level = level_requirements[self.realm_type] or 1
    return player.components.cultivation.level >= required_level
end

function CultivationPortal:TransportPlayerToRealm(player)
    -- For now, simulate realm entry with effects and buffs
    -- In full implementation, this would load a separate world/area
    
    -- Visual effect
    local fx = SpawnPrefab("cultivation_portal_fx")
    if fx then
        fx.Transform:SetPosition(player.Transform:GetWorldPosition())
    end
    
    -- Sound effect
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("cultivation/portal_enter")
    end
    
    -- Add player to realm (simulated)
    table.insert(self.players_inside, player)
    
    -- Apply realm-specific effects
    self:ApplyRealmEffects(player)
    
    -- Notify events
    self.inst:PushEvent("player_entered_realm", {player = player, realm_type = self.realm_type})
    
    if player.components.talker then
        player.components.talker:Say("进入了" .. self:GetRealmDisplayName() .. "！", 3)
    end
end

function CultivationPortal:ApplyRealmEffects(player)
    -- Apply temporary buffs/effects based on realm type
    local effects = {
        peach_blossom = function(p)
            -- Healing aura
            if p.components.health then
                p.components.health:DoDelta(20)
            end
            -- Peaceful environment - no sanity drain
            if p.components.sanity then
                p.components.sanity:SetRate(0.1)
            end
        end,
        
        heavenly_mountain = function(p)
            -- Cultivation experience bonus
            if p.components.cultivation then
                p.components.cultivation:AddExperience(10)
            end
        end,
        
        fire_realm = function(p)
            -- Fire resistance but heat damage
            if p.components.temperature then
                p.components.temperature:SetTemp(70) -- Hot environment
            end
        end,
        
        moon_realm = function(p)
            -- Sanity drain but magic power boost
            if p.components.sanity then
                p.components.sanity:DoDelta(-10)
            end
            if p.components.spell_caster then
                -- Boost spell power temporarily
            end
        end,
    }
    
    local effect_fn = effects[self.realm_type]
    if effect_fn then
        effect_fn(player)
    end
end

function CultivationPortal:EjectAllPlayers()
    for _, player in ipairs(self.players_inside) do
        if player and player:IsValid() then
            -- Teleport back to portal
            player.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
            
            if player.components.talker then
                player.components.talker:Say("秘境关闭，被传送了出来。", 2)
            end
        end
    end
    self.players_inside = {}
end

function CultivationPortal:IsActive()
    return self.is_active
end

function CultivationPortal:GetRealmDisplayName()
    local names = {
        peach_blossom = "桃花秘境",
        heavenly_mountain = "天山秘境",
        ancient_battlefield = "古战场",
        fire_realm = "地火秘境",
        moon_realm = "阴月秘境",
        thunder_realm = "雷电秘境",
        medicine_valley = "药王谷",
    }
    return names[self.realm_type] or "未知秘境"
end

function CultivationPortal:SetDuration(days)
    self.duration = days
end

function CultivationPortal:GetTimeRemaining()
    if not self.is_active then
        return 0
    end
    
    local elapsed_time = GetTime() - self.spawn_time
    local elapsed_days = elapsed_time / TUNING.TOTAL_DAY_TIME
    return math.max(0, self.duration - elapsed_days)
end

function CultivationPortal:OnSave()
    return {
        realm_type = self.realm_type,
        is_active = self.is_active,
        duration = self.duration,
        spawn_time = self.spawn_time,
    }
end

function CultivationPortal:OnLoad(data)
    if data then
        self.realm_type = data.realm_type
        self.duration = data.duration or 0
        self.spawn_time = data.spawn_time or GetTime()
        
        if data.is_active then
            self:Activate()
        end
    end
end

-- Define the component properly
local function MakeCultivationPortalComponent(inst)
    local portal = CultivationPortal(inst)
    inst.components.cultivation_portal = portal
    return portal
end

-- Override the MakeCultivationPortal function
MakeCultivationPortal = MakeCultivationPortalComponent

return Prefab("cultivation_portal", fn, assets, prefabs)
