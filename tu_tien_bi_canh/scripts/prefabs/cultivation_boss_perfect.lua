-- Cultivation Boss Perfect - Professional Game-Quality Boss
-- Behaves like a real creature with intelligent AI

local assets = {
    Asset("ANIM", "anim/tk_boss_zhangjiao.zip"),
    Asset("ANIM", "anim/fx.zip"),
}

local prefabs = {
    "lightning_rod_fx",
    "ice",
}

-- Boss configuration
local BOSS_HEALTH = 3000
local BOSS_DAMAGE = 80
local BOSS_SPEED = 5
local BOSS_ATTACK_RANGE = 4
local BOSS_FOLLOW_RANGE = 20
local BOSS_TARGET_RANGE = 15

local function OnAttacked(inst, data)
    local attacker = data and data.attacker
    if attacker and inst.components.combat then
        inst.components.combat:SetTarget(attacker)
        inst.components.combat:ShareTarget(attacker, 30, function(dude) 
            return dude:HasTag("cultivation_boss") and not dude.components.health:IsDead() 
        end, 5)
        
        -- Trigger hit state
        inst.sg:PushEvent("attacked")
        
        -- Chance to use ability when attacked
        if math.random() < 0.3 then
            inst:DoTaskInTime(0.5, function()
                if inst:IsValid() and inst.UseRandomAbility then
                    inst:UseRandomAbility()
                end
            end)
        end
    end
end

local function OnDeath(inst)
    -- Death announcement
    local pos = inst:GetPosition()
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"player"})
    
    for _, player in ipairs(players) do
        if player.components.talker then
            player.components.talker:Say("[古修者]: 想不到...本座竟败在凡人手中...", 5)
        end
    end
    
    -- Epic loot drops
    inst:DoTaskInTime(1, function()
        if not inst:IsValid() then return end
        
        local drops = {
            {item = "goldnugget", count = 15},
            {item = "redgem", count = 5},
            {item = "bluegem", count = 5},
            {item = "purplegem", count = 3},
            {item = "yellowgem", count = 2},
            {item = "greengem", count = 2},
            {item = "orangegem", count = 2},
        }
        
        for _, drop in ipairs(drops) do
            for i = 1, drop.count do
                local item = SpawnPrefab(drop.item)
                if item then
                    local angle = math.random() * 2 * math.pi
                    local radius = 3 + math.random() * 5
                    local x = pos.x + math.cos(angle) * radius
                    local z = pos.z + math.sin(angle) * radius
                    item.Transform:SetPosition(x, 0, z)
                    
                    -- Make drops glow
                    if item.AnimState then
                        item.AnimState:SetMultColour(1.2, 1.1, 0.8, 1.0)
                    end
                end
            end
        end
        
        -- Victory portal
        local portal = SpawnPrefab("resurrectionstone")
        if portal then
            portal.Transform:SetPosition(pos.x, 0, pos.z)
            portal.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0)
            
            if portal.components.inspectable then
                portal.components.inspectable:SetDescription("A mystical portal opened by the boss's defeat!")
            end
        end
    end)
end

local function OnPhaseTransition(inst, phase)
    -- Visual phase transition effects
    if inst.AnimState then
        inst.AnimState:SetMultColour(1.5, 1.2, 0.8, 1.0)
        inst:DoTaskInTime(1, function()
            if inst:IsValid() then
                local colors = {
                    {0.8, 0.9, 1.2, 1.0}, -- Phase 1: Blue
                    {0.9, 1.0, 0.8, 1.0}, -- Phase 2: Green
                    {1.0, 0.9, 0.7, 1.0}, -- Phase 3: Orange
                    {1.2, 0.8, 0.8, 1.0}, -- Phase 4: Red
                }
                local color = colors[phase] or colors[1]
                inst.AnimState:SetMultColour(color[1], color[2], color[3], color[4])
            end
        end)
    end
    
    -- Phase-specific enhancements
    if phase == 2 then
        -- Nascent Soul - Faster abilities
        for _, ability in pairs(inst.cultivation_data.abilities) do
            ability.cooldown = ability.cooldown * 0.8
        end
    elseif phase == 3 then
        -- Soul Formation - More damage and speed
        if inst.components.combat then
            inst.components.combat.defaultdamage = inst.components.combat.defaultdamage * 1.2
        end
        if inst.components.locomotor then
            inst.components.locomotor.walkspeed = inst.components.locomotor.walkspeed * 1.2
            inst.components.locomotor.runspeed = inst.components.locomotor.runspeed * 1.2
        end
    elseif phase == 4 then
        -- Void Refinement - Enrage mode
        inst.cultivation_data.enraged = true
        if inst.components.combat then
            inst.components.combat.defaultdamage = inst.components.combat.defaultdamage * 1.5
            inst.components.combat:SetAttackPeriod(1.5) -- Faster attacks
        end
        if inst.components.locomotor then
            inst.components.locomotor.walkspeed = inst.components.locomotor.walkspeed * 1.5
            inst.components.locomotor.runspeed = inst.components.locomotor.runspeed * 1.5
        end
        
        -- Continuous ability usage in phase 4
        inst.enrage_task = inst:DoPeriodicTask(3, function()
            if inst:IsValid() and inst.UseRandomAbility then
                inst:UseRandomAbility()
            end
        end)
    end
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddDynamicShadow()
    inst.entity:AddNetwork()

    -- Animation setup
    inst.AnimState:SetBank("tk_boss_zhangjiao")
    inst.AnimState:SetBuild("tk_boss_zhangjiao")
    inst.AnimState:PlayAnimation("idle", true)
    inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)

    -- Shadow
    inst.DynamicShadow:SetSize(3, 2)

    -- Physics
    MakeCharacterPhysics(inst, 1000, 3)

    -- Tags
    inst:AddTag("epic")
    inst:AddTag("monster")
    inst:AddTag("hostile")
    inst:AddTag("cultivation_boss")
    inst:AddTag("largecreature")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Health
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(BOSS_HEALTH)
    inst.components.health.nofadeout = true

    -- Combat
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(BOSS_DAMAGE)
    inst.components.combat:SetAttackPeriod(2)
    inst.components.combat:SetRange(BOSS_ATTACK_RANGE)
    inst.components.combat:SetRetargetFunction(3, function(inst)
        return FindEntity(inst, BOSS_TARGET_RANGE, function(guy)
            return guy:HasTag("player") 
                and inst.components.combat:CanTarget(guy)
                and not guy:HasTag("notarget")
        end)
    end)
    inst.components.combat:SetKeepTargetFunction(function(inst, target)
        return target and target:IsValid() and target:HasTag("player")
            and inst:GetDistanceSqToInst(target) < BOSS_FOLLOW_RANGE * BOSS_FOLLOW_RANGE
    end)

    -- Locomotor
    inst:AddComponent("locomotor")
    inst.components.locomotor.walkspeed = BOSS_SPEED
    inst.components.locomotor.runspeed = BOSS_SPEED * 1.5

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("An ancient cultivation master of immense power!")

    -- Loot dropper
    inst:AddComponent("lootdropper")

    -- Cultivation data
    inst.cultivation_data = {
        realm = "金丹期 (Golden Core)",
        phase = 1,
        spiritual_energy = 1000,
        max_spiritual_energy = 1000,
        last_taunt = 0,
        enraged = false,
        abilities = {
            thunder_strike = {cooldown = 12, last_used = 0},
            vajra_protection = {cooldown = 20, last_used = 0},
            sword_control = {cooldown = 8, last_used = 0},
            clone_technique = {cooldown = 25, last_used = 0}
        }
    }

    -- Phase transition callback
    inst.OnPhaseTransition = OnPhaseTransition

    -- Professional AI Brain
    inst:SetBrain(require("brains/cultivation_boss_brain"))
    inst:SetStateGraph("SGcultivation_boss")

    -- Event listeners
    inst:ListenForEvent("attacked", OnAttacked)
    inst:ListenForEvent("death", OnDeath)

    -- Advanced Abilities
    function inst:UseRandomAbility()
        if not self.cultivation_data or not self.cultivation_data.abilities then
            return false
        end

        local current_time = GetTime()
        local available_abilities = {}

        for name, ability in pairs(self.cultivation_data.abilities) do
            if (current_time - ability.last_used) >= ability.cooldown then
                table.insert(available_abilities, name)
            end
        end

        if #available_abilities > 0 then
            local ability_name = available_abilities[math.random(#available_abilities)]
            self.cultivation_data.abilities[ability_name].last_used = current_time

            if ability_name == "thunder_strike" then
                self.sg:GoToState("ability_thunder")
            elseif ability_name == "vajra_protection" then
                self.sg:GoToState("ability_protection")
            elseif ability_name == "sword_control" then
                self:CastSwordControl()
            elseif ability_name == "clone_technique" then
                self:CastCloneTechnique()
            end

            return true
        end

        return false
    end

    function inst:CastThunderStrike()
        print("[Boss] Casts 雷霆万钧 (Thunder Strike)")
        local pos = self:GetPosition()
        local targets = TheSim:FindEntities(pos.x, pos.y, pos.z, 10, {"player"})

        for _, target in ipairs(targets) do
            if target and target:IsValid() and target.components.combat then
                local damage = 120 * (self.cultivation_data.enraged and 1.5 or 1)
                target.components.combat:GetAttacked(self, damage)

                -- Lightning effect
                if target.AnimState then
                    target.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0)
                    target:DoTaskInTime(0.5, function()
                        if target:IsValid() then
                            target.AnimState:SetMultColour(1, 1, 1, 1)
                        end
                    end)
                end
            end
        end
    end

    function inst:CastVajraProtection()
        print("[Boss] Casts 金刚护体 (Vajra Protection)")
        if self.components.combat then
            local old_mult = self.components.combat.damagemultiplier or 1
            self.components.combat.damagemultiplier = old_mult * 0.3 -- 70% damage reduction

            -- Remove after 12 seconds
            self:DoTaskInTime(12, function()
                if self:IsValid() and self.components.combat then
                    self.components.combat.damagemultiplier = old_mult
                end
            end)
        end
    end

    function inst:CastSwordControl()
        print("[Boss] Casts 御剑术 (Sword Control)")
        local target = self.components.combat.target
        if target and target:IsValid() then
            -- Multiple sword strikes
            for i = 1, 3 do
                self:DoTaskInTime(i * 0.4, function()
                    if target:IsValid() and target.components.combat then
                        target.components.combat:GetAttacked(self, 60)

                        -- Sword effect
                        if target.AnimState then
                            target.AnimState:SetMultColour(0.8, 0.8, 1.2, 1.0)
                            target:DoTaskInTime(0.3, function()
                                if target:IsValid() then
                                    target.AnimState:SetMultColour(1, 1, 1, 1)
                                end
                            end)
                        end
                    end
                end)
            end
        end
    end

    function inst:CastCloneTechnique()
        print("[Boss] Casts 分身术 (Clone Technique)")
        -- Create illusion attacks
        local target = self.components.combat.target
        if target and target:IsValid() then
            for i = 1, 4 do
                self:DoTaskInTime(i * 0.3, function()
                    if target:IsValid() and target.components.combat then
                        target.components.combat:GetAttacked(self, 40)

                        -- Clone effect
                        if target.AnimState then
                            target.AnimState:SetMultColour(0.6, 0.7, 1.0, 1.0)
                            target:DoTaskInTime(0.2, function()
                                if target:IsValid() then
                                    target.AnimState:SetMultColour(1, 1, 1, 1)
                                end
                            end)
                        end
                    end
                end)
            end
        end
    end

    -- Spiritual energy regeneration
    inst:DoPeriodicTask(2, function()
        if inst.cultivation_data then
            local regen = inst.cultivation_data.enraged and 50 or 30
            inst.cultivation_data.spiritual_energy = math.min(
                inst.cultivation_data.spiritual_energy + regen,
                inst.cultivation_data.max_spiritual_energy
            )
        end
    end)

    return inst
end

return Prefab("cultivation_boss_perfect", fn, assets, prefabs)
