-- Cultivation Boss - Clean Implementation
-- Using assets from Three Kingdoms mod

local assets = {
    Asset("ANIM", "anim/tk_boss_zhangjiao.zip"),
    Asset("ANIM", "anim/fx.zip"),
}

local prefabs = {
    "lightning_rod_fx",
}

-- Boss stats
local BOSS_HEALTH = 2000
local BOSS_DAMAGE = 60
local BOSS_SPEED = 4

local function OnAttacked(inst, data)
    local attacker = data and data.attacker
    if attacker and inst.components.combat then
        inst.components.combat:SetTarget(attacker)

        -- Flash red when hit
        if inst.AnimState then
            inst.AnimState:SetMultColour(1.0, 0.5, 0.5, 1.0)
            inst:DoTaskInTime(0.3, function()
                if inst:IsValid() then
                    inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0) -- Back to blue
                end
            end)
        end

        -- Wake up and become aggressive
        if inst.components.sleeper and inst.components.sleeper:IsAsleep() then
            inst.components.sleeper:WakeUp()
        end

        -- Taunt when hit
        if math.random() < 0.3 then
            local taunts = {
                "You dare challenge the ancient guardian!",
                "Your cultivation is weak!",
                "Feel the power of true mastery!"
            }
            local taunt = taunts[math.random(#taunts)]

            -- Show taunt to nearby players
            local pos = inst:GetPosition()
            local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 15, {"player"})
            for _, player in ipairs(players) do
                if player.components.talker then
                    player.components.talker:Say("[Guardian]: " .. taunt, 3)
                end
            end
        end

        print("[Boss] Attacked by", attacker.name or "unknown", "- Health:", inst.components.health.currenthealth)
    end
end

local function OnDeath(inst)
    print("Cultivation Boss defeated!")
    
    -- Death message
    local pos = inst:GetPosition()
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 20, {"player"})
    for _, player in ipairs(players) do
        if player.components.talker then
            player.components.talker:Say("[Guardian]: Perhaps... you have potential...", 4)
        end
    end
    
    -- Drop rewards
    local drops = {
        {item = "goldnugget", count = 8},
        {item = "redgem", count = 3},
        {item = "bluegem", count = 3},
        {item = "purplegem", count = 2},
    }
    
    for _, drop in ipairs(drops) do
        for i = 1, drop.count do
            local item = SpawnPrefab(drop.item)
            if item then
                local angle = math.random() * 2 * math.pi
                local radius = 2 + math.random() * 4
                local x = pos.x + math.cos(angle) * radius
                local z = pos.z + math.sin(angle) * radius
                item.Transform:SetPosition(x, 0, z)
                
                -- Make drops glow
                if item.AnimState then
                    item.AnimState:SetMultColour(0.8, 0.9, 1.0, 1.0)
                end
            end
        end
    end
    
    -- Victory portal
    local portal = SpawnPrefab("campfire")
    if portal then
        portal.Transform:SetPosition(pos.x, 0, pos.z)
        portal.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0) -- Golden
        
        if portal.components.inspectable then
            portal.components.inspectable:SetDescription("A golden portal opened by victory!")
        end
    end
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    -- Use Three Kingdoms boss animation
    inst.AnimState:SetBank("tk_boss_zhangjiao")
    inst.AnimState:SetBuild("tk_boss_zhangjiao")
    inst.AnimState:PlayAnimation("idle", true)

    -- Make it mystical blue
    inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)

    -- Safe animation handling - no complex API calls
    local function SafeUpdateAnimation()
        if not inst or not inst:IsValid() or not inst.AnimState then
            return
        end

        -- Simple animation - just keep idle running
        local success = pcall(function()
            if not inst.AnimState:IsCurrentAnimation("idle") then
                inst.AnimState:PlayAnimation("idle", true)
            end
        end)

        if not success then
            print("[Boss] Animation error - using fallback")
        end
    end

    -- Update animation safely
    inst:DoPeriodicTask(2, SafeUpdateAnimation)

    -- Physics
    MakeCharacterPhysics(inst, 1000, 2)

    -- Tags
    inst:AddTag("epic")
    inst:AddTag("monster")
    inst:AddTag("hostile")
    inst:AddTag("cultivation_boss")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Health
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(BOSS_HEALTH)
    inst.components.health:StartRegen(1, 2) -- Regen 1 HP every 2 seconds

    -- Combat
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(BOSS_DAMAGE)
    inst.components.combat:SetAttackPeriod(2)
    inst.components.combat:SetRange(3)
    inst.components.combat:SetRetargetFunction(3, function(inst)
        return FindEntity(inst, 10, function(guy)
            return guy:HasTag("player") and inst.components.combat:CanTarget(guy)
        end)
    end)
    inst.components.combat:SetKeepTargetFunction(function(inst, target)
        return target and target:IsValid() and target:HasTag("player")
    end)

    -- Attack animation with safe API calls
    local function OnAttackOther(inst, data)
        local target = data and data.target
        if target then
            -- Try attack animation, fallback to flash effect
            local success = pcall(function()
                inst.AnimState:PlayAnimation("attack")
                inst.AnimState:PushAnimation("idle", true)
            end)
            if not success then
                -- Flash effect if no attack animation
                inst.AnimState:SetMultColour(1.2, 1.0, 0.8, 1.0)
                inst:DoTaskInTime(0.3, function()
                    if inst:IsValid() then
                        inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
                    end
                end)
            end
        end
    end

    -- Use proper event listener instead of SetOnAttack
    inst:ListenForEvent("onattackother", OnAttackOther)

    -- Locomotor
    inst:AddComponent("locomotor")
    inst.components.locomotor.walkspeed = BOSS_SPEED
    inst.components.locomotor.runspeed = BOSS_SPEED * 1.5

    -- Sleeper (for AI)
    inst:AddComponent("sleeper")
    inst.components.sleeper:SetResistance(3)

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("An ancient cultivation master, fallen to darkness!")

    -- Simple cultivation data (no complex brain)
    inst.cultivation_data = {
        realm = "金丹期 (Golden Core)",
        phase = 1,
        spiritual_energy = 1000,
        max_spiritual_energy = 1000,
        abilities = {
            vajra_protection = {cooldown = 20, last_used = 0},
            thunder_strike = {cooldown = 15, last_used = 0},
            sword_control = {cooldown = 8, last_used = 0},
            clone_technique = {cooldown = 30, last_used = 0}
        }
    }

    -- Ultra-safe AI function with full validation
    local function UltraSafeAI()
        -- Validate everything before proceeding
        if not inst or not inst:IsValid() then
            return
        end

        if not inst.components or not inst.components.combat then
            return
        end

        if not inst.cultivation_data then
            return
        end

        -- Safe target finding
        local success, error_msg = pcall(function()
            -- Look for nearby players if no target
            if not inst.components.combat.target then
                local pos = inst:GetPosition()
                if pos then
                    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 12, {"player"})
                    if players and #players > 0 then
                        local target = players[math.random(#players)]
                        if target and target:IsValid() then
                            inst.components.combat:SetTarget(target)
                            print("[Boss] Found target:", target.name or "player")
                        end
                    end
                end
            end

            -- Safe movement
            local target = inst.components.combat.target
            if target and target:IsValid() and inst.components.locomotor then
                local target_pos = target:GetPosition()
                local boss_pos = inst:GetPosition()

                if target_pos and boss_pos then
                    local distance = math.sqrt((target_pos.x - boss_pos.x)^2 + (target_pos.z - boss_pos.z)^2)

                    if distance > 3 and distance < 50 then -- Reasonable distance check
                        inst.components.locomotor:GoToPoint(target_pos)
                    end

                    -- Use abilities occasionally
                    if math.random() < 0.1 then -- 10% chance per second
                        inst:UseRandomAbility()
                    end
                end
            elseif target and not target:IsValid() then
                -- Clear invalid target
                inst.components.combat:SetTarget(nil)
            end

            -- Safe phase update
            if inst.components.health and inst.components.health.currenthealth and inst.components.health.maxhealth then
                local health_percent = inst.components.health.currenthealth / inst.components.health.maxhealth
                local new_phase = 1
                if health_percent <= 0.75 then new_phase = 2 end
                if health_percent <= 0.5 then new_phase = 3 end
                if health_percent <= 0.25 then new_phase = 4 end

                if new_phase > inst.cultivation_data.phase then
                    inst.cultivation_data.phase = new_phase
                    local realms = {"金丹期 (Golden Core)", "元婴期 (Nascent Soul)", "化神期 (Soul Formation)", "炼虚期 (Void Refinement)"}
                    inst.cultivation_data.realm = realms[new_phase] or "Unknown"
                    print("[Boss] Entered Phase", new_phase, ":", inst.cultivation_data.realm)
                end
            end
        end)

        if not success then
            print("[Boss] AI Error:", error_msg)
        end
    end

    -- Ultra-safe ability usage
    function inst:UseRandomAbility()
        -- Validate everything
        if not self or not self:IsValid() then
            return
        end

        if not self.cultivation_data or not self.cultivation_data.abilities then
            return
        end

        local success, error_msg = pcall(function()
            local current_time = GetTime()
            if not current_time then return end

            local available_abilities = {}

            for name, ability in pairs(self.cultivation_data.abilities) do
                if ability and ability.last_used and ability.cooldown then
                    if (current_time - ability.last_used) >= ability.cooldown then
                        table.insert(available_abilities, name)
                    end
                end
            end

            if #available_abilities > 0 then
                local ability_name = available_abilities[math.random(#available_abilities)]
                if ability_name and self.cultivation_data.abilities[ability_name] then
                    self.cultivation_data.abilities[ability_name].last_used = current_time

                    if ability_name == "thunder_strike" then
                        self:CastThunderStrike()
                    elseif ability_name == "vajra_protection" then
                        self:CastVajraProtection()
                    end
                end
            end
        end)

        if not success then
            print("[Boss] Ability Error:", error_msg)
        end
    end

    -- Ultra-safe abilities with full validation
    function inst:CastThunderStrike()
        if not self or not self:IsValid() then
            return
        end

        local success, error_msg = pcall(function()
            print("[Boss] Casts 雷霆万钧 (Thunder Strike)")
            local pos = self:GetPosition()
            if not pos then return end

            local targets = TheSim:FindEntities(pos.x, pos.y, pos.z, 8, {"player"})
            if not targets then return end

            for _, target in ipairs(targets) do
                if target and target:IsValid() and target.components and target.components.combat then
                    target.components.combat:GetAttacked(self, 80)

                    -- Safe lightning effect
                    if target.AnimState then
                        target.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0)
                        target:DoTaskInTime(0.3, function()
                            if target and target:IsValid() and target.AnimState then
                                target.AnimState:SetMultColour(1, 1, 1, 1)
                            end
                        end)
                    end
                end
            end
        end)

        if not success then
            print("[Boss] Thunder Strike Error:", error_msg)
        end
    end

    function inst:CastVajraProtection()
        if not self or not self:IsValid() then
            return
        end

        local success, error_msg = pcall(function()
            print("[Boss] Casts 金刚护体 (Vajra Protection)")

            if self.components and self.components.combat then
                local old_mult = self.components.combat.damagemultiplier or 1
                self.components.combat.damagemultiplier = old_mult * 0.5 -- 50% damage reduction

                -- Safe visual effect
                if self.AnimState then
                    self.AnimState:SetMultColour(0.8, 0.8, 0.8, 1.0)
                end

                -- Safe removal after 8 seconds
                self:DoTaskInTime(8, function()
                    if self and self:IsValid() and self.components and self.components.combat then
                        self.components.combat.damagemultiplier = old_mult
                        if self.AnimState then
                            self.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
                        end
                    end
                end)
            end
        end)

        if not success then
            print("[Boss] Vajra Protection Error:", error_msg)
        end
    end

    -- Run ultra-safe AI
    inst:DoPeriodicTask(1, function()
        UltraSafeAI()
    end)

    -- Safe energy regeneration
    inst:DoPeriodicTask(2, function()
        if inst and inst:IsValid() and inst.cultivation_data then
            local success = pcall(function()
                inst.cultivation_data.spiritual_energy = math.min(
                    inst.cultivation_data.spiritual_energy + 30,
                    inst.cultivation_data.max_spiritual_energy
                )
            end)
            if not success then
                print("[Boss] Energy regen error")
            end
        end
    end)

    -- Event listeners
    inst:ListenForEvent("attacked", OnAttacked)
    inst:ListenForEvent("death", OnDeath)

    -- Debug health display
    inst:DoPeriodicTask(5, function()
        if inst.components.health then
            print("[Boss] Health:", inst.components.health.currenthealth, "/", inst.components.health.maxhealth)
        end
    end)

    return inst
end

return Prefab("cultivation_boss", fn, assets, prefabs)
