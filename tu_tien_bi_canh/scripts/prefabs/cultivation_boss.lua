-- Cultivation Boss - Clean Implementation
-- Using assets from Three Kingdoms mod

local assets = {
    Asset("ANIM", "anim/tk_boss_zhangjiao.zip"),
    Asset("ANIM", "anim/fx.zip"),
}

local prefabs = {
    "lightning_rod_fx",
}

-- Boss stats
local BOSS_HEALTH = 2000
local BOSS_DAMAGE = 60
local BOSS_SPEED = 4

local function OnAttacked(inst, data)
    local attacker = data and data.attacker
    if attacker and inst.components.combat then
        inst.components.combat:SetTarget(attacker)

        -- Flash red when hit
        if inst.AnimState then
            inst.AnimState:SetMultColour(1.0, 0.5, 0.5, 1.0)
            inst:DoTaskInTime(0.3, function()
                if inst:IsValid() then
                    inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0) -- Back to blue
                end
            end)
        end

        -- Wake up and become aggressive
        if inst.components.sleeper and inst.components.sleeper:IsAsleep() then
            inst.components.sleeper:WakeUp()
        end

        -- Taunt when hit
        if math.random() < 0.3 then
            local taunts = {
                "You dare challenge the ancient guardian!",
                "Your cultivation is weak!",
                "Feel the power of true mastery!"
            }
            local taunt = taunts[math.random(#taunts)]

            -- Show taunt to nearby players
            local pos = inst:GetPosition()
            local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 15, {"player"})
            for _, player in ipairs(players) do
                if player.components.talker then
                    player.components.talker:Say("[Guardian]: " .. taunt, 3)
                end
            end
        end

        print("[Boss] Attacked by", attacker.name or "unknown", "- Health:", inst.components.health.currenthealth)
    end
end

local function OnDeath(inst)
    print("Cultivation Boss defeated!")
    
    -- Death message
    local pos = inst:GetPosition()
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 20, {"player"})
    for _, player in ipairs(players) do
        if player.components.talker then
            player.components.talker:Say("[Guardian]: Perhaps... you have potential...", 4)
        end
    end
    
    -- Drop rewards
    local drops = {
        {item = "goldnugget", count = 8},
        {item = "redgem", count = 3},
        {item = "bluegem", count = 3},
        {item = "purplegem", count = 2},
    }
    
    for _, drop in ipairs(drops) do
        for i = 1, drop.count do
            local item = SpawnPrefab(drop.item)
            if item then
                local angle = math.random() * 2 * math.pi
                local radius = 2 + math.random() * 4
                local x = pos.x + math.cos(angle) * radius
                local z = pos.z + math.sin(angle) * radius
                item.Transform:SetPosition(x, 0, z)
                
                -- Make drops glow
                if item.AnimState then
                    item.AnimState:SetMultColour(0.8, 0.9, 1.0, 1.0)
                end
            end
        end
    end
    
    -- Victory portal
    local portal = SpawnPrefab("campfire")
    if portal then
        portal.Transform:SetPosition(pos.x, 0, pos.z)
        portal.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0) -- Golden
        
        if portal.components.inspectable then
            portal.components.inspectable:SetDescription("A golden portal opened by victory!")
        end
    end
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    -- Use Three Kingdoms boss animation
    inst.AnimState:SetBank("tk_boss_zhangjiao")
    inst.AnimState:SetBuild("tk_boss_zhangjiao")
    inst.AnimState:PlayAnimation("idle", true)

    -- Make it mystical blue
    inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)

    -- Simple animation handling (no event callbacks needed)
    local function UpdateAnimation()
        if not inst:IsValid() then return end

        local is_moving = inst.components.locomotor and inst.components.locomotor:IsMoving()

        if is_moving then
            -- Try walk animation, fallback to idle
            local success = pcall(function()
                if not inst.AnimState:IsCurrentAnimation("walk") then
                    inst.AnimState:PlayAnimation("walk", true)
                end
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle", true)
            end
        else
            if not inst.AnimState:IsCurrentAnimation("idle") then
                inst.AnimState:PlayAnimation("idle", true)
            end
        end
    end

    -- Update animation periodically
    inst:DoPeriodicTask(0.5, UpdateAnimation)

    -- Physics
    MakeCharacterPhysics(inst, 1000, 2)

    -- Tags
    inst:AddTag("epic")
    inst:AddTag("monster")
    inst:AddTag("hostile")
    inst:AddTag("cultivation_boss")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Health
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(BOSS_HEALTH)
    inst.components.health:StartRegen(1, 2) -- Regen 1 HP every 2 seconds

    -- Combat
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(BOSS_DAMAGE)
    inst.components.combat:SetAttackPeriod(2)
    inst.components.combat:SetRange(3)
    inst.components.combat:SetRetargetFunction(3, function(inst)
        return FindEntity(inst, 10, function(guy)
            return guy:HasTag("player") and inst.components.combat:CanTarget(guy)
        end)
    end)
    inst.components.combat:SetKeepTargetFunction(function(inst, target)
        return target and target:IsValid() and target:HasTag("player")
    end)

    -- Attack animation with safe API calls
    local function OnAttackOther(inst, data)
        local target = data and data.target
        if target then
            -- Try attack animation, fallback to flash effect
            local success = pcall(function()
                inst.AnimState:PlayAnimation("attack")
                inst.AnimState:PushAnimation("idle", true)
            end)
            if not success then
                -- Flash effect if no attack animation
                inst.AnimState:SetMultColour(1.2, 1.0, 0.8, 1.0)
                inst:DoTaskInTime(0.3, function()
                    if inst:IsValid() then
                        inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
                    end
                end)
            end
        end
    end

    -- Use proper event listener instead of SetOnAttack
    inst:ListenForEvent("onattackother", OnAttackOther)

    -- Locomotor
    inst:AddComponent("locomotor")
    inst.components.locomotor.walkspeed = BOSS_SPEED
    inst.components.locomotor.runspeed = BOSS_SPEED * 1.5

    -- Sleeper (for AI)
    inst:AddComponent("sleeper")
    inst.components.sleeper:SetResistance(3)

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("An ancient cultivation master, fallen to darkness!")

    -- Advanced Cultivation Brain
    local CultivationBossBrain = require("scripts/components/cultivation_boss_brain")
    inst:AddComponent("cultivation_boss_brain")
    inst.components.cultivation_boss_brain = CultivationBossBrain(inst)
    inst.cultivation_brain = inst.components.cultivation_boss_brain -- Easy access for UI

    -- Event listeners
    inst:ListenForEvent("attacked", OnAttacked)
    inst:ListenForEvent("death", OnDeath)

    -- Debug health display
    inst:DoPeriodicTask(5, function()
        if inst.components.health then
            print("[Boss] Health:", inst.components.health.currenthealth, "/", inst.components.health.maxhealth)
        end
    end)

    return inst
end

return Prefab("cultivation_boss", fn, assets, prefabs)
