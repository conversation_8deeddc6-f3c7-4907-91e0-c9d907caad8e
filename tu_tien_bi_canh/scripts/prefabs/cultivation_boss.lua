-- Ancient Cultivation Guardian Boss
-- A fallen cultivator who guards ancient secrets

local assets = {
    Asset("ANIM", "anim/deerclops.zip"), -- Use existing boss animation
}

local prefabs = {
    "lightning_rod_fx",
    "fire_fx",
    "ice_fx",
}

-- Boss stats based on cultivation level
local BOSS_STATS = {
    health = 2500,
    damage = 75,
    attack_period = 3,
    walk_speed = 3,
    run_speed = 6,
    spiritual_energy = 500,
    cultivation_level = 7, -- <PERSON><PERSON><PERSON><PERSON> (Body Integration)
}

-- Boss spells and abilities
local BOSS_SPELLS = {
    spiritual_blast = {
        name = "Spiritual Energy Blast",
        damage = 100,
        range = 8,
        cooldown = 8,
        cost = 50,
    },
    cultivation_drain = {
        name = "Cultivation Drain",
        drain_amount = 30,
        range = 6,
        cooldown = 12,
        cost = 40,
    },
    ancient_barrier = {
        name = "Ancient Barrier",
        duration = 10,
        damage_reduction = 0.5,
        cooldown = 20,
        cost = 80,
    },
    summon_spirits = {
        name = "Summon Ancient Spirits",
        spirit_count = 3,
        cooldown = 25,
        cost = 100,
    }
}

local function OnAttacked(inst, data)
    local attacker = data and data.attacker
    if not attacker then return end
    
    -- Boss gets angry when attacked
    if inst.components.combat then
        inst.components.combat:SetTarget(attacker)
    end
    
    -- Chance to cast spell when attacked
    if math.random() < 0.3 then
        inst.boss_brain:CastRandomSpell()
    end
    
    -- Show damage with visual effect
    if inst.AnimState then
        inst.AnimState:SetMultColour(1.0, 0.5, 0.5, 1.0) -- Red flash
        inst:DoTaskInTime(0.2, function()
            inst.AnimState:SetMultColour(1, 1, 1, 1)
        end)
    end
end

local function OnDeath(inst)
    print("[Boss] Ancient Cultivation Guardian defeated!")

    -- Death taunt
    if inst.boss_brain then
        inst.boss_brain:Taunt("death")
    end
    
    -- Drop valuable cultivation items
    local pos = inst:GetPosition()
    local drops = {
        {item = "goldnugget", count = 5}, -- Represent spiritual stones
        {item = "redgem", count = 2},     -- Represent fire crystals
        {item = "bluegem", count = 2},    -- Represent water crystals
        {item = "purplegem", count = 1},  -- Represent rare cultivation pill
    }
    
    for _, drop in ipairs(drops) do
        for i = 1, drop.count do
            local item = SpawnPrefab(drop.item)
            if item then
                local angle = math.random() * 2 * math.pi
                local radius = 2 + math.random() * 3
                local x = pos.x + math.cos(angle) * radius
                local z = pos.z + math.sin(angle) * radius
                item.Transform:SetPosition(x, 0, z)
                
                -- Make drops glow
                if item.AnimState then
                    item.AnimState:SetMultColour(0.8, 0.9, 1.0, 1.0)
                end
            end
        end
    end
    
    -- Give experience to nearby players
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 15, {"player"})
    for _, player in ipairs(players) do
        if player.components.cultivation then
            player.components.cultivation:AddExperience(200)
            if player.components.talker then
                player.components.talker:Say("Gained cultivation insights from defeating the guardian!", 4)
            end
        end
    end
    
    -- Spawn victory portal
    local portal = SpawnPrefab("campfire") -- Use campfire as portal
    if portal then
        portal.Transform:SetPosition(pos.x, 0, pos.z)
        portal.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0) -- Golden glow
        
        if portal.components.inspectable then
            portal.components.inspectable:SetDescription("A golden portal opened by the guardian's defeat!")
        end
        
        -- Add teleport functionality
        if not portal.components.activatable then
            portal:AddComponent("activatable")
        end
        portal.components.activatable.OnActivate = function(inst, doer)
            if doer.components.talker then
                doer.components.talker:Say("Teleporting to safety...", 2)
            end
            -- Teleport player to spawn point
            doer.Transform:SetPosition(0, 0, 0)
        end
        portal.components.activatable:SetText("Exit Portal")
    end
end

-- Boss Brain AI
local BossBrain = Class(function(self, inst)
    self.inst = inst
    self.last_spell_time = {}
    self.spiritual_energy = BOSS_STATS.spiritual_energy
    self.barrier_active = false
    self.summoned_spirits = {}
    self.current_phase = 1
    self.enrage_mode = false
    self.last_taunt_time = 0

    -- Initialize spell cooldowns
    for spell_id, _ in pairs(BOSS_SPELLS) do
        self.last_spell_time[spell_id] = 0
    end

    -- Phase thresholds
    self.phase_thresholds = {
        [2] = 0.75, -- Phase 2 at 75% health
        [3] = 0.50, -- Phase 3 at 50% health
        [4] = 0.25, -- Enrage at 25% health
    }

    -- Taunt messages
    self.taunt_messages = {
        spawn = "Foolish mortals dare disturb my eternal meditation!",
        phase2 = "You have some skill... but not enough!",
        phase3 = "Impossible! How can mere mortals wound me?!",
        enrage = "ENOUGH! I will show you true cultivation power!",
        death = "Perhaps... there is hope for your generation...",
    }
end)

function BossBrain:CanCastSpell(spell_id)
    local spell = BOSS_SPELLS[spell_id]
    if not spell then return false end
    
    local current_time = GetTime()
    if current_time - self.last_spell_time[spell_id] < spell.cooldown then
        return false
    end
    
    if self.spiritual_energy < spell.cost then
        return false
    end
    
    return true
end

function BossBrain:CastSpell(spell_id, target)
    if not self:CanCastSpell(spell_id) then return false end
    
    local spell = BOSS_SPELLS[spell_id]
    self.spiritual_energy = self.spiritual_energy - spell.cost
    self.last_spell_time[spell_id] = GetTime()
    
    print(string.format("[Boss] Casting %s", spell.name))
    
    if spell_id == "spiritual_blast" then
        self:CastSpiritualBlast(target)
    elseif spell_id == "cultivation_drain" then
        self:CastCultivationDrain(target)
    elseif spell_id == "ancient_barrier" then
        self:CastAncientBarrier()
    elseif spell_id == "summon_spirits" then
        self:CastSummonSpirits()
    end
    
    return true
end

function BossBrain:CastSpiritualBlast(target)
    local pos = self.inst:GetPosition()
    local targets = TheSim:FindEntities(pos.x, pos.y, pos.z, BOSS_SPELLS.spiritual_blast.range, {"player"})
    
    for _, player in ipairs(targets) do
        if player.components.combat then
            player.components.combat:GetAttacked(self.inst, BOSS_SPELLS.spiritual_blast.damage)
            
            -- Visual effect
            if player.AnimState then
                player.AnimState:SetMultColour(0.5, 0.5, 1.0, 1.0)
                player:DoTaskInTime(0.5, function()
                    player.AnimState:SetMultColour(1, 1, 1, 1)
                end)
            end
        end
    end
    
    -- Boss visual effect
    if self.inst.AnimState then
        self.inst.AnimState:SetMultColour(0.8, 0.8, 1.0, 1.0)
        self.inst:DoTaskInTime(1, function()
            self.inst.AnimState:SetMultColour(1, 1, 1, 1)
        end)
    end
end

function BossBrain:CastCultivationDrain(target)
    local pos = self.inst:GetPosition()
    local targets = TheSim:FindEntities(pos.x, pos.y, pos.z, BOSS_SPELLS.cultivation_drain.range, {"player"})
    
    for _, player in ipairs(targets) do
        if player.components.cultivation then
            -- Drain spiritual energy
            local drained = math.min(player.components.cultivation.spiritual_energy, BOSS_SPELLS.cultivation_drain.drain_amount)
            player.components.cultivation.spiritual_energy = player.components.cultivation.spiritual_energy - drained
            
            -- Boss recovers energy
            self.spiritual_energy = math.min(self.spiritual_energy + drained, BOSS_STATS.spiritual_energy)
            
            if player.components.talker then
                player.components.talker:Say("My spiritual energy is being drained!", 2)
            end
        end
    end
end

function BossBrain:CastAncientBarrier()
    self.barrier_active = true
    
    -- Reduce damage taken
    if self.inst.components.combat then
        local old_mult = self.inst.components.combat.damagemultiplier or 1
        self.inst.components.combat.damagemultiplier = old_mult * BOSS_SPELLS.ancient_barrier.damage_reduction
        
        -- Visual effect
        if self.inst.AnimState then
            self.inst.AnimState:SetMultColour(0.8, 0.8, 0.8, 1.0) -- Gray shield
        end
        
        -- Remove barrier after duration
        self.inst:DoTaskInTime(BOSS_SPELLS.ancient_barrier.duration, function()
            self.barrier_active = false
            self.inst.components.combat.damagemultiplier = old_mult
            if self.inst.AnimState then
                self.inst.AnimState:SetMultColour(1, 1, 1, 1)
            end
        end)
    end
end

function BossBrain:CastSummonSpirits()
    local pos = self.inst:GetPosition()
    
    for i = 1, BOSS_SPELLS.summon_spirits.spirit_count do
        local spirit = SpawnPrefab("spider") -- Use spider as spirit minion
        if spirit then
            local angle = (i / BOSS_SPELLS.summon_spirits.spirit_count) * 2 * math.pi
            local radius = 4
            local x = pos.x + math.cos(angle) * radius
            local z = pos.z + math.sin(angle) * radius
            spirit.Transform:SetPosition(x, 0, z)
            
            -- Make spirit look mystical
            if spirit.AnimState then
                spirit.AnimState:SetMultColour(0.7, 0.7, 1.0, 0.8) -- Translucent blue
            end
            
            -- Make spirit aggressive to players
            if spirit.components.combat then
                spirit.components.combat:SetTarget(self.inst.components.combat.target)
            end
            
            table.insert(self.summoned_spirits, spirit)
            
            -- Spirits disappear after 30 seconds
            spirit:DoTaskInTime(30, function()
                if spirit and spirit:IsValid() then
                    spirit:Remove()
                end
            end)
        end
    end
end

function BossBrain:CastRandomSpell()
    local available_spells = {}
    for spell_id, _ in pairs(BOSS_SPELLS) do
        if self:CanCastSpell(spell_id) then
            table.insert(available_spells, spell_id)
        end
    end
    
    if #available_spells > 0 then
        local spell_id = available_spells[math.random(#available_spells)]
        local target = self.inst.components.combat and self.inst.components.combat.target
        self:CastSpell(spell_id, target)
    end
end

function BossBrain:CheckPhaseTransition()
    if not self.inst.components.health then return end

    local health_percent = self.inst.components.health.currenthealth / self.inst.components.health.maxhealth
    local new_phase = 1

    for phase, threshold in pairs(self.phase_thresholds) do
        if health_percent <= threshold then
            new_phase = math.max(new_phase, phase)
        end
    end

    if new_phase > self.current_phase then
        self:EnterPhase(new_phase)
    end
end

function BossBrain:EnterPhase(phase)
    self.current_phase = phase

    if phase == 2 then
        self:Taunt("phase2")
        -- Phase 2: Faster spell casting
        for spell_id, _ in pairs(BOSS_SPELLS) do
            BOSS_SPELLS[spell_id].cooldown = BOSS_SPELLS[spell_id].cooldown * 0.8
        end

    elseif phase == 3 then
        self:Taunt("phase3")
        -- Phase 3: More damage
        if self.inst.components.combat then
            self.inst.components.combat.defaultdamage = BOSS_STATS.damage * 1.3
        end

    elseif phase == 4 then
        self:Taunt("enrage")
        self.enrage_mode = true
        -- Enrage: Much faster spells, more damage
        for spell_id, _ in pairs(BOSS_SPELLS) do
            BOSS_SPELLS[spell_id].cooldown = BOSS_SPELLS[spell_id].cooldown * 0.5
        end
        if self.inst.components.combat then
            self.inst.components.combat.defaultdamage = BOSS_STATS.damage * 1.5
        end

        -- Visual enrage effect
        if self.inst.AnimState then
            self.inst.AnimState:SetMultColour(1.2, 0.8, 0.8, 1.0) -- Red tint
        end
    end

    print(string.format("[Boss] Entered Phase %d", phase))
end

function BossBrain:Taunt(taunt_type)
    local current_time = GetTime()
    if current_time - self.last_taunt_time < 5 then return end -- Limit taunts

    self.last_taunt_time = current_time
    local message = self.taunt_messages[taunt_type]

    if message then
        -- Show taunt to all nearby players
        local pos = self.inst:GetPosition()
        local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 20, {"player"})

        for _, player in ipairs(players) do
            if player.components.talker then
                player.components.talker:Say("[Guardian]: " .. message, 4)
            end
        end

        print("[Boss Taunt]:", message)
    end
end

function BossBrain:Update()
    -- Check for phase transitions
    self:CheckPhaseTransition()

    -- Regenerate spiritual energy over time
    local regen_rate = self.enrage_mode and 4 or 2
    self.spiritual_energy = math.min(self.spiritual_energy + regen_rate, BOSS_STATS.spiritual_energy)

    -- Cast spells periodically if in combat
    if self.inst.components.combat and self.inst.components.combat.target then
        local spell_chance = self.enrage_mode and 0.2 or 0.1
        if math.random() < spell_chance then
            self:CastRandomSpell()
        end
    end

    -- Clean up dead spirits
    if self.summoned_spirits then
        for i = #self.summoned_spirits, 1, -1 do
            local spirit = self.summoned_spirits[i]
            if not spirit or not spirit:IsValid() then
                table.remove(self.summoned_spirits, i)
            end
        end
    end
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    -- Use deerclops as base (large boss model)
    inst.AnimState:SetBank("deerclops")
    inst.AnimState:SetBuild("deerclops")
    inst.AnimState:PlayAnimation("idle_loop", true)
    
    -- Make it look mystical
    inst.AnimState:SetMultColour(0.8, 0.9, 1.0, 1.0) -- Slight blue tint

    -- Physics
    MakeCharacterPhysics(inst, 1000, 2)

    -- Tags
    inst:AddTag("epic")
    inst:AddTag("monster")
    inst:AddTag("hostile")
    inst:AddTag("cultivation_boss")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Health
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(BOSS_STATS.health)

    -- Combat
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(BOSS_STATS.damage)
    inst.components.combat:SetAttackPeriod(BOSS_STATS.attack_period)
    inst.components.combat:SetRange(3)

    -- Locomotor
    inst:AddComponent("locomotor")
    inst.components.locomotor.walkspeed = BOSS_STATS.walk_speed
    inst.components.locomotor.runspeed = BOSS_STATS.run_speed

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("An ancient cultivation guardian, fallen to darkness. Approach with extreme caution!")

    -- Boss brain
    inst.boss_brain = BossBrain(inst)

    -- Update task for AI
    inst:DoPeriodicTask(1, function()
        inst.boss_brain:Update()
    end)

    -- Spawn taunt after 2 seconds
    inst:DoTaskInTime(2, function()
        inst.boss_brain:Taunt("spawn")
    end)

    -- Event listeners
    inst:ListenForEvent("attacked", OnAttacked)
    inst:ListenForEvent("death", OnDeath)

    return inst
end

return Prefab("cultivation_boss", fn, assets, prefabs)
