-- Cultivation Boss - Ultra Safe Simple Version
-- No complex APIs, only basic DST functions

local assets = {
    Asset("ANIM", "anim/tk_boss_zhangjiao.zip"),
}

-- Boss stats
local BOSS_HEALTH = 2000
local BOSS_DAMAGE = 60
local BOSS_SPEED = 4

local function OnAttacked(inst, data)
    local attacker = data and data.attacker
    if attacker and inst.components.combat then
        inst.components.combat:SetTarget(attacker)
        
        -- Simple flash effect
        if inst.AnimState then
            inst.AnimState:SetMultColour(1.0, 0.5, 0.5, 1.0)
            inst:DoTaskInTime(0.3, function()
                if inst:IsValid() then
                    inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
                end
            end)
        end
        
        print("[Boss] Attacked by", attacker.name or "unknown")
    end
end

local function OnDeath(inst)
    print("Cultivation Boss defeated!")
    
    -- Drop rewards
    local pos = inst:GetPosition()
    local drops = {
        {item = "goldnugget", count = 8},
        {item = "redgem", count = 3},
        {item = "bluegem", count = 3},
    }
    
    for _, drop in ipairs(drops) do
        for i = 1, drop.count do
            local item = SpawnPrefab(drop.item)
            if item then
                local angle = math.random() * 2 * math.pi
                local radius = 2 + math.random() * 4
                local x = pos.x + math.cos(angle) * radius
                local z = pos.z + math.sin(angle) * radius
                item.Transform:SetPosition(x, 0, z)
            end
        end
    end
end

-- Simple AI function
local function SimpleAI(inst)
    if not inst:IsValid() or not inst.components.combat then
        return
    end
    
    -- Look for nearby players if no target
    if not inst.components.combat.target then
        local pos = inst:GetPosition()
        local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 12, {"player"})
        if #players > 0 then
            local target = players[math.random(#players)]
            inst.components.combat:SetTarget(target)
            print("[Boss] Found target:", target.name or "player")
        end
    end
    
    -- Move towards target if we have one
    if inst.components.combat.target and inst.components.locomotor then
        local target = inst.components.combat.target
        if target:IsValid() then
            local target_pos = target:GetPosition()
            local boss_pos = inst:GetPosition()
            local distance = math.sqrt((target_pos.x - boss_pos.x)^2 + (target_pos.z - boss_pos.z)^2)
            
            -- Move towards target if too far
            if distance > 3 then
                inst.components.locomotor:GoToPoint(target_pos)
            end
        else
            -- Target is invalid, clear it
            inst.components.combat:SetTarget(nil)
        end
    end
end

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddNetwork()

    -- Use Three Kingdoms boss animation
    inst.AnimState:SetBank("tk_boss_zhangjiao")
    inst.AnimState:SetBuild("tk_boss_zhangjiao")
    inst.AnimState:PlayAnimation("idle", true)
    
    -- Make it mystical blue
    inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)

    -- Physics
    MakeCharacterPhysics(inst, 1000, 2)

    -- Tags
    inst:AddTag("epic")
    inst:AddTag("monster")
    inst:AddTag("hostile")
    inst:AddTag("cultivation_boss")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Health
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(BOSS_HEALTH)

    -- Combat
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(BOSS_DAMAGE)
    inst.components.combat:SetAttackPeriod(2)
    inst.components.combat:SetRange(3)
    inst.components.combat:SetRetargetFunction(3, function(inst)
        return FindEntity(inst, 10, function(guy)
            return guy:HasTag("player") and inst.components.combat:CanTarget(guy)
        end)
    end)
    inst.components.combat:SetKeepTargetFunction(function(inst, target)
        return target and target:IsValid() and target:HasTag("player")
    end)

    -- Locomotor
    inst:AddComponent("locomotor")
    inst.components.locomotor.walkspeed = BOSS_SPEED
    inst.components.locomotor.runspeed = BOSS_SPEED * 1.5

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("An ancient cultivation master!")

    -- Simple AI
    inst:DoPeriodicTask(1, function() SimpleAI(inst) end)

    -- Event listeners
    inst:ListenForEvent("attacked", OnAttacked)
    inst:ListenForEvent("death", OnDeath)

    -- Debug health display
    inst:DoPeriodicTask(5, function()
        if inst.components.health then
            print("[Boss] Health:", inst.components.health.currenthealth, "/", inst.components.health.maxhealth)
        end
    end)

    return inst
end

return Prefab("cultivation_boss_simple", fn, assets)
