-- Cultivation Items Prefabs
-- Basic cultivation items like spiritual stones, herbs, pills

local assets = {
    -- Use existing DST assets to avoid missing file errors
    Asset("ANIM", "anim/rocks.zip"),
    <PERSON>set("ANIM", "anim/berries.zip"),
    Asset("ANIM", "anim/carrot.zip"),
}

-- Spiritual Stone (<PERSON>h Thạch - Tiền tệ cơ bản)
local function spiritual_stone_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    -- Use existing rock animation
    inst.AnimState:SetBank("rocks")
    inst.AnimState:SetBuild("rocks")
    inst.AnimState:PlayAnimation("1")

    -- Tint it blue for mystical effect
    inst.AnimState:SetMultColour(0.5, 0.8, 1.0, 1.0)

    inst:AddTag("spiritual_stone")
    inst:AddTag("cultivation_currency")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "spiritual_stone"
    inst.components.inventoryitem.stacksize = 99

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 1

    -- Can be used for cultivation
    inst:AddComponent("spiritual_item")
    inst.components.spiritual_item:SetSpiritualValue(5)

    return inst
end

-- Cultivation Pill (Tu Luyện Đan - Thuốc tu luyện)
local function cultivation_pill_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("cultivation_pill")
    inst.AnimState:SetBuild("cultivation_pill")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("cultivation_pill")
    inst:AddTag("edible")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "cultivation_pill"
    inst.components.inventoryitem.stacksize = 20

    -- Edible component for consumption
    inst:AddComponent("edible")
    inst.components.edible.healthvalue = 0
    inst.components.edible.hungervalue = 0
    inst.components.edible.sanityvalue = 0
    inst.components.edible.foodtype = FOODTYPE.GENERIC

    -- Custom consumption effect
    inst.components.edible:SetOnEatenFn(function(inst, eater)
        if eater.components.cultivation then
            eater.components.cultivation:AddExperience(20)
            eater.components.cultivation:AddSpiritualEnergy(30)
            
            if eater.components.talker then
                local message = GetMessage("consumed_pill") or "Đã dùng thuốc, tu vi tăng trường!"
                eater.components.talker:Say(message, 2)
            end
        end
    end)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 5

    return inst
end

-- Heaven Ginseng (Thiên Sâm - Nhân sâm thiên sơn)
local function heaven_ginseng_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("heaven_ginseng")
    inst.AnimState:SetBuild("heaven_ginseng")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("heaven_ginseng")
    inst:AddTag("spiritual_herb")
    inst:AddTag("edible")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "heaven_ginseng"
    inst.components.inventoryitem.stacksize = 10

    -- Powerful healing herb
    inst:AddComponent("edible")
    inst.components.edible.healthvalue = 60
    inst.components.edible.hungervalue = 25
    inst.components.edible.sanityvalue = 15
    inst.components.edible.foodtype = FOODTYPE.VEGGIE

    -- Special cultivation effects
    inst.components.edible:SetOnEatenFn(function(inst, eater)
        if eater.components.cultivation then
            eater.components.cultivation:AddExperience(50)
            eater.components.cultivation:AddSpiritualEnergy(50)
            
            -- Temporary stat boost
            if eater.components.locomotor then
                eater.components.locomotor:SetExternalSpeedMultiplier(eater, "heaven_ginseng", 1.2)
                eater:DoTaskInTime(300, function() -- 5 minutes
                    if eater:IsValid() and eater.components.locomotor then
                        eater.components.locomotor:RemoveExternalSpeedMultiplier(eater, "heaven_ginseng")
                    end
                end)
            end
            
            if eater.components.talker then
                local message = GetMessage("consumed_ginseng") or "Dược lực thiên sâm thật mạnh mẽ!"
                eater.components.talker:Say(message, 3)
            end
        end
    end)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 20

    -- Alchemy ingredient
    inst:AddComponent("alchemy_ingredient")
    inst.components.alchemy_ingredient:SetType("herb")
    inst.components.alchemy_ingredient:SetPotency(3)

    return inst
end

-- Cloud Talisman Grass (Vân Phù Thảo - Cỏ bùa mây)
local function cloud_talisman_grass_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("cloud_talisman_grass")
    inst.AnimState:SetBuild("cloud_talisman_grass")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("cloud_talisman_grass")
    inst:AddTag("spiritual_herb")
    inst:AddTag("spell_component")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "cloud_talisman_grass"
    inst.components.inventoryitem.stacksize = 20

    -- Used for spell enhancement
    inst:AddComponent("spell_component")
    inst.components.spell_component:SetElement("air")
    inst.components.spell_component:SetPower(2)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 8

    -- Alchemy ingredient
    inst:AddComponent("alchemy_ingredient")
    inst.components.alchemy_ingredient:SetType("herb")
    inst.components.alchemy_ingredient:SetPotency(2)

    return inst
end

-- Fire Crystal (Hỏa Tinh Thạch - Pha lê lửa)
local function fire_crystal_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("fire_crystal")
    inst.AnimState:SetBuild("fire_crystal")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("fire_crystal")
    inst:AddTag("spell_component")
    inst:AddTag("elemental_crystal")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "fire_crystal"
    inst.components.inventoryitem.stacksize = 20

    -- Fire spell enhancement
    inst:AddComponent("spell_component")
    inst.components.spell_component:SetElement("fire")
    inst.components.spell_component:SetPower(3)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 15

    -- Can be used as fuel
    inst:AddComponent("fuel")
    inst.components.fuel.fuelvalue = TUNING.LARGE_FUEL
    inst.components.fuel.fueltype = FUELTYPE.CAVE

    -- Alchemy ingredient
    inst:AddComponent("alchemy_ingredient")
    inst.components.alchemy_ingredient:SetType("crystal")
    inst.components.alchemy_ingredient:SetPotency(3)

    return inst
end

-- Cultivation Manual (Tu Luyện Thủ Sách - Sách hướng dẫn tu luyện)
local function cultivation_manual_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("cultivation_manual")
    inst.AnimState:SetBuild("cultivation_manual")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("cultivation_manual")
    inst:AddTag("book")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "cultivation_manual"

    -- Can be read to gain knowledge
    inst:AddComponent("book")
    inst.components.book:SetOnReadFn(function(inst, reader)
        if reader.components.cultivation then
            reader.components.cultivation:AddExperience(100)
            
            -- Chance to unlock a random spell
            if math.random() < 0.3 then -- 30% chance
                local available_spells = {"metal_spike", "wood_bind", "water_shield", "fire_ball", "earth_spike"}
                local spell = available_spells[math.random(#available_spells)]
                
                if not reader.components.cultivation:HasSpell(spell) then
                    reader.components.cultivation:UnlockSpell(spell)
                    if reader.components.talker then
                        local message = GetMessage("learned_new_spell") or "Lĩnh ngộ được pháp thuật mới!"
                        reader.components.talker:Say(message, 3)
                    end
                end
            end
            
            if reader.components.talker then
                local message = GetMessage("read_manual") or "Đọc sách tu luyện, có được cảm ngộ."
                reader.components.talker:Say(message, 2)
            end
        end
        
        return true
    end)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 50

    return inst
end

-- Register all prefabs
local prefabs = {
    Prefab("spiritual_stone", spiritual_stone_fn, assets),
    Prefab("cultivation_pill", cultivation_pill_fn, assets),
    Prefab("heaven_ginseng", heaven_ginseng_fn, assets),
    Prefab("cloud_talisman_grass", cloud_talisman_grass_fn, assets),
    Prefab("fire_crystal", fire_crystal_fn, assets),
    Prefab("cultivation_manual", cultivation_manual_fn, assets),
}

return unpack(prefabs)
