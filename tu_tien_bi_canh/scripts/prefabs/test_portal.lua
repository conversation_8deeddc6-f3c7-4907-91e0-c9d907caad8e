-- Simple test portal prefab

local assets = {
    -- No assets needed for testing
}

local prefabs = {
    -- No prefabs needed for testing
}

local function fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    -- Use existing game assets for testing
    inst.AnimState:SetBank("portal_dst")
    inst.AnimState:SetBuild("portal_dst") 
    inst.AnimState:PlayAnimation("idle", true)

    -- Make it interactable
    inst:AddTag("structure")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Add basic components
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("Test portal for Tu Tien mod")

    inst:AddComponent("activatable")
    inst.components.activatable.OnActivate = function(inst, doer)
        if doer and doer.components.talker then
            doer.components.talker:Say("Test portal activated!", 2)
        end
        print("Test portal activated by", doer)
    end
    inst.components.activatable:SetText("Test Portal")

    return inst
end

return Prefab("test_portal", fn, assets, prefabs)
