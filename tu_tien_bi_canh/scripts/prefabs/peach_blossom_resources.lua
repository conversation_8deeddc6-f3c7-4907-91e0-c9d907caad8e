-- Peach Blossom Realm Resources
-- Unique items found in the Peach Blossom Realm

local assets = {
    Asset("ATLAS", "images/inventoryimages/peach_blossom_flower.xml"),
    Asset("IMAGE", "images/inventoryimages/peach_blossom_flower.tex"),
    Asset("ATLAS", "images/inventoryimages/spring_water.xml"),
    Asset("IMAGE", "images/inventoryimages/spring_water.tex"),
    Asset("ATLAS", "images/inventoryimages/healing_dew.xml"),
    Asset("IMAGE", "images/inventoryimages/healing_dew.tex"),
    Asset("ATLAS", "images/inventoryimages/butterfly_essence.xml"),
    Asset("IMAGE", "images/inventoryimages/butterfly_essence.tex"),
}

-- Peach Blossom Flower (桃花)
local function peach_blossom_flower_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("peach_blossom_flower")
    inst.AnimState:SetBuild("peach_blossom_flower")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("peach_blossom_flower")
    inst:AddTag("spiritual_herb")
    inst:AddTag("realm_resource")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "peach_blossom_flower"
    inst.components.inventoryitem.stacksize = 40

    -- Healing properties
    inst:AddComponent("edible")
    inst.components.edible.healthvalue = 15
    inst.components.edible.hungervalue = 5
    inst.components.edible.sanityvalue = 10
    inst.components.edible.foodtype = FOODTYPE.VEGGIE

    -- Special cultivation effects
    inst.components.edible:SetOnEatenFn(function(inst, eater)
        if eater.components.cultivation then
            eater.components.cultivation:AddSpiritualEnergy(15)
            
            -- Temporary sanity regeneration boost
            if eater.components.sanity then
                eater.components.sanity:SetRate(0.2) -- Boost sanity regen
                eater:DoTaskInTime(120, function() -- 2 minutes
                    if eater:IsValid() and eater.components.sanity then
                        eater.components.sanity:SetRate(0) -- Reset to normal
                    end
                end)
            end
            
            if eater.components.talker then
                eater.components.talker:Say("桃花的香气让人心旷神怡。", 2)
            end
        end
    end)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 3

    -- Alchemy ingredient
    inst:AddComponent("alchemy_ingredient")
    inst.components.alchemy_ingredient:SetType("flower")
    inst.components.alchemy_ingredient:SetPotency(2)
    inst.components.alchemy_ingredient:SetElement("wood")

    return inst
end

-- Spring Water (灵泉水)
local function spring_water_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("spring_water")
    inst.AnimState:SetBuild("spring_water")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("spring_water")
    inst:AddTag("drink")
    inst:AddTag("realm_resource")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "spring_water"
    inst.components.inventoryitem.stacksize = 20

    -- Powerful healing drink
    inst:AddComponent("edible")
    inst.components.edible.healthvalue = 40
    inst.components.edible.hungervalue = 15
    inst.components.edible.sanityvalue = 20
    inst.components.edible.foodtype = FOODTYPE.GENERIC

    inst.components.edible:SetOnEatenFn(function(inst, eater)
        if eater.components.cultivation then
            eater.components.cultivation:AddSpiritualEnergy(25)
            eater.components.cultivation:AddExperience(5)
            
            -- Cleanse negative effects
            if eater.components.temperature then
                eater.components.temperature:SetTemp(25) -- Normalize temperature
            end
            
            -- Remove poison if any
            if eater.components.poisonable and eater.components.poisonable:IsPoisoned() then
                eater.components.poisonable:Cure()
            end
            
            if eater.components.talker then
                eater.components.talker:Say("灵泉水清洗了身心的污浊。", 3)
            end
        end
    end)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 8

    -- Alchemy ingredient
    inst:AddComponent("alchemy_ingredient")
    inst.components.alchemy_ingredient:SetType("liquid")
    inst.components.alchemy_ingredient:SetPotency(3)
    inst.components.alchemy_ingredient:SetElement("water")

    return inst
end

-- Healing Dew (治愈甘露)
local function healing_dew_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("healing_dew")
    inst.AnimState:SetBuild("healing_dew")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("healing_dew")
    inst:AddTag("medicine")
    inst:AddTag("realm_resource")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "healing_dew"
    inst.components.inventoryitem.stacksize = 10

    -- Powerful healing medicine
    inst:AddComponent("edible")
    inst.components.edible.healthvalue = 60
    inst.components.edible.hungervalue = 0
    inst.components.edible.sanityvalue = 30
    inst.components.edible.foodtype = FOODTYPE.GENERIC

    inst.components.edible:SetOnEatenFn(function(inst, eater)
        if eater.components.cultivation then
            eater.components.cultivation:AddSpiritualEnergy(40)
            
            -- Instant healing over time
            local heal_count = 0
            local heal_task = eater:DoPeriodicTask(1, function()
                if eater:IsValid() and eater.components.health then
                    eater.components.health:DoDelta(10)
                    heal_count = heal_count + 1
                    
                    -- Healing sparkle effect
                    local fx = SpawnPrefab("healing_sparkle_fx")
                    if fx then
                        fx.Transform:SetPosition(eater.Transform:GetWorldPosition())
                    end
                    
                    if heal_count >= 5 then -- 5 seconds of healing
                        heal_task:Cancel()
                    end
                end
            end)
            
            if eater.components.talker then
                eater.components.talker:Say("甘露的治愈之力在体内流淌。", 3)
            end
        end
    end)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 15

    -- Alchemy ingredient
    inst:AddComponent("alchemy_ingredient")
    inst.components.alchemy_ingredient:SetType("essence")
    inst.components.alchemy_ingredient:SetPotency(4)
    inst.components.alchemy_ingredient:SetElement("wood")

    return inst
end

-- Butterfly Essence (蝶灵精华)
local function butterfly_essence_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddNetwork()

    MakeInventoryPhysics(inst)

    inst.AnimState:SetBank("butterfly_essence")
    inst.AnimState:SetBuild("butterfly_essence")
    inst.AnimState:PlayAnimation("idle")

    inst:AddTag("butterfly_essence")
    inst:AddTag("spell_component")
    inst:AddTag("realm_resource")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    inst:AddComponent("inspectable")
    inst:AddComponent("inventoryitem")
    inst.components.inventoryitem.imagename = "butterfly_essence"
    inst.components.inventoryitem.stacksize = 20

    -- Spell enhancement component
    inst:AddComponent("spell_component")
    inst.components.spell_component:SetElement("air")
    inst.components.spell_component:SetPower(3)

    -- Can be consumed for flight-like effects
    inst:AddComponent("edible")
    inst.components.edible.healthvalue = 0
    inst.components.edible.hungervalue = 0
    inst.components.edible.sanityvalue = 15
    inst.components.edible.foodtype = FOODTYPE.GENERIC

    inst.components.edible:SetOnEatenFn(function(inst, eater)
        if eater.components.cultivation then
            eater.components.cultivation:AddSpiritualEnergy(20)
            
            -- Temporary speed boost and lightness
            if eater.components.locomotor then
                eater.components.locomotor:SetExternalSpeedMultiplier(eater, "butterfly_essence", 1.4)
                
                -- Visual effect - floating particles
                local fx_task = eater:DoPeriodicTask(0.5, function()
                    local fx = SpawnPrefab("butterfly_trail_fx")
                    if fx then
                        fx.Transform:SetPosition(eater.Transform:GetWorldPosition())
                    end
                end)
                
                eater:DoTaskInTime(60, function() -- 1 minute
                    if eater:IsValid() then
                        if eater.components.locomotor then
                            eater.components.locomotor:RemoveExternalSpeedMultiplier(eater, "butterfly_essence")
                        end
                        if fx_task then
                            fx_task:Cancel()
                        end
                    end
                end)
            end
            
            if eater.components.talker then
                eater.components.talker:Say("身轻如燕，如蝶飞舞！", 2)
            end
        end
    end)

    inst:AddComponent("tradable")
    inst.components.tradable.goldvalue = 12

    -- Alchemy ingredient
    inst:AddComponent("alchemy_ingredient")
    inst.components.alchemy_ingredient:SetType("essence")
    inst.components.alchemy_ingredient:SetPotency(3)
    inst.components.alchemy_ingredient:SetElement("air")

    return inst
end

-- Register all prefabs
local prefabs = {
    Prefab("peach_blossom_flower", peach_blossom_flower_fn, assets),
    Prefab("spring_water", spring_water_fn, assets),
    Prefab("healing_dew", healing_dew_fn, assets),
    Prefab("butterfly_essence", butterfly_essence_fn, assets),
}

return unpack(prefabs)
