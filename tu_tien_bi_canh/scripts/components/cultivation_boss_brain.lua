-- Cultivation Boss Brain - Advanced AI System
-- Implements authentic cultivation combat patterns

local CultivationBossBrain = Class(function(self, inst)
    self.inst = inst
    
    -- Cultivation State
    self.cultivation_realm = "golden_core" -- 金丹期
    self.spiritual_energy = 1000
    self.max_spiritual_energy = 1000
    
    -- Phase System
    self.current_phase = 1
    self.phase_thresholds = {75, 50, 25} -- HP percentages for phase transitions
    
    -- Ability Cooldowns
    self.abilities = {
        vajra_protection = {cooldown = 20, last_used = 0, cost = 200},
        thunder_strike = {cooldown = 15, last_used = 0, cost = 150},
        sword_control = {cooldown = 8, last_used = 0, cost = 100},
        clone_technique = {cooldown = 30, last_used = 0, cost = 300}
    }
    
    -- Combat State
    self.combat_mode = false
    self.last_taunt = 0
    self.enrage_mode = false
    
    -- Cultivation Terminology
    self.realm_names = {
        [1] = "金丹期", -- Golden Core
        [2] = "元婴期", -- Nascent Soul  
        [3] = "化神期", -- Soul Formation
        [4] = "炼虚期"  -- Void Refinement
    }
    
    self.phase_taunts = {
        [1] = "区区凡人，也敢挑战本座？", -- Mere mortals dare challenge me?
        [2] = "看来你们有些本事...", -- It seems you have some skill...
        [3] = "不可能！凡人怎能伤到本座！", -- Impossible! How can mortals wound me!
        [4] = "既然如此，本座就让你们见识真正的修仙之力！" -- Then let me show you true cultivation power!
    }
    
    -- Start AI
    self:StartAI()
end)

function CultivationBossBrain:StartAI()
    -- Main AI loop - optimized for performance
    self.ai_task = self.inst:DoPeriodicTask(1, function()
        self:UpdateAI()
    end)
    
    -- Spiritual energy regeneration
    self.energy_task = self.inst:DoPeriodicTask(2, function()
        self:RegenerateEnergy()
    end)
end

function CultivationBossBrain:UpdateAI()
    if not self.inst:IsValid() then return end
    
    -- Check phase transitions
    self:CheckPhaseTransition()
    
    -- Update combat state
    self:UpdateCombatState()
    
    -- Execute abilities
    if self.combat_mode then
        self:ExecuteCombatAI()
    else
        self:ExecuteIdleAI()
    end
end

function CultivationBossBrain:CheckPhaseTransition()
    if not self.inst.components.health then return end
    
    local health_percent = (self.inst.components.health.currenthealth / self.inst.components.health.maxhealth) * 100
    local new_phase = 1
    
    for i, threshold in ipairs(self.phase_thresholds) do
        if health_percent <= threshold then
            new_phase = i + 1
        end
    end
    
    if new_phase > self.current_phase then
        self:EnterPhase(new_phase)
    end
end

function CultivationBossBrain:EnterPhase(phase)
    self.current_phase = phase
    self.cultivation_realm = self.realm_names[phase] or "unknown"
    
    -- Phase-specific enhancements
    if phase == 2 then
        -- 元婴期 - Faster abilities
        for _, ability in pairs(self.abilities) do
            ability.cooldown = ability.cooldown * 0.8
        end
    elseif phase == 3 then
        -- 化神期 - More damage
        if self.inst.components.combat then
            self.inst.components.combat.defaultdamage = self.inst.components.combat.defaultdamage * 1.3
        end
    elseif phase == 4 then
        -- 炼虚期 - Enrage mode
        self.enrage_mode = true
        self.max_spiritual_energy = 1500
        self.spiritual_energy = self.max_spiritual_energy
        
        -- Visual enrage effect
        if self.inst.AnimState then
            self.inst.AnimState:SetMultColour(1.2, 0.8, 0.8, 1.0)
        end
    end
    
    -- Announce phase transition
    self:Taunt(self.phase_taunts[phase])
    
    print(string.format("[Boss] Entered Phase %d: %s", phase, self.cultivation_realm))
end

function CultivationBossBrain:UpdateCombatState()
    local has_target = self.inst.components.combat and self.inst.components.combat.target
    
    if has_target and not self.combat_mode then
        self.combat_mode = true
        self:Taunt("受死吧！") -- Die!
    elseif not has_target and self.combat_mode then
        self.combat_mode = false
    end
end

function CultivationBossBrain:ExecuteCombatAI()
    -- Ability priority based on situation
    local target = self.inst.components.combat.target
    if not target or not target:IsValid() then return end
    
    local distance = self:GetDistanceToTarget(target)
    local health_percent = (self.inst.components.health.currenthealth / self.inst.components.health.maxhealth)
    
    -- Emergency abilities (low health)
    if health_percent < 0.3 then
        if self:CanUseAbility("vajra_protection") then
            self:UseAbility("vajra_protection")
            return
        end
        if self:CanUseAbility("clone_technique") then
            self:UseAbility("clone_technique")
            return
        end
    end
    
    -- Ranged abilities
    if distance > 5 then
        if self:CanUseAbility("thunder_strike") then
            self:UseAbility("thunder_strike")
        elseif self:CanUseAbility("sword_control") then
            self:UseAbility("sword_control")
        end
    end
    
    -- Move towards target if too far
    if distance > 3 and self.inst.components.locomotor then
        local target_pos = target:GetPosition()
        self.inst.components.locomotor:GoToPoint(target_pos)
    end
end

function CultivationBossBrain:ExecuteIdleAI()
    -- Look for nearby players
    local pos = self.inst:GetPosition()
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 15, {"player"})
    
    if #players > 0 and self.inst.components.combat then
        local target = players[math.random(#players)]
        self.inst.components.combat:SetTarget(target)
        self:Taunt("何人胆敢闯入本座的领域！") -- Who dares enter my domain!
    end
end

function CultivationBossBrain:CanUseAbility(ability_name)
    local ability = self.abilities[ability_name]
    if not ability then return false end
    
    local current_time = GetTime()
    local cooldown_ready = (current_time - ability.last_used) >= ability.cooldown
    local energy_sufficient = self.spiritual_energy >= ability.cost
    
    return cooldown_ready and energy_sufficient
end

function CultivationBossBrain:UseAbility(ability_name)
    if not self:CanUseAbility(ability_name) then return false end
    
    local ability = self.abilities[ability_name]
    ability.last_used = GetTime()
    self.spiritual_energy = self.spiritual_energy - ability.cost
    
    -- Execute specific ability
    if ability_name == "vajra_protection" then
        self:CastVajraProtection()
    elseif ability_name == "thunder_strike" then
        self:CastThunderStrike()
    elseif ability_name == "sword_control" then
        self:CastSwordControl()
    elseif ability_name == "clone_technique" then
        self:CastCloneTechnique()
    end
    
    return true
end

function CultivationBossBrain:CastVajraProtection()
    -- 金刚护体 - Damage reduction shield
    print("[Boss] Casts 金刚护体 (Vajra Protection)")
    
    if self.inst.components.combat then
        local old_mult = self.inst.components.combat.damagemultiplier or 1
        self.inst.components.combat.damagemultiplier = old_mult * 0.3 -- 70% damage reduction
        
        -- Visual effect
        if self.inst.AnimState then
            self.inst.AnimState:SetMultColour(0.8, 0.8, 0.8, 1.0)
        end
        
        -- Remove after 10 seconds
        self.inst:DoTaskInTime(10, function()
            if self.inst:IsValid() and self.inst.components.combat then
                self.inst.components.combat.damagemultiplier = old_mult
                if self.inst.AnimState then
                    self.inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
                end
            end
        end)
    end
    
    self:Taunt("金刚护体！") -- Vajra Protection!
end

function CultivationBossBrain:CastThunderStrike()
    -- 雷霆万钧 - AOE lightning attack
    print("[Boss] Casts 雷霆万钧 (Thunder Strike)")
    
    local pos = self.inst:GetPosition()
    local targets = TheSim:FindEntities(pos.x, pos.y, pos.z, 8, {"player"})
    
    for _, target in ipairs(targets) do
        if target.components.combat then
            local damage = 80 * (self.enrage_mode and 1.5 or 1)
            target.components.combat:GetAttacked(self.inst, damage)
            
            -- Lightning effect
            if target.AnimState then
                target.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0)
                target:DoTaskInTime(0.3, function()
                    if target:IsValid() then
                        target.AnimState:SetMultColour(1, 1, 1, 1)
                    end
                end)
            end
        end
    end
    
    self:Taunt("雷霆万钧！") -- Thunder Strike!
end

function CultivationBossBrain:CastSwordControl()
    -- 御剑术 - Projectile attack
    print("[Boss] Casts 御剑术 (Sword Control)")

    local target = self.inst.components.combat.target
    if target and target:IsValid() then
        -- Simple projectile effect without complex locomotor
        local boss_pos = self.inst:GetPosition()
        local target_pos = target:GetPosition()

        -- Calculate direction
        local dx = target_pos.x - boss_pos.x
        local dz = target_pos.z - boss_pos.z
        local distance = math.sqrt(dx*dx + dz*dz)

        if distance > 0 then
            -- Normalize direction
            dx = dx / distance
            dz = dz / distance

            -- Create multiple projectile hits along the path
            for i = 1, 3 do
                self.inst:DoTaskInTime(i * 0.3, function()
                    if target:IsValid() and target.components.combat then
                        -- Visual effect at target
                        if target.AnimState then
                            target.AnimState:SetMultColour(0.8, 0.8, 1.2, 1.0)
                            target:DoTaskInTime(0.2, function()
                                if target:IsValid() then
                                    target.AnimState:SetMultColour(1, 1, 1, 1)
                                end
                            end)
                        end

                        -- Deal damage
                        target.components.combat:GetAttacked(self.inst, 20)
                    end
                end)
            end
        end
    end

    self:Taunt("御剑术！") -- Sword Control!
end

function CultivationBossBrain:CastCloneTechnique()
    -- 分身术 - Summon clones (simplified version)
    print("[Boss] Casts 分身术 (Clone Technique)")

    local pos = self.inst:GetPosition()
    local target = self.inst.components.combat.target

    -- Create illusion effect instead of actual clones
    for i = 1, 3 do
        self.inst:DoTaskInTime(i * 0.5, function()
            if target and target:IsValid() and target.components.combat then
                -- Multiple phantom attacks
                local damage = 25
                target.components.combat:GetAttacked(self.inst, damage)

                -- Visual effect
                if target.AnimState then
                    target.AnimState:SetMultColour(0.6, 0.7, 1.0, 1.0)
                    target:DoTaskInTime(0.3, function()
                        if target:IsValid() then
                            target.AnimState:SetMultColour(1, 1, 1, 1)
                        end
                    end)
                end

                print("[Boss] Phantom strike", i, "hits for", damage, "damage")
            end
        end)
    end

    -- Boss becomes briefly untargetable (illusion effect)
    if self.inst.components.combat then
        local old_target = self.inst.components.combat.target
        self.inst.components.combat:SetTarget(nil)

        -- Visual illusion effect
        if self.inst.AnimState then
            self.inst.AnimState:SetMultColour(0.5, 0.5, 1.0, 0.7)
        end

        -- Restore after 2 seconds
        self.inst:DoTaskInTime(2, function()
            if self.inst:IsValid() then
                if self.inst.AnimState then
                    self.inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
                end
                if self.inst.components.combat and old_target and old_target:IsValid() then
                    self.inst.components.combat:SetTarget(old_target)
                end
            end
        end)
    end

    self:Taunt("分身术！") -- Clone Technique!
end

function CultivationBossBrain:RegenerateEnergy()
    local regen_rate = self.enrage_mode and 50 or 30
    self.spiritual_energy = math.min(self.spiritual_energy + regen_rate, self.max_spiritual_energy)
end

function CultivationBossBrain:GetDistanceToTarget(target)
    if not target or not target:IsValid() then return math.huge end
    
    local pos1 = self.inst:GetPosition()
    local pos2 = target:GetPosition()
    return math.sqrt((pos1.x - pos2.x)^2 + (pos1.z - pos2.z)^2)
end

function CultivationBossBrain:Taunt(message)
    local current_time = GetTime()
    if current_time - self.last_taunt < 3 then return end -- Limit taunts
    
    self.last_taunt = current_time
    
    -- Show to all nearby players
    local pos = self.inst:GetPosition()
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 20, {"player"})
    
    for _, player in ipairs(players) do
        if player.components.talker then
            player.components.talker:Say("[古修者]: " .. message, 4)
        end
    end
    
    print("[Boss Taunt]:", message)
end

function CultivationBossBrain:OnRemoveFromEntity()
    if self.ai_task then
        self.ai_task:Cancel()
    end
    if self.energy_task then
        self.energy_task:Cancel()
    end
end

function CultivationBossBrain:GetDebugString()
    return string.format("Realm: %s, Phase: %d, Energy: %d/%d, Combat: %s", 
        self.cultivation_realm, 
        self.current_phase,
        self.spiritual_energy, 
        self.max_spiritual_energy,
        tostring(self.combat_mode))
end

return CultivationBossBrain
