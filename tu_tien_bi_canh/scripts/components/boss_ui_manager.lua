-- Boss UI Manager Component
-- Manages boss UI display and updates

local BossUIManager = Class(function(self, inst)
    self.inst = inst
    self.active_boss = nil
    self.health_bar = nil
    self.update_task = nil
    self.boss_detection_task = nil
    
    -- Start boss detection
    self:StartBossDetection()
end)

function BossUIManager:StartBossDetection()
    -- Check for nearby bosses every 2 seconds
    self.boss_detection_task = self.inst:DoPeriodicTask(2, function()
        self:DetectNearbyBoss()
    end)
end

function BossUIManager:DetectNearbyBoss()
    if not self.inst or not self.inst:IsValid() then return end
    
    local pos = self.inst:GetPosition()
    if not pos then return end
    
    -- Look for cultivation bosses within 30 units
    local bosses = TheSim:FindEntities(pos.x, pos.y, pos.z, 30, {"cultivation_boss"})
    
    local closest_boss = nil
    local closest_distance = math.huge
    
    for _, boss in ipairs(bosses) do
        if boss and boss:<PERSON><PERSON><PERSON><PERSON>() and boss.components.health and boss.components.health.currenthealth > 0 then
            local boss_pos = boss:GetPosition()
            local distance = math.sqrt((pos.x - boss_pos.x)^2 + (pos.z - boss_pos.z)^2)
            
            if distance < closest_distance then
                closest_distance = distance
                closest_boss = boss
            end
        end
    end
    
    -- Update active boss
    if closest_boss ~= self.active_boss then
        self:SetActiveBoss(closest_boss)
    end
end

function BossUIManager:SetActiveBoss(boss)
    self.active_boss = boss
    
    if boss then
        self:ShowBossUI(boss)
        print("[Boss UI] Now tracking:", boss.name or "Unknown Boss")
    else
        self:HideBossUI()
        print("[Boss UI] No boss to track")
    end
end

function BossUIManager:ShowBossUI(boss)
    if not self.health_bar then
        -- Create health bar widget
        local success, error_msg = pcall(function()
            local BossHealthBar = require("scripts/widgets/boss_health_bar")
            self.health_bar = self.inst.HUD:AddChild(BossHealthBar(self.inst))
        end)
        
        if not success then
            print("[Boss UI] Could not create health bar:", error_msg)
            return
        end
    end
    
    if self.health_bar then
        self.health_bar:SetBoss(boss)
        
        -- Start update task
        if not self.update_task then
            self.update_task = self.inst:DoPeriodicTask(0.1, function()
                if self.health_bar and self.active_boss then
                    self.health_bar:OnUpdate(0.1)
                end
            end)
        end
    end
end

function BossUIManager:HideBossUI()
    if self.health_bar then
        self.health_bar:SetBoss(nil)
    end
    
    if self.update_task then
        self.update_task:Cancel()
        self.update_task = nil
    end
end

function BossUIManager:OnRemoveFromEntity()
    if self.boss_detection_task then
        self.boss_detection_task:Cancel()
        self.boss_detection_task = nil
    end
    
    if self.update_task then
        self.update_task:Cancel()
        self.update_task = nil
    end
    
    if self.health_bar then
        self.health_bar:Kill()
        self.health_bar = nil
    end
end

function BossUIManager:GetDebugString()
    local boss_name = self.active_boss and (self.active_boss.name or "Unknown") or "None"
    local ui_active = self.health_bar and "Yes" or "No"
    return string.format("Active Boss: %s, UI Active: %s", boss_name, ui_active)
end

return BossUIManager
