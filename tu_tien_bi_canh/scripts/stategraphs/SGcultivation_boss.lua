-- Cultivation Boss State Graph - Professional Animation System
-- Handles all boss animations and state transitions

require("stategraphs/commonstates")

local actionhandlers = {
    <PERSON><PERSON><PERSON><PERSON>(ACTIONS.ATTACK, "attack"),
    <PERSON><PERSON><PERSON><PERSON>(ACTIONS.EAT, "eat"),
    <PERSON><PERSON><PERSON><PERSON>(ACTIONS.GOHOME, "gohome"),
}

local events = {
    EventHandler("doattack", function(inst)
        if not inst.components.health:IsDead() and not inst.sg:HasStateTag("busy") then
            inst.sg:GoToState("attack")
        end
    end),
    
    EventHandler("attacked", function(inst)
        if not inst.components.health:IsDead() and not inst.sg:HasStateTag("busy") then
            inst.sg:GoToState("hit")
        end
    end),
    
    EventHandler("death", function(inst)
        inst.sg:GoToState("death")
    end),
    
    EventHandler("locomote", function(inst)
        if not inst.sg:HasStateTag("busy") then
            local is_moving = inst.components.locomotor:WantsToMoveForward()
            if is_moving and not inst.sg:HasStateTag("moving") then
                inst.sg:GoToState("walk")
            elseif not is_moving and inst.sg:HasStateTag("moving") then
                inst.sg:GoToState("idle")
            end
        end
    end),
}

local states = {
    State{
        name = "idle",
        tags = {"idle", "canrotate"},
        
        onenter = function(inst)
            inst.components.locomotor:StopMoving()
            inst.AnimState:PlayAnimation("idle", true)
        end,
        
        events = {
            EventHandler("animover", function(inst)
                if math.random() < 0.1 then
                    inst.sg:GoToState("taunt")
                else
                    inst.sg:GoToState("idle")
                end
            end),
        },
    },
    
    State{
        name = "walk",
        tags = {"moving", "canrotate"},
        
        onenter = function(inst)
            inst.components.locomotor:WalkForward()
            local success = pcall(function()
                inst.AnimState:PlayAnimation("walk", true)
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle", true)
            end
        end,
        
        timeline = {
            TimeEvent(4*FRAMES, function(inst)
                inst.SoundEmitter:PlaySound("dontstarve/creatures/together/antlion/sfx/step")
            end),
            TimeEvent(12*FRAMES, function(inst)
                inst.SoundEmitter:PlaySound("dontstarve/creatures/together/antlion/sfx/step")
            end),
        },
    },
    
    State{
        name = "run",
        tags = {"moving", "running", "canrotate"},
        
        onenter = function(inst)
            inst.components.locomotor:RunForward()
            local success = pcall(function()
                inst.AnimState:PlayAnimation("run", true)
            end)
            if not success then
                inst.AnimState:PlayAnimation("walk", true)
            end
        end,
    },
    
    State{
        name = "attack",
        tags = {"attack", "busy"},
        
        onenter = function(inst)
            inst.components.locomotor:StopMoving()
            inst.components.combat:StartAttack()
            
            local success = pcall(function()
                inst.AnimState:PlayAnimation("attack")
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle")
                inst.sg:GoToState("idle")
                return
            end
        end,
        
        timeline = {
            TimeEvent(15*FRAMES, function(inst)
                inst.components.combat:DoAttack()
                inst.SoundEmitter:PlaySound("dontstarve/creatures/together/antlion/sfx/attack")
            end),
        },
        
        events = {
            EventHandler("animover", function(inst)
                inst.sg:GoToState("idle")
            end),
        },
    },
    
    State{
        name = "hit",
        tags = {"hit", "busy"},
        
        onenter = function(inst)
            inst.components.locomotor:StopMoving()
            
            local success = pcall(function()
                inst.AnimState:PlayAnimation("hit")
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle")
                inst.sg:GoToState("idle")
                return
            end
            
            -- Flash effect
            inst.AnimState:SetMultColour(1.0, 0.5, 0.5, 1.0)
        end,
        
        timeline = {
            TimeEvent(5*FRAMES, function(inst)
                inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
            end),
        },
        
        events = {
            EventHandler("animover", function(inst)
                inst.sg:GoToState("idle")
            end),
        },
    },
    
    State{
        name = "taunt",
        tags = {"busy"},
        
        onenter = function(inst)
            inst.components.locomotor:StopMoving()
            
            local success = pcall(function()
                inst.AnimState:PlayAnimation("taunt")
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle")
                inst.sg:GoToState("idle")
                return
            end
            
            -- Taunt effect
            inst.AnimState:SetMultColour(1.2, 1.0, 0.8, 1.0)
        end,
        
        timeline = {
            TimeEvent(10*FRAMES, function(inst)
                inst.SoundEmitter:PlaySound("dontstarve/creatures/together/antlion/sfx/taunt")
            end),
            TimeEvent(20*FRAMES, function(inst)
                inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
            end),
        },
        
        events = {
            EventHandler("animover", function(inst)
                inst.sg:GoToState("idle")
            end),
        },
    },
    
    State{
        name = "ability_thunder",
        tags = {"busy", "ability"},
        
        onenter = function(inst)
            inst.components.locomotor:StopMoving()
            
            local success = pcall(function()
                inst.AnimState:PlayAnimation("special")
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle")
                inst.sg:GoToState("idle")
                return
            end
            
            -- Thunder effect
            inst.AnimState:SetMultColour(1.0, 1.0, 0.5, 1.0)
        end,
        
        timeline = {
            TimeEvent(15*FRAMES, function(inst)
                if inst.CastThunderStrike then
                    inst:CastThunderStrike()
                end
                inst.SoundEmitter:PlaySound("dontstarve/rain/thunder_close")
            end),
            TimeEvent(25*FRAMES, function(inst)
                inst.AnimState:SetMultColour(0.8, 0.9, 1.2, 1.0)
            end),
        },
        
        events = {
            EventHandler("animover", function(inst)
                inst.sg:GoToState("idle")
            end),
        },
    },
    
    State{
        name = "ability_protection",
        tags = {"busy", "ability"},
        
        onenter = function(inst)
            inst.components.locomotor:StopMoving()
            
            local success = pcall(function()
                inst.AnimState:PlayAnimation("special")
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle")
                inst.sg:GoToState("idle")
                return
            end
            
            -- Protection effect
            inst.AnimState:SetMultColour(0.8, 0.8, 0.8, 1.0)
        end,
        
        timeline = {
            TimeEvent(15*FRAMES, function(inst)
                if inst.CastVajraProtection then
                    inst:CastVajraProtection()
                end
                inst.SoundEmitter:PlaySound("dontstarve/creatures/together/antlion/sfx/shield")
            end),
        },
        
        events = {
            EventHandler("animover", function(inst)
                inst.sg:GoToState("idle")
            end),
        },
    },
    
    State{
        name = "death",
        tags = {"busy"},
        
        onenter = function(inst)
            inst.components.locomotor:StopMoving()
            
            local success = pcall(function()
                inst.AnimState:PlayAnimation("death")
            end)
            if not success then
                inst.AnimState:PlayAnimation("idle")
            end
            
            -- Death effect
            inst.AnimState:SetMultColour(0.5, 0.5, 0.5, 1.0)
            inst.SoundEmitter:PlaySound("dontstarve/creatures/together/antlion/sfx/death")
        end,
        
        events = {
            EventHandler("animover", function(inst)
                if inst.components.health then
                    inst.components.health:SetPercent(0)
                end
            end),
        },
    },
}

return StateGraph("cultivation_boss", states, events, "idle", actionhandlers)
