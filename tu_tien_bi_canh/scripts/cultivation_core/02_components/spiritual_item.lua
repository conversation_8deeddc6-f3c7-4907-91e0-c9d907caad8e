-- Spiritual Item Component

local SpiritualItem = Class(function(self, inst)
    self.inst = inst
    self.spiritual_value = 0
    self.element_type = nil
end)

function SpiritualItem:SetSpiritualValue(value)
    self.spiritual_value = value or 0
end

function SpiritualItem:GetSpiritualValue()
    return self.spiritual_value
end

function SpiritualItem:SetElementType(element)
    self.element_type = element
end

function SpiritualItem:GetElementType()
    return self.element_type
end

function SpiritualItem:OnSave()
    return {
        spiritual_value = self.spiritual_value,
        element_type = self.element_type,
    }
end

function SpiritualItem:OnLoad(data)
    if data then
        self.spiritual_value = data.spiritual_value or 0
        self.element_type = data.element_type
    end
end

return SpiritualItem
