-- Cultivation Component
-- Handles player cultivation level, spiritual energy, and progression

local function OnCultivationLevelChanged(self, level)
    if level then
        self.inst:AddTag("cultivator_level_" .. level)
        -- Remove old level tags
        for i, old_level in ipairs(TUNING.CULTIVATION.LEVELS) do
            if old_level ~= level then
                self.inst:RemoveTag("cultivator_level_" .. old_level)
            end
        end
        
        -- Trigger level up effects
        self:OnLevelUp(level)
    end
end

local function OnSpiritualEnergyChanged(self, energy)
    if energy then
        self.inst:PushEvent("spiritual_energy_changed", {energy = energy, max = self.max_spiritual_energy})
    end
end

local function OnExperienceChanged(self, exp)
    if exp then
        self.inst:PushEvent("cultivation_exp_changed", {exp = exp, required = self:GetExpRequiredForNextLevel()})
        
        -- Check for level up
        if self:CanLevelUp() then
            self:TryLevelUp()
        end
    end
end

local Cultivation = Class(function(self, inst)
    self.inst = inst
    
    -- Cultivation level (1-8, corresponding to TUNING.CULTIVATION.LEVELS)
    self.level = 1
    self.experience = 0
    
    -- Spiritual energy system
    self.spiritual_energy = TUNING.CULTIVATION.SPIRITUAL_ENERGY.MAX_CAPACITY
    self.max_spiritual_energy = TUNING.CULTIVATION.SPIRITUAL_ENERGY.MAX_CAPACITY
    
    -- Meditation state
    self.is_meditating = false
    self.meditation_start_time = 0
    
    -- Breakthrough state
    self.is_breaking_through = false
    self.breakthrough_progress = 0
    
    -- Unlocked spells
    self.unlocked_spells = {}
    
    -- Sect affiliation
    self.sect = nil
    self.sect_rank = 0
    self.sect_points = 0
    
    -- Karma system
    self.righteous_karma = 0
    self.evil_karma = 0
    
    -- Initialize with basic spells for level 1
    self:UnlockSpell("metal_spike")
    self:UnlockSpell("wood_bind")
    
    -- Start spiritual energy regeneration
    self:StartSpiritualEnergyRegen()
    
    -- Listen for events
    inst:ListenForEvent("death", function() self:OnDeath() end)
    inst:ListenForEvent("respawnfromghost", function() self:OnRespawn() end)
    
end, nil, {
    level = OnCultivationLevelChanged,
    spiritual_energy = OnSpiritualEnergyChanged,
    experience = OnExperienceChanged,
})

function Cultivation:GetLevelName()
    return GetCultivationLevelName(self.level) or "Unknown Level"
end

function Cultivation:GetExpRequiredForNextLevel()
    if self.level >= #TUNING.CULTIVATION.LEVELS then
        return 0 -- Max level reached
    end
    return TUNING.CULTIVATION.EXP_REQUIREMENTS[self.level] or 0
end

function Cultivation:CanLevelUp()
    local required = self:GetExpRequiredForNextLevel()
    return required > 0 and self.experience >= required
end

function Cultivation:TryLevelUp()
    if not self:CanLevelUp() then
        return false
    end
    
    local required = self:GetExpRequiredForNextLevel()
    self.experience = self.experience - required
    self.level = self.level + 1
    
    -- Trigger breakthrough sequence
    self:StartBreakthrough()
    
    return true
end

function Cultivation:StartBreakthrough()
    self.is_breaking_through = true
    self.breakthrough_progress = 0
    
    -- Visual and audio effects
    self.inst:PushEvent("cultivation_breakthrough_start")
    
    -- Breakthrough takes time and requires focus
    self.breakthrough_task = self.inst:DoPeriodicTask(1, function()
        self.breakthrough_progress = self.breakthrough_progress + 1
        
        if self.breakthrough_progress >= 10 then -- 10 seconds
            self:CompleteBreakthrough()
        end
    end)
end

function Cultivation:CompleteBreakthrough()
    if self.breakthrough_task then
        self.breakthrough_task:Cancel()
        self.breakthrough_task = nil
    end
    
    self.is_breaking_through = false
    
    -- Level up benefits
    self:ApplyLevelUpBenefits()
    
    -- Effects
    self.inst:PushEvent("cultivation_breakthrough_complete", {level = self.level})

    -- Show level up message
    if self.inst.components.talker then
        local message = GetMessage("level_up", {level = self:GetLevelName()})
        self.inst.components.talker:Say(message, 3)
    end

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Cultivation] Player reached level", self.level, "(" .. self:GetLevelName() .. ")")
    end
end

function Cultivation:ApplyLevelUpBenefits()
    local level = self.level
    
    -- Increase max spiritual energy
    self.max_spiritual_energy = TUNING.CULTIVATION.SPIRITUAL_ENERGY.MAX_CAPACITY + (level - 1) * 20
    
    -- Heal player on level up
    if self.inst.components.health then
        self.inst.components.health:DoDelta(self.inst.components.health.maxhealth * 0.5)
    end
    
    -- Restore spiritual energy
    self.spiritual_energy = self.max_spiritual_energy
    
    -- Unlock new spells based on level
    self:UnlockSpellsForLevel(level)
    
    -- Apply stat bonuses
    self:ApplyStatBonuses(level)
end

function Cultivation:UnlockSpellsForLevel(level)
    local spell_unlocks = {
        [1] = {"metal_spike", "wood_bind"}, -- Qi Refining (already unlocked in init)
        [2] = {"water_shield", "fire_ball"}, -- Foundation
        [3] = {"earth_spike", "metal_rain"}, -- Golden Core
        [4] = {"wood_growth", "water_prison"}, -- Nascent Soul
        [5] = {"fire_tornado", "earth_armor"}, -- Spirit Transform
        [6] = {"lightning_strike", "void_step"}, -- Void Refining
        [7] = {"gravity_well", "time_slow"}, -- Integration
        [8] = {"divine_shield", "soul_burn"}, -- Great Vehicle
    }

    local spells = spell_unlocks[level]
    if spells then
        for _, spell in ipairs(spells) do
            self:UnlockSpell(spell)
        end
    end
end

function Cultivation:ApplyStatBonuses(level)
    -- Apply permanent stat bonuses based on cultivation level
    if self.inst.components.locomotor then
        local speed_bonus = (level - 1) * 0.05 -- 5% per level
        self.inst.components.locomotor:SetExternalSpeedMultiplier(self.inst, "cultivation", 1 + speed_bonus)
    end
    
    if self.inst.components.health then
        local health_bonus = (level - 1) * 10 -- 10 health per level
        self.inst.components.health:SetMaxHealth(self.inst.components.health.maxhealth + health_bonus)
    end
end

function Cultivation:UnlockSpell(spell_name)
    if not self:HasSpell(spell_name) then
        table.insert(self.unlocked_spells, spell_name)
        self.inst:PushEvent("spell_unlocked", {spell = spell_name})
        
        if TUNING.CULTIVATION_MOD.DEBUG_MODE then
            print("[Cultivation] Unlocked spell:", spell_name)
        end
    end
end

function Cultivation:HasSpell(spell_name)
    for _, spell in ipairs(self.unlocked_spells) do
        if spell == spell_name then
            return true
        end
    end
    return false
end

function Cultivation:AddExperience(amount)
    if amount and amount > 0 then
        local modified_amount = amount * TUNING.CULTIVATION.SPEED_MODIFIER
        self.experience = self.experience + modified_amount
        
        if TUNING.CULTIVATION_MOD.DEBUG_MODE then
            print("[Cultivation] Gained", modified_amount, "experience (total:", self.experience .. ")")
        end
    end
end

function Cultivation:StartMeditation()
    if self.is_meditating then
        return false
    end
    
    self.is_meditating = true
    self.meditation_start_time = GetTime()
    
    -- Start meditation effects
    self.meditation_task = self.inst:DoPeriodicTask(1, function()
        self:OnMeditationTick()
    end)
    
    self.inst:PushEvent("meditation_start")

    -- Show meditation start message
    if self.inst.components.talker then
        local message = GetMessage("meditation_start")
        self.inst.components.talker:Say(message, 2)
    end

    return true
end

function Cultivation:StopMeditation()
    if not self.is_meditating then
        return
    end
    
    self.is_meditating = false
    
    if self.meditation_task then
        self.meditation_task:Cancel()
        self.meditation_task = nil
    end
    
    local meditation_time = GetTime() - self.meditation_start_time
    local exp_gained = math.floor(meditation_time / 10) -- 1 exp per 10 seconds
    
    if exp_gained > 0 then
        self:AddExperience(exp_gained)
    end
    
    self.inst:PushEvent("meditation_stop", {time = meditation_time, exp = exp_gained})

    -- Show meditation stop message
    if self.inst.components.talker then
        local message = GetMessage("meditation_stop")
        self.inst.components.talker:Say(message, 2)
    end
end

function Cultivation:OnMeditationTick()
    -- Regenerate spiritual energy faster while meditating
    local regen_amount = TUNING.CULTIVATION.SPIRITUAL_ENERGY.MEDITATION_BONUS
    self:AddSpiritualEnergy(regen_amount)
    
    -- Small chance to gain experience
    if math.random() < 0.1 then -- 10% chance per second
        self:AddExperience(1)
    end
end

function Cultivation:StartSpiritualEnergyRegen()
    self.energy_regen_task = self.inst:DoPeriodicTask(60, function() -- Every minute
        if not self.is_meditating then
            local regen = TUNING.CULTIVATION.SPIRITUAL_ENERGY.BASE_REGEN
            self:AddSpiritualEnergy(regen)
        end
    end)
end

function Cultivation:AddSpiritualEnergy(amount)
    if amount then
        self.spiritual_energy = math.min(self.max_spiritual_energy, self.spiritual_energy + amount)
    end
end

function Cultivation:ConsumeSpiritualEnergy(amount)
    if amount and self.spiritual_energy >= amount then
        self.spiritual_energy = self.spiritual_energy - amount
        return true
    end
    return false
end

function Cultivation:OnDeath()
    self:StopMeditation()
    
    if self.energy_regen_task then
        self.energy_regen_task:Cancel()
        self.energy_regen_task = nil
    end
    
    -- Lose some experience on death
    local exp_loss = math.floor(self.experience * 0.1) -- 10% loss
    self.experience = math.max(0, self.experience - exp_loss)
end

function Cultivation:OnRespawn()
    -- Restore some spiritual energy
    self.spiritual_energy = math.floor(self.max_spiritual_energy * 0.5)
    
    -- Restart energy regeneration
    self:StartSpiritualEnergyRegen()
end

function Cultivation:OnSave()
    return {
        level = self.level,
        experience = self.experience,
        spiritual_energy = self.spiritual_energy,
        max_spiritual_energy = self.max_spiritual_energy,
        unlocked_spells = self.unlocked_spells,
        sect = self.sect,
        sect_rank = self.sect_rank,
        sect_points = self.sect_points,
        righteous_karma = self.righteous_karma,
        evil_karma = self.evil_karma,
    }
end

function Cultivation:OnLoad(data)
    if data then
        self.level = data.level or 1
        self.experience = data.experience or 0
        self.spiritual_energy = data.spiritual_energy or self.max_spiritual_energy
        self.max_spiritual_energy = data.max_spiritual_energy or TUNING.CULTIVATION.SPIRITUAL_ENERGY.MAX_CAPACITY
        self.unlocked_spells = data.unlocked_spells or {}
        self.sect = data.sect
        self.sect_rank = data.sect_rank or 0
        self.sect_points = data.sect_points or 0
        self.righteous_karma = data.righteous_karma or 0
        self.evil_karma = data.evil_karma or 0
        
        -- Apply level bonuses
        self:ApplyStatBonuses(self.level)
    end
end

return Cultivation
