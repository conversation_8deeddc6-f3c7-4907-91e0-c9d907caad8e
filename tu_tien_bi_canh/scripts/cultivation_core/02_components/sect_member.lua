-- Sect Member Component (Placeholder)

local SectMember = Class(function(self, inst)
    self.inst = inst
    self.sect = nil
    self.rank = 0
    self.sect_points = 0
end)

function SectMember:JoinSect(sect_name)
    self.sect = sect_name
    self.rank = 1 -- Outer Disciple
    self.sect_points = 0
end

function SectMember:LeaveSect()
    self.sect = nil
    self.rank = 0
    self.sect_points = 0
end

function SectMember:OnSave()
    return {
        sect = self.sect,
        rank = self.rank,
        sect_points = self.sect_points,
    }
end

function SectMember:OnLoad(data)
    if data then
        self.sect = data.sect
        self.rank = data.rank or 0
        self.sect_points = data.sect_points or 0
    end
end

return SectMember
