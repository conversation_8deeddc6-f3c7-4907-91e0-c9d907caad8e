-- Sect Member Component
-- Handles player membership in cultivation sects

local SECT_DATA = {
    -- Righteous Sects
    heavenly_sword_sect = {
        name = "天剑宗",
        type = "righteous",
        description = "正道第一大派，以剑法无双著称",
        benefits = {
            sword_mastery = true,
            righteous_karma_bonus = 1.2,
            spell_discount = 0.9,
        },
        ranks = {
            {name = "外门弟子", points_required = 0, benefits = {"basic_sword_technique"}},
            {name = "内门弟子", points_required = 100, benefits = {"intermediate_sword_technique", "sect_uniform"}},
            {name = "核心弟子", points_required = 300, benefits = {"advanced_sword_technique", "flying_sword"}},
            {name = "长老", points_required = 800, benefits = {"master_sword_technique", "sect_authority"}},
            {name = "掌门", points_required = 2000, benefits = {"supreme_sword_technique", "sect_leadership"}},
        },
        requirements = {
            min_level = 2,
            karma_type = "righteous",
            min_karma = 20,
        },
    },

    fire_cloud_sect = {
        name = "火云派",
        type = "righteous",
        description = "掌控烈火之力，门下弟子都是火法高手",
        benefits = {
            fire_mastery = true,
            fire_resistance = true,
            alchemy_bonus = 1.3,
        },
        ranks = {
            {name = "外门弟子", points_required = 0, benefits = {"basic_fire_technique"}},
            {name = "内门弟子", points_required = 120, benefits = {"intermediate_fire_technique", "fire_robe"}},
            {name = "核心弟子", points_required = 350, benefits = {"advanced_fire_technique", "flame_spirit"}},
            {name = "长老", points_required = 900, benefits = {"master_fire_technique", "fire_domain"}},
            {name = "掌门", points_required = 2200, benefits = {"supreme_fire_technique", "fire_phoenix"}},
        },
        requirements = {
            min_level = 3,
            karma_type = "righteous",
            min_karma = 15,
        },
    },

    -- Demonic Sects
    blood_moon_sect = {
        name = "血月魔宗",
        type = "demonic",
        description = "魔道宗门，修炼血腥法术",
        benefits = {
            blood_magic = true,
            evil_karma_bonus = 1.2,
            combat_bonus = 1.1,
        },
        ranks = {
            {name = "外门魔徒", points_required = 0, benefits = {"basic_blood_technique"}},
            {name = "内门魔徒", points_required = 80, benefits = {"intermediate_blood_technique", "blood_robe"}},
            {name = "核心魔徒", points_required = 250, benefits = {"advanced_blood_technique", "blood_spirit"}},
            {name = "魔长老", points_required = 700, benefits = {"master_blood_technique", "blood_domain"}},
            {name = "魔主", points_required = 1800, benefits = {"supreme_blood_technique", "blood_moon"}},
        },
        requirements = {
            min_level = 2,
            karma_type = "evil",
            min_karma = 20,
        },
    },

    -- Neutral Sects
    wandering_immortals = {
        name = "逍遥仙盟",
        type = "neutral",
        description = "自由修士联盟，不拘一格",
        benefits = {
            freedom_bonus = true,
            all_spell_access = true,
            trading_bonus = 1.2,
        },
        ranks = {
            {name = "散修", points_required = 0, benefits = {"basic_freedom_technique"}},
            {name = "逍遥客", points_required = 150, benefits = {"intermediate_freedom_technique", "travel_cloak"}},
            {name = "仙盟成员", points_required = 400, benefits = {"advanced_freedom_technique", "immortal_spirit"}},
            {name = "仙盟长老", points_required = 1000, benefits = {"master_freedom_technique", "space_mastery"}},
            {name = "盟主", points_required = 2500, benefits = {"supreme_freedom_technique", "immortal_domain"}},
        },
        requirements = {
            min_level = 1,
            karma_type = "any",
        },
    },
}

local function OnSectChanged(self, sect)
    if sect then
        self.inst:AddTag("sect_" .. sect)

        -- Remove old sect tags
        for sect_name, _ in pairs(SECT_DATA) do
            if sect_name ~= sect then
                self.inst:RemoveTag("sect_" .. sect_name)
            end
        end

        -- Apply sect benefits
        self:ApplySectBenefits()
    end
end

local function OnRankChanged(self, rank)
    if rank then
        self:ApplyRankBenefits()
    end
end

local SectMember = Class(function(self, inst)
    self.inst = inst

    -- Sect membership
    self.sect = nil
    self.rank = 0
    self.sect_points = 0

    -- Sect activities
    self.daily_tasks_completed = 0
    self.sect_missions_completed = 0
    self.sect_contributions = 0

    -- Sect relationships
    self.sect_reputation = {}

    -- Benefits tracking
    self.active_benefits = {}

end, nil, {
    sect = OnSectChanged,
    rank = OnRankChanged,
})

function SectMember:CanJoinSect(sect_name)
    local sect_data = SECT_DATA[sect_name]
    if not sect_data then
        return false, "未知宗门"
    end

    if self.sect then
        return false, "已经加入了宗门"
    end

    -- Check requirements
    local req = sect_data.requirements

    if self.inst.components.cultivation then
        local cultivation = self.inst.components.cultivation

        -- Level requirement
        if cultivation.level < req.min_level then
            return false, "修为不足"
        end

        -- Karma requirement
        if req.karma_type == "righteous" then
            if cultivation.righteous_karma < (req.min_karma or 0) then
                return false, "正道声望不足"
            end
        elseif req.karma_type == "evil" then
            if cultivation.evil_karma < (req.min_karma or 0) then
                return false, "魔道声望不足"
            end
        end
    end

    return true
end

function SectMember:JoinSect(sect_name)
    local can_join, reason = self:CanJoinSect(sect_name)
    if not can_join then
        return false, reason
    end

    self.sect = sect_name
    self.rank = 1 -- Start as outer disciple
    self.sect_points = 0

    -- Initialize sect reputation
    for other_sect, _ in pairs(SECT_DATA) do
        if other_sect ~= sect_name then
            local sect_type = SECT_DATA[sect_name].type
            local other_type = SECT_DATA[other_sect].type

            if sect_type == "righteous" and other_type == "demonic" then
                self.sect_reputation[other_sect] = -50 -- Hostile
            elseif sect_type == "demonic" and other_type == "righteous" then
                self.sect_reputation[other_sect] = -50 -- Hostile
            else
                self.sect_reputation[other_sect] = 0 -- Neutral
            end
        end
    end

    -- Apply initial benefits
    self:ApplySectBenefits()
    self:ApplyRankBenefits()

    -- Trigger join event
    self.inst:PushEvent("sect_joined", {sect = sect_name})

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Sect] Player joined:", sect_name)
    end

    return true
end

function SectMember:LeaveSect()
    if not self.sect then
        return false
    end

    local old_sect = self.sect

    -- Remove benefits
    self:RemoveSectBenefits()
    self:RemoveRankBenefits()

    -- Reset sect data
    self.sect = nil
    self.rank = 0
    self.sect_points = 0
    self.sect_reputation = {}
    self.active_benefits = {}

    -- Trigger leave event
    self.inst:PushEvent("sect_left", {sect = old_sect})

    return true
end

function SectMember:AddSectPoints(amount)
    if not self.sect or not amount then
        return
    end

    self.sect_points = self.sect_points + amount

    -- Check for rank promotion
    self:CheckRankPromotion()

    -- Trigger points gained event
    self.inst:PushEvent("sect_points_gained", {amount = amount, total = self.sect_points})
end

function SectMember:CheckRankPromotion()
    if not self.sect then
        return
    end

    local sect_data = SECT_DATA[self.sect]
    if not sect_data then
        return
    end

    local current_rank = self.rank
    local max_rank = #sect_data.ranks

    for rank = current_rank + 1, max_rank do
        local rank_data = sect_data.ranks[rank]
        if self.sect_points >= rank_data.points_required then
            self:PromoteToRank(rank)
        else
            break
        end
    end
end

function SectMember:PromoteToRank(new_rank)
    if not self.sect or new_rank <= self.rank then
        return
    end

    local old_rank = self.rank
    self.rank = new_rank

    -- Remove old rank benefits
    self:RemoveRankBenefits()

    -- Apply new rank benefits
    self:ApplyRankBenefits()

    -- Trigger promotion event
    self.inst:PushEvent("sect_rank_promoted", {
        old_rank = old_rank,
        new_rank = new_rank,
        sect = self.sect
    })

    if self.inst.components.talker then
        local sect_data = SECT_DATA[self.sect]
        local rank_name = sect_data.ranks[new_rank].name
        self.inst.components.talker:Say("晋升为" .. rank_name .. "！", 3)
    end

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Sect] Player promoted to rank", new_rank, "in", self.sect)
    end
end

function SectMember:ApplySectBenefits()
    if not self.sect then
        return
    end

    local sect_data = SECT_DATA[self.sect]
    if not sect_data then
        return
    end

    local benefits = sect_data.benefits

    -- Apply sect-wide benefits
    if benefits.sword_mastery and self.inst.components.combat then
        self.inst.components.combat:AddDamageModifier("sect_sword", 1.1)
        self.active_benefits.sword_mastery = true
    end

    if benefits.fire_mastery and self.inst.components.spell_caster then
        self.inst.fire_spell_bonus = 1.2
        self.active_benefits.fire_mastery = true
    end

    if benefits.fire_resistance then
        self.inst:AddTag("fire_resistant")
        self.active_benefits.fire_resistance = true
    end

    if benefits.righteous_karma_bonus and self.inst.components.cultivation then
        self.inst.components.cultivation.righteous_karma_multiplier = benefits.righteous_karma_bonus
        self.active_benefits.righteous_karma_bonus = true
    end

    if benefits.evil_karma_bonus and self.inst.components.cultivation then
        self.inst.components.cultivation.evil_karma_multiplier = benefits.evil_karma_bonus
        self.active_benefits.evil_karma_bonus = true
    end

    if benefits.combat_bonus and self.inst.components.combat then
        self.inst.components.combat:AddDamageModifier("sect_combat", benefits.combat_bonus)
        self.active_benefits.combat_bonus = true
    end

    if benefits.alchemy_bonus then
        self.inst.alchemy_success_bonus = benefits.alchemy_bonus
        self.active_benefits.alchemy_bonus = true
    end

    if benefits.trading_bonus then
        self.inst.trading_bonus = benefits.trading_bonus
        self.active_benefits.trading_bonus = true
    end

    if benefits.all_spell_access and self.inst.components.cultivation then
        -- Unlock all basic spells
        local basic_spells = {"metal_spike", "wood_bind", "water_shield", "fire_ball", "earth_spike"}
        for _, spell in ipairs(basic_spells) do
            self.inst.components.cultivation:UnlockSpell(spell)
        end
        self.active_benefits.all_spell_access = true
    end
end

function SectMember:RemoveSectBenefits()
    if self.active_benefits.sword_mastery and self.inst.components.combat then
        self.inst.components.combat:RemoveDamageModifier("sect_sword")
    end

    if self.active_benefits.fire_mastery then
        self.inst.fire_spell_bonus = nil
    end

    if self.active_benefits.fire_resistance then
        self.inst:RemoveTag("fire_resistant")
    end

    if self.active_benefits.righteous_karma_bonus and self.inst.components.cultivation then
        self.inst.components.cultivation.righteous_karma_multiplier = nil
    end

    if self.active_benefits.evil_karma_bonus and self.inst.components.cultivation then
        self.inst.components.cultivation.evil_karma_multiplier = nil
    end

    if self.active_benefits.combat_bonus and self.inst.components.combat then
        self.inst.components.combat:RemoveDamageModifier("sect_combat")
    end

    if self.active_benefits.alchemy_bonus then
        self.inst.alchemy_success_bonus = nil
    end

    if self.active_benefits.trading_bonus then
        self.inst.trading_bonus = nil
    end

    self.active_benefits = {}
end

function SectMember:ApplyRankBenefits()
    if not self.sect or self.rank <= 0 then
        return
    end

    local sect_data = SECT_DATA[self.sect]
    if not sect_data then
        return
    end

    local rank_data = sect_data.ranks[self.rank]
    if not rank_data then
        return
    end

    -- Apply rank-specific benefits
    for _, benefit in ipairs(rank_data.benefits) do
        if benefit == "sect_uniform" then
            -- Give sect uniform
            if self.inst.components.inventory then
                local uniform = SpawnPrefab("sect_uniform_" .. self.sect)
                if uniform then
                    self.inst.components.inventory:GiveItem(uniform)
                end
            end
        elseif benefit == "flying_sword" then
            -- Give flying sword
            if self.inst.components.inventory then
                local sword = SpawnPrefab("flying_sword")
                if sword then
                    self.inst.components.inventory:GiveItem(sword)
                end
            end
        elseif benefit == "sect_authority" then
            -- Grant sect authority
            self.inst:AddTag("sect_elder")
        elseif benefit == "sect_leadership" then
            -- Grant sect leadership
            self.inst:AddTag("sect_leader")
        end
        -- Add more rank benefits as needed
    end
end

function SectMember:RemoveRankBenefits()
    -- Remove rank-specific tags and items
    self.inst:RemoveTag("sect_elder")
    self.inst:RemoveTag("sect_leader")
    -- Could remove sect items here if needed
end

function SectMember:GetSectData()
    if not self.sect then
        return nil
    end

    local base_data = SECT_DATA[self.sect]
    if not base_data then
        return nil
    end

    -- Create localized copy
    local localized_data = {}
    for key, value in pairs(base_data) do
        localized_data[key] = value
    end

    -- Override with localized text
    localized_data.name = GetSectName(self.sect)
    localized_data.description = GetSectDescription(self.sect)

    return localized_data
end

function SectMember:GetRankName()
    if not self.sect or self.rank <= 0 then
        return "无门无派"
    end

    local sect_data = SECT_DATA[self.sect]
    if not sect_data or not sect_data.ranks[self.rank] then
        return "未知"
    end

    return sect_data.ranks[self.rank].name
end

function SectMember:GetAvailableSects()
    local available = {}

    for sect_name, sect_data in pairs(SECT_DATA) do
        local can_join = self:CanJoinSect(sect_name)
        if can_join then
            table.insert(available, sect_name)
        end
    end

    return available
end

function SectMember:OnSave()
    return {
        sect = self.sect,
        rank = self.rank,
        sect_points = self.sect_points,
        daily_tasks_completed = self.daily_tasks_completed,
        sect_missions_completed = self.sect_missions_completed,
        sect_contributions = self.sect_contributions,
        sect_reputation = self.sect_reputation,
        active_benefits = self.active_benefits,
    }
end

function SectMember:OnLoad(data)
    if data then
        self.sect = data.sect
        self.rank = data.rank or 0
        self.sect_points = data.sect_points or 0
        self.daily_tasks_completed = data.daily_tasks_completed or 0
        self.sect_missions_completed = data.sect_missions_completed or 0
        self.sect_contributions = data.sect_contributions or 0
        self.sect_reputation = data.sect_reputation or {}
        self.active_benefits = data.active_benefits or {}

        -- Reapply benefits
        if self.sect then
            self:ApplySectBenefits()
            self:ApplyRankBenefits()
        end
    end
end

return SectMember
