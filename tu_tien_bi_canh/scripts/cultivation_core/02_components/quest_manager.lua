-- Quest Manager Component
-- Handles quest system for cultivation mod

local QUEST_DATA = {
    -- Beginner Quests
    first_meditation = {
        id = "first_meditation",
        name = "初次冥想",
        description = "进行第一次冥想修炼",
        type = "tutorial",
        requirements = {
            cultivation_level = 1,
        },
        objectives = {
            {type = "meditate", target = 30, description = "冥想30秒"},
        },
        rewards = {
            experience = 20,
            items = {"spiritual_stone", "spiritual_stone"},
            spiritual_energy = 50,
        },
        auto_start = true,
    },
    
    collect_herbs = {
        id = "collect_herbs",
        name = "采集灵草",
        description = "收集修炼所需的灵草",
        type = "collection",
        requirements = {
            cultivation_level = 1,
        },
        objectives = {
            {type = "collect", item = "peach_blossom_flower", target = 5, description = "收集5朵桃花"},
            {type = "collect", item = "cloud_talisman_grass", target = 3, description = "收集3株云符草"},
        },
        rewards = {
            experience = 30,
            items = {"cultivation_pill", "cultivation_pill"},
            sect_points = 10,
        },
    },
    
    first_spell = {
        id = "first_spell",
        name = "初学法术",
        description = "学会并使用第一个法术",
        type = "tutorial",
        requirements = {
            cultivation_level = 1,
        },
        objectives = {
            {type = "cast_spell", spell = "metal_spike", target = 3, description = "使用金刺术3次"},
        },
        rewards = {
            experience = 40,
            items = {"fire_crystal"},
            unlock_spell = "wood_bind",
        },
    },
    
    -- Intermediate Quests
    realm_explorer = {
        id = "realm_explorer",
        name = "秘境探索者",
        description = "探索不同的修炼秘境",
        type = "exploration",
        requirements = {
            cultivation_level = 2,
        },
        objectives = {
            {type = "enter_realm", realm = "peach_blossom", target = 1, description = "进入桃花秘境"},
            {type = "enter_realm", realm = "heavenly_mountain", target = 1, description = "进入天山秘境"},
        },
        rewards = {
            experience = 100,
            items = {"heaven_ginseng", "mountain_crystal"},
            sect_points = 25,
        },
    },
    
    alchemy_apprentice = {
        id = "alchemy_apprentice",
        name = "炼丹学徒",
        description = "学习炼丹术的基础",
        type = "crafting",
        requirements = {
            cultivation_level = 2,
        },
        objectives = {
            {type = "craft", item = "basic_healing_pill", target = 3, description = "炼制3颗基础疗伤丹"},
            {type = "craft", item = "spiritual_energy_pill", target = 1, description = "炼制1颗回灵丹"},
        },
        rewards = {
            experience = 80,
            items = {"alchemy_furnace"},
            unlock_recipe = "foundation_pill",
        },
    },
    
    -- Advanced Quests
    sect_champion = {
        id = "sect_champion",
        name = "宗门之光",
        description = "成为宗门的杰出弟子",
        type = "sect",
        requirements = {
            cultivation_level = 4,
            sect_member = true,
        },
        objectives = {
            {type = "sect_points", target = 200, description = "获得200宗门贡献点"},
            {type = "sect_rank", target = 3, description = "达到核心弟子级别"},
        },
        rewards = {
            experience = 200,
            items = {"flying_sword", "sect_manual"},
            sect_points = 100,
        },
    },
    
    realm_master = {
        id = "realm_master",
        name = "秘境之主",
        description = "征服所有修炼秘境",
        type = "exploration",
        requirements = {
            cultivation_level = 5,
        },
        objectives = {
            {type = "enter_realm", realm = "ancient_battlefield", target = 1, description = "进入古战场"},
            {type = "enter_realm", realm = "fire_realm", target = 1, description = "进入地火秘境"},
            {type = "enter_realm", realm = "moon_realm", target = 1, description = "进入阴月秘境"},
            {type = "enter_realm", realm = "thunder_realm", target = 1, description = "进入雷电秘境"},
            {type = "enter_realm", realm = "medicine_valley", target = 1, description = "进入药王谷"},
        },
        rewards = {
            experience = 500,
            items = {"immortal_herb", "sky_metal", "night_pearl"},
            title = "秘境征服者",
        },
    },
}

local QuestManager = Class(function(self, inst)
    self.inst = inst
    
    -- Active quests
    self.active_quests = {}
    
    -- Completed quests
    self.completed_quests = {}
    
    -- Quest progress tracking
    self.quest_progress = {}
    
    -- Available quests (not yet started)
    self.available_quests = {}
    
    -- Quest cooldowns
    self.quest_cooldowns = {}
    
    -- Initialize available quests
    self:RefreshAvailableQuests()
    
    -- Auto-start tutorial quests
    self:CheckAutoStartQuests()
    
end)

function QuestManager:RefreshAvailableQuests()
    self.available_quests = {}
    
    for quest_id, quest_data in pairs(QUEST_DATA) do
        if not self:IsQuestCompleted(quest_id) and not self:IsQuestActive(quest_id) then
            if self:MeetsQuestRequirements(quest_data) then
                table.insert(self.available_quests, quest_id)
            end
        end
    end
end

function QuestManager:CheckAutoStartQuests()
    for quest_id, quest_data in pairs(QUEST_DATA) do
        if quest_data.auto_start and not self:IsQuestCompleted(quest_id) and not self:IsQuestActive(quest_id) then
            if self:MeetsQuestRequirements(quest_data) then
                self:StartQuest(quest_id)
            end
        end
    end
end

function QuestManager:MeetsQuestRequirements(quest_data)
    local req = quest_data.requirements or {}
    
    -- Check cultivation level
    if req.cultivation_level and self.inst.components.cultivation then
        if self.inst.components.cultivation.level < req.cultivation_level then
            return false
        end
    end
    
    -- Check sect membership
    if req.sect_member and self.inst.components.sect_member then
        if not self.inst.components.sect_member.sect then
            return false
        end
    end
    
    -- Check specific sect
    if req.sect and self.inst.components.sect_member then
        if self.inst.components.sect_member.sect ~= req.sect then
            return false
        end
    end
    
    -- Check completed quests
    if req.completed_quests then
        for _, required_quest in ipairs(req.completed_quests) do
            if not self:IsQuestCompleted(required_quest) then
                return false
            end
        end
    end
    
    return true
end

function QuestManager:StartQuest(quest_id)
    local quest_data = QUEST_DATA[quest_id]
    if not quest_data then
        return false, "未知任务"
    end
    
    if self:IsQuestActive(quest_id) then
        return false, "任务已激活"
    end
    
    if self:IsQuestCompleted(quest_id) then
        return false, "任务已完成"
    end
    
    if not self:MeetsQuestRequirements(quest_data) then
        return false, "不满足任务条件"
    end
    
    -- Initialize quest
    self.active_quests[quest_id] = {
        id = quest_id,
        start_time = GetTime(),
        objectives_completed = {},
    }
    
    -- Initialize progress tracking
    self.quest_progress[quest_id] = {}
    for i, objective in ipairs(quest_data.objectives) do
        self.quest_progress[quest_id][i] = 0
    end
    
    -- Trigger quest start event
    self.inst:PushEvent("quest_started", {quest_id = quest_id, quest_data = quest_data})
    
    if self.inst.components.talker then
        self.inst.components.talker:Say("接受任务：" .. quest_data.name, 3)
    end
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Quest] Started quest:", quest_id)
    end
    
    return true
end

function QuestManager:UpdateQuestProgress(quest_id, objective_index, progress)
    if not self:IsQuestActive(quest_id) then
        return
    end
    
    local quest_data = QUEST_DATA[quest_id]
    if not quest_data or not quest_data.objectives[objective_index] then
        return
    end
    
    local objective = quest_data.objectives[objective_index]
    local current_progress = self.quest_progress[quest_id][objective_index] or 0
    local new_progress = math.min(objective.target, current_progress + progress)
    
    self.quest_progress[quest_id][objective_index] = new_progress
    
    -- Check if objective completed
    if new_progress >= objective.target then
        if not self.active_quests[quest_id].objectives_completed[objective_index] then
            self.active_quests[quest_id].objectives_completed[objective_index] = true
            
            if self.inst.components.talker then
                self.inst.components.talker:Say("完成目标：" .. objective.description, 2)
            end
        end
    end
    
    -- Check if quest completed
    self:CheckQuestCompletion(quest_id)
    
    -- Trigger progress event
    self.inst:PushEvent("quest_progress", {
        quest_id = quest_id,
        objective_index = objective_index,
        progress = new_progress,
        target = objective.target
    })
end

function QuestManager:CheckQuestCompletion(quest_id)
    if not self:IsQuestActive(quest_id) then
        return
    end
    
    local quest_data = QUEST_DATA[quest_id]
    if not quest_data then
        return
    end
    
    local active_quest = self.active_quests[quest_id]
    local all_completed = true
    
    for i, objective in ipairs(quest_data.objectives) do
        if not active_quest.objectives_completed[i] then
            all_completed = false
            break
        end
    end
    
    if all_completed then
        self:CompleteQuest(quest_id)
    end
end

function QuestManager:CompleteQuest(quest_id)
    local quest_data = QUEST_DATA[quest_id]
    if not quest_data then
        return
    end
    
    -- Move from active to completed
    local active_quest = self.active_quests[quest_id]
    self.completed_quests[quest_id] = {
        id = quest_id,
        start_time = active_quest.start_time,
        completion_time = GetTime(),
    }
    
    self.active_quests[quest_id] = nil
    self.quest_progress[quest_id] = nil
    
    -- Give rewards
    self:GiveQuestRewards(quest_data.rewards)
    
    -- Trigger completion event
    self.inst:PushEvent("quest_completed", {quest_id = quest_id, quest_data = quest_data})
    
    if self.inst.components.talker then
        self.inst.components.talker:Say("完成任务：" .. quest_data.name, 3)
    end
    
    -- Refresh available quests
    self:RefreshAvailableQuests()
    self:CheckAutoStartQuests()
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Quest] Completed quest:", quest_id)
    end
end

function QuestManager:GiveQuestRewards(rewards)
    if not rewards then
        return
    end
    
    -- Experience reward
    if rewards.experience and self.inst.components.cultivation then
        self.inst.components.cultivation:AddExperience(rewards.experience)
    end
    
    -- Spiritual energy reward
    if rewards.spiritual_energy and self.inst.components.cultivation then
        self.inst.components.cultivation:AddSpiritualEnergy(rewards.spiritual_energy)
    end
    
    -- Item rewards
    if rewards.items and self.inst.components.inventory then
        for _, item_name in ipairs(rewards.items) do
            local item = SpawnPrefab(item_name)
            if item then
                self.inst.components.inventory:GiveItem(item)
            end
        end
    end
    
    -- Sect points reward
    if rewards.sect_points and self.inst.components.sect_member then
        self.inst.components.sect_member:AddSectPoints(rewards.sect_points)
    end
    
    -- Spell unlock reward
    if rewards.unlock_spell and self.inst.components.cultivation then
        self.inst.components.cultivation:UnlockSpell(rewards.unlock_spell)
    end
    
    -- Recipe unlock reward
    if rewards.unlock_recipe then
        -- Could implement recipe unlocking system here
        if self.inst.components.talker then
            self.inst.components.talker:Say("学会了新的炼丹配方！", 2)
        end
    end
    
    -- Title reward
    if rewards.title then
        -- Could implement title system here
        if self.inst.components.talker then
            self.inst.components.talker:Say("获得称号：" .. rewards.title, 3)
        end
    end
end

function QuestManager:IsQuestActive(quest_id)
    return self.active_quests[quest_id] ~= nil
end

function QuestManager:IsQuestCompleted(quest_id)
    return self.completed_quests[quest_id] ~= nil
end

function QuestManager:GetActiveQuests()
    return self.active_quests
end

function QuestManager:GetCompletedQuests()
    return self.completed_quests
end

function QuestManager:GetAvailableQuests()
    return self.available_quests
end

function QuestManager:GetQuestData(quest_id)
    local base_data = QUEST_DATA[quest_id]
    if not base_data then
        return nil
    end

    -- Create localized copy
    local localized_data = {}
    for key, value in pairs(base_data) do
        localized_data[key] = value
    end

    -- Override with localized text if available
    local localized_name = GetLocalizedText("quest_names", quest_id)
    if localized_name and localized_name ~= quest_id then
        localized_data.name = localized_name
    end

    local localized_desc = GetLocalizedText("quest_descriptions", quest_id)
    if localized_desc and localized_desc ~= quest_id then
        localized_data.description = localized_desc
    end

    return localized_data
end

function QuestManager:GetQuestProgress(quest_id)
    return self.quest_progress[quest_id]
end

-- Event handlers for quest progress tracking
function QuestManager:OnMeditation(duration)
    for quest_id, _ in pairs(self.active_quests) do
        local quest_data = QUEST_DATA[quest_id]
        if quest_data then
            for i, objective in ipairs(quest_data.objectives) do
                if objective.type == "meditate" then
                    self:UpdateQuestProgress(quest_id, i, duration)
                end
            end
        end
    end
end

function QuestManager:OnItemCollected(item_name, count)
    for quest_id, _ in pairs(self.active_quests) do
        local quest_data = QUEST_DATA[quest_id]
        if quest_data then
            for i, objective in ipairs(quest_data.objectives) do
                if objective.type == "collect" and objective.item == item_name then
                    self:UpdateQuestProgress(quest_id, i, count or 1)
                end
            end
        end
    end
end

function QuestManager:OnSpellCast(spell_name)
    for quest_id, _ in pairs(self.active_quests) do
        local quest_data = QUEST_DATA[quest_id]
        if quest_data then
            for i, objective in ipairs(quest_data.objectives) do
                if objective.type == "cast_spell" and objective.spell == spell_name then
                    self:UpdateQuestProgress(quest_id, i, 1)
                end
            end
        end
    end
end

function QuestManager:OnRealmEntered(realm_type)
    for quest_id, _ in pairs(self.active_quests) do
        local quest_data = QUEST_DATA[quest_id]
        if quest_data then
            for i, objective in ipairs(quest_data.objectives) do
                if objective.type == "enter_realm" and objective.realm == realm_type then
                    self:UpdateQuestProgress(quest_id, i, 1)
                end
            end
        end
    end
end

function QuestManager:OnItemCrafted(item_name, count)
    for quest_id, _ in pairs(self.active_quests) do
        local quest_data = QUEST_DATA[quest_id]
        if quest_data then
            for i, objective in ipairs(quest_data.objectives) do
                if objective.type == "craft" and objective.item == item_name then
                    self:UpdateQuestProgress(quest_id, i, count or 1)
                end
            end
        end
    end
end

function QuestManager:OnSave()
    return {
        active_quests = self.active_quests,
        completed_quests = self.completed_quests,
        quest_progress = self.quest_progress,
        available_quests = self.available_quests,
        quest_cooldowns = self.quest_cooldowns,
    }
end

function QuestManager:OnLoad(data)
    if data then
        self.active_quests = data.active_quests or {}
        self.completed_quests = data.completed_quests or {}
        self.quest_progress = data.quest_progress or {}
        self.available_quests = data.available_quests or {}
        self.quest_cooldowns = data.quest_cooldowns or {}
        
        -- Refresh available quests after loading
        self:RefreshAvailableQuests()
    end
end

return QuestManager
