-- Realm Status Component
-- Tracks player's current realm status and provides UI feedback

local RealmStatus = Class(function(self, inst)
    self.inst = inst
    self.current_realm = nil
    self.current_portal = nil
    self.entry_time = 0
    self.realm_effects = {}
end)

function RealmStatus:EnterRealm(realm_type, portal)
    self.current_realm = realm_type
    self.current_portal = portal
    self.entry_time = GetTime()
    
    -- Start UI updates
    self:StartStatusUpdates()
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Realm Status] Player entered", realm_type)
    end
end

function RealmStatus:ExitRealm()
    local old_realm = self.current_realm
    
    self.current_realm = nil
    self.current_portal = nil
    self.entry_time = 0
    
    -- Clear realm effects
    self:ClearRealmEffects()
    
    -- Stop UI updates
    self:StopStatusUpdates()
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Realm Status] Player exited", old_realm)
    end
end

function RealmStatus:GetCurrentRealm()
    return self.current_realm
end

function RealmStatus:GetTimeInRealm()
    if self.current_realm then
        return GetTime() - self.entry_time
    end
    return 0
end

function RealmStatus:IsInRealm()
    return self.current_realm ~= nil
end

function RealmStatus:AddRealmEffect(effect_name, effect_data)
    self.realm_effects[effect_name] = effect_data
    
    -- Apply effect to player
    if effect_name == "health_regen" then
        if not self.inst.components.health then
            self.inst:AddComponent("health")
        end
        -- Health regen will be handled in periodic task
    elseif effect_name == "spiritual_energy_boost" then
        if self.inst.components.cultivation then
            self.inst.components.cultivation:ModifyMaxSpiritualEnergy(effect_data.amount)
        end
    elseif effect_name == "experience_multiplier" then
        if self.inst.components.cultivation then
            self.inst.components.cultivation.experience_multiplier = (self.inst.components.cultivation.experience_multiplier or 1) * effect_data.multiplier
        end
    end
end

function RealmStatus:RemoveRealmEffect(effect_name)
    local effect_data = self.realm_effects[effect_name]
    if not effect_data then
        return
    end
    
    -- Remove effect from player
    if effect_name == "spiritual_energy_boost" then
        if self.inst.components.cultivation then
            self.inst.components.cultivation:ModifyMaxSpiritualEnergy(-effect_data.amount)
        end
    elseif effect_name == "experience_multiplier" then
        if self.inst.components.cultivation then
            self.inst.components.cultivation.experience_multiplier = (self.inst.components.cultivation.experience_multiplier or 1) / effect_data.multiplier
        end
    end
    
    self.realm_effects[effect_name] = nil
end

function RealmStatus:ClearRealmEffects()
    for effect_name, _ in pairs(self.realm_effects) do
        self:RemoveRealmEffect(effect_name)
    end
end

function RealmStatus:StartStatusUpdates()
    if self.status_task then
        self.status_task:Cancel()
    end
    
    self.status_task = self.inst:DoPeriodicTask(1, function()
        self:UpdateRealmEffects()
        self:UpdateUI()
    end)
end

function RealmStatus:StopStatusUpdates()
    if self.status_task then
        self.status_task:Cancel()
        self.status_task = nil
    end
end

function RealmStatus:UpdateRealmEffects()
    -- Apply periodic realm effects
    for effect_name, effect_data in pairs(self.realm_effects) do
        if effect_name == "health_regen" then
            if self.inst.components.health and not self.inst.components.health:IsDead() then
                local current_health = self.inst.components.health.currenthealth
                local max_health = self.inst.components.health.maxhealth
                
                if current_health < max_health then
                    local regen_amount = effect_data.amount or 1
                    self.inst.components.health:DoDelta(regen_amount, false, "realm_regen")
                end
            end
        end
    end
end

function RealmStatus:UpdateUI()
    -- This will be called by UI system to update realm status display
    if self.current_realm and self.inst == ThePlayer then
        -- UI updates will be handled by the UI system
        self.inst:PushEvent("realm_status_update", {
            realm = self.current_realm,
            time_in_realm = self:GetTimeInRealm(),
            effects = self.realm_effects
        })
    end
end

function RealmStatus:GetStatusString()
    if not self.current_realm then
        return ""
    end
    
    local realm_name = GetRealmName(self.current_realm) or self.current_realm
    local time_in_realm = self:GetTimeInRealm()
    local minutes = math.floor(time_in_realm / 60)
    local seconds = math.floor(time_in_realm % 60)
    
    return string.format("%s (%02d:%02d)", realm_name, minutes, seconds)
end

function RealmStatus:OnSave()
    return {
        current_realm = self.current_realm,
        entry_time = self.entry_time,
        realm_effects = self.realm_effects,
    }
end

function RealmStatus:OnLoad(data)
    if data then
        self.current_realm = data.current_realm
        self.entry_time = data.entry_time or 0
        self.realm_effects = data.realm_effects or {}
        
        if self.current_realm then
            self:StartStatusUpdates()
        end
    end
end

return RealmStatus
