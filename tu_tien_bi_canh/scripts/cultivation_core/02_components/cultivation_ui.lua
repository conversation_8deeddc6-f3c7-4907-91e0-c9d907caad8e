-- Cultivation UI Component
-- Manages the cultivation interface for players

local CultivationUI = Class(function(self, inst)
    self.inst = inst
    self.is_open = false
    self.screen = nil
end)

function CultivationUI:OpenUI()
    if self.is_open then
        return
    end
    
    -- Load the screen class
    local CultivationScreen = require("screens/cultivation_screen")
    
    -- Create and show the screen
    self.screen = CultivationScreen(self.inst)
    TheFrontEnd:PushScreen(self.screen)
    
    self.is_open = true
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[UI] Cultivation UI opened for", self.inst)
    end
end

function CultivationUI:CloseUI()
    if not self.is_open or not self.screen then
        return
    end
    
    -- Close the screen
    TheFrontEnd:PopScreen(self.screen)
    self.screen = nil
    self.is_open = false
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[UI] Cultivation UI closed for", self.inst)
    end
end

function CultivationUI:ToggleUI()
    if self.is_open then
        self:CloseUI()
    else
        self:OpenUI()
    end
end

function CultivationUI:IsOpen()
    return self.is_open
end

function CultivationUI:RefreshUI()
    if self.is_open and self.screen then
        self.screen:RefreshCurrentTab()
    end
end

-- Auto-refresh when cultivation changes
function CultivationUI:OnSave()
    return {}
end

function CultivationUI:OnLoad(data)
    -- Nothing to load
end

return CultivationUI
