-- Realm Manager Component
-- Handles spawning and managing cultivation realms

local REALM_TYPES = {
    "peach_blossom", -- <PERSON><PERSON><PERSON> (starting realm)
    "heavenly_mountain", -- <PERSON><PERSON><PERSON><PERSON>
    "ancient_battlefield", -- <PERSON><PERSON>
    "fire_realm", -- <PERSON><PERSON><PERSON> Hỏa <PERSON>
    "moon_realm", -- <PERSON><PERSON>
    "thunder_realm", -- <PERSON><PERSON><PERSON>
    "medicine_valley", -- <PERSON><PERSON><PERSON><PERSON> Vương Cốc
}

local function GetValidSpawnLocation(inst)
    -- Find a location away from players and important structures
    local players = {}
    for i, v in ipairs(AllPlayers) do
        if v and v:IsValid() then
            table.insert(players, v:GetPosition())
        end
    end
    
    local attempts = 0
    local max_attempts = 50
    
    while attempts < max_attempts do
        local angle = math.random() * 2 * PI
        local radius = 30 + math.random() * 50 -- 30-80 units from world center
        local x = radius * math.cos(angle)
        local z = radius * math.sin(angle)
        
        -- Check if location is valid and away from players
        local valid = true
        for _, player_pos in ipairs(players) do
            local dist = math.sqrt((x - player_pos.x)^2 + (z - player_pos.z)^2)
            if dist < 20 then -- Too close to player
                valid = false
                break
            end
        end
        
        if valid then
            -- Check if ground is walkable
            local ground = TheWorld.Map:GetTileAtPoint(x, 0, z)
            if ground and ground ~= GROUND.IMPASSABLE and ground ~= GROUND.INVALID then
                return Vector3(x, 0, z)
            end
        end
        
        attempts = attempts + 1
    end
    
    -- Fallback to random location
    local angle = math.random() * 2 * PI
    local radius = 40
    return Vector3(radius * math.cos(angle), 0, radius * math.sin(angle))
end

local function CanSpawnRealm(inst)
    -- Check if conditions are met for realm spawning
    local day = TheWorld.state.cycles
    
    -- Need at least day 15 or a high-level player
    if day < 15 then
        local has_qualified_player = false
        for _, player in ipairs(AllPlayers) do
            if player and player.components.cultivation and player.components.cultivation.level >= 3 then
                has_qualified_player = true
                break
            end
        end
        if not has_qualified_player then
            return false
        end
    end
    
    return true
end

local RealmManager = Class(function(self, inst)
    self.inst = inst
    
    -- Active realms
    self.active_realms = {}
    
    -- Spawn timing
    self.days_since_last_spawn = 0
    self.next_spawn_check = TUNING.CULTIVATION.REALM_SPAWN_INTERVAL
    
    -- Realm history
    self.spawned_realm_count = 0
    self.realm_history = {}
    
    -- Listen for day changes
    inst:WatchWorldState("cycles", function() self:OnNewDay() end)
    
    -- Start the realm spawning system
    self:ScheduleNextSpawnCheck()
    
end)

function RealmManager:OnNewDay()
    self.days_since_last_spawn = self.days_since_last_spawn + 1
    
    -- Check for realm expiration
    self:CheckRealmExpiration()
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Realm Manager] Day", TheWorld.state.cycles, "- Days since last spawn:", self.days_since_last_spawn)
        print("[Realm Manager] Active realms:", #self.active_realms)
    end
end

function RealmManager:ScheduleNextSpawnCheck()
    if self.spawn_check_task then
        self.spawn_check_task:Cancel()
    end
    
    local check_time = self.next_spawn_check * TUNING.TOTAL_DAY_TIME
    self.spawn_check_task = self.inst:DoTaskInTime(check_time, function()
        self:TrySpawnRealm()
        self:ScheduleNextSpawnCheck()
    end)
end

function RealmManager:TrySpawnRealm()
    -- Check if we can spawn a realm
    if not CanSpawnRealm(self.inst) then
        if TUNING.CULTIVATION_MOD.DEBUG_MODE then
            print("[Realm Manager] Conditions not met for realm spawning")
        end
        return false
    end
    
    -- Check spawn chance
    local spawn_chance = TUNING.CULTIVATION.REALM_SPAWN_CHANCE
    if math.random() > spawn_chance then
        if TUNING.CULTIVATION_MOD.DEBUG_MODE then
            print("[Realm Manager] Spawn chance failed:", spawn_chance)
        end
        return false
    end
    
    -- Limit active realms
    if #self.active_realms >= 2 then
        if TUNING.CULTIVATION_MOD.DEBUG_MODE then
            print("[Realm Manager] Too many active realms")
        end
        return false
    end
    
    -- Choose realm type
    local realm_type = self:ChooseRealmType()
    if not realm_type then
        return false
    end
    
    -- Find spawn location
    local spawn_pos = GetValidSpawnLocation(self.inst)
    
    -- Spawn the realm
    return self:SpawnRealm(realm_type, spawn_pos)
end

function RealmManager:ChooseRealmType()
    -- For now, start with peach blossom realm as it's the safest
    local available_types = {"peach_blossom"}
    
    -- Unlock more realm types based on world progression
    local day = TheWorld.state.cycles
    local max_player_level = self:GetMaxPlayerLevel()
    
    if day >= 20 or max_player_level >= 3 then
        table.insert(available_types, "heavenly_mountain")
        table.insert(available_types, "medicine_valley")
    end
    
    if day >= 30 or max_player_level >= 4 then
        table.insert(available_types, "fire_realm")
        table.insert(available_types, "moon_realm")
    end
    
    if day >= 50 or max_player_level >= 5 then
        table.insert(available_types, "thunder_realm")
        table.insert(available_types, "ancient_battlefield")
    end
    
    -- Choose randomly from available types
    return available_types[math.random(#available_types)]
end

function RealmManager:GetMaxPlayerLevel()
    local max_level = 1
    for _, player in ipairs(AllPlayers) do
        if player and player.components.cultivation then
            max_level = math.max(max_level, player.components.cultivation.level)
        end
    end
    return max_level
end

function RealmManager:SpawnRealm(realm_type, position)
    -- Create portal
    local portal = SpawnPrefab("cultivation_portal")
    if not portal then
        print("[Realm Manager] Failed to spawn portal prefab")
        return false
    end
    
    portal.Transform:SetPosition(position:Get())
    portal.realm_type = realm_type
    
    -- Configure portal
    if portal.components.cultivation_portal then
        portal.components.cultivation_portal:SetRealmType(realm_type)
        portal.components.cultivation_portal:SetDuration(TUNING.CULTIVATION.REALM_DURATION)
    end
    
    -- Add to active realms
    local realm_data = {
        portal = portal,
        type = realm_type,
        spawn_day = TheWorld.state.cycles,
        position = position,
        players_entered = {},
    }
    
    table.insert(self.active_realms, realm_data)
    self.spawned_realm_count = self.spawned_realm_count + 1
    self.days_since_last_spawn = 0
    
    -- Add to history
    table.insert(self.realm_history, {
        type = realm_type,
        spawn_day = TheWorld.state.cycles,
        position = position,
    })
    
    -- Announce to players
    self:AnnounceRealmSpawn(realm_type, position)
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Realm Manager] Spawned realm:", realm_type, "at", position)
    end
    
    return true
end

function RealmManager:AnnounceRealmSpawn(realm_type, position)
    -- Visual and audio effects
    local fx = SpawnPrefab("cultivation_portal_spawn_fx")
    if fx then
        fx.Transform:SetPosition(position:Get())
    end
    
    -- Message to all players
    local realm_names = {
        peach_blossom = "桃花秘境",
        heavenly_mountain = "天山秘境", 
        ancient_battlefield = "古战场",
        fire_realm = "地火秘境",
        moon_realm = "阴月秘境",
        thunder_realm = "雷电秘境",
        medicine_valley = "药王谷",
    }
    
    local realm_name = realm_names[realm_type] or realm_type
    local message = "一处" .. realm_name .. "已经出现！"
    
    for _, player in ipairs(AllPlayers) do
        if player and player.components.talker then
            player.components.talker:Say(message, 3, true)
        end
    end
    
    -- Screen flash effect
    TheWorld:PushEvent("ms_sendlightningstrike", position)
end

function RealmManager:CheckRealmExpiration()
    for i = #self.active_realms, 1, -1 do
        local realm = self.active_realms[i]
        local days_active = TheWorld.state.cycles - realm.spawn_day
        
        -- Warning before closing
        if days_active >= TUNING.CULTIVATION.REALM_DURATION - TUNING.CULTIVATION.REALM_WARNING_TIME then
            self:WarnRealmClosing(realm)
        end
        
        -- Close expired realms
        if days_active >= TUNING.CULTIVATION.REALM_DURATION then
            self:CloseRealm(i)
        end
    end
end

function RealmManager:WarnRealmClosing(realm)
    if realm.warning_sent then
        return
    end
    
    realm.warning_sent = true
    
    -- Warning message
    local message = "秘境即将关闭！"
    for _, player in ipairs(AllPlayers) do
        if player and player.components.talker then
            player.components.talker:Say(message, 3, true)
        end
    end
end

function RealmManager:CloseRealm(index)
    local realm = self.active_realms[index]
    if not realm then
        return
    end
    
    -- Teleport any players still inside back to the portal
    if realm.portal and realm.portal:IsValid() then
        if realm.portal.components.cultivation_portal then
            realm.portal.components.cultivation_portal:EjectAllPlayers()
        end
        
        -- Remove portal
        realm.portal:Remove()
    end
    
    -- Remove from active list
    table.remove(self.active_realms, index)
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Realm Manager] Closed realm:", realm.type)
    end
end

function RealmManager:GetActiveRealms()
    return self.active_realms
end

function RealmManager:GetRealmHistory()
    return self.realm_history
end

function RealmManager:OnSave()
    local data = {
        days_since_last_spawn = self.days_since_last_spawn,
        spawned_realm_count = self.spawned_realm_count,
        realm_history = self.realm_history,
    }
    
    -- Save active realms (portals will save themselves)
    data.active_realms = {}
    for _, realm in ipairs(self.active_realms) do
        table.insert(data.active_realms, {
            type = realm.type,
            spawn_day = realm.spawn_day,
            position = realm.position,
            warning_sent = realm.warning_sent,
        })
    end
    
    return data
end

function RealmManager:OnLoad(data)
    if data then
        self.days_since_last_spawn = data.days_since_last_spawn or 0
        self.spawned_realm_count = data.spawned_realm_count or 0
        self.realm_history = data.realm_history or {}
        
        -- Active realms will be restored when portals load
        if data.active_realms then
            self.active_realms = data.active_realms
        end
    end
end

return RealmManager
