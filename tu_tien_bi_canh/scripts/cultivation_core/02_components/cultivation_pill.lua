-- Cultivation Pill Component

local CultivationPill = Class(function(self, inst)
    self.inst = inst
    self.pill_type = "basic"
    self.potency = 1
    self.effects = {}
end)

function CultivationPill:SetPillType(pill_type)
    self.pill_type = pill_type or "basic"
end

function CultivationPill:SetPotency(potency)
    self.potency = potency or 1
end

function CultivationPill:AddEffect(effect_name, value)
    self.effects[effect_name] = value
end

function CultivationPill:OnSave()
    return {
        pill_type = self.pill_type,
        potency = self.potency,
        effects = self.effects,
    }
end

function CultivationPill:OnLoad(data)
    if data then
        self.pill_type = data.pill_type or "basic"
        self.potency = data.potency or 1
        self.effects = data.effects or {}
    end
end

return CultivationPill
