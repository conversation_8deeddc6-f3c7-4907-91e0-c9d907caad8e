-- NPC Memory Component
-- Handles NPC memory of player interactions and relationships

local NPCMemory = Class(function(self, inst)
    self.inst = inst

    -- Player relationships (-100 to 100)
    self.player_relationships = {}

    -- Memory of specific events
    self.player_memories = {}

    -- Gossip system - what this NPC knows about players
    self.gossip = {}

    -- Last interaction times
    self.last_interactions = {}

    -- Memory decay settings
    self.memory_decay_rate = 0.1 -- How fast memories fade
    self.relationship_decay_rate = 0.05 -- How fast relationships decay

    -- Start memory decay task
    self:StartMemoryDecay()
end)

function NPCMemory:GetRelationship(player)
    if not player then
        return 0
    end

    local player_id = player.userid or tostring(player)
    return self.player_relationships[player_id] or 0
end

function NPCMemory:ModifyRelationship(player, change)
    if not player or not change then
        return
    end

    local player_id = player.userid or tostring(player)
    local current = self.player_relationships[player_id] or 0
    local new_value = math.max(-100, math.min(100, current + change))

    self.player_relationships[player_id] = new_value
    self.last_interactions[player_id] = GetTime()

    -- Record this relationship change as a memory
    self:AddMemory(player, "relationship_change", {
        change = change,
        new_value = new_value,
        reason = "interaction"
    })

    -- Trigger relationship change event
    self.inst:PushEvent("npc_relationship_changed", {
        player = player,
        old_value = current,
        new_value = new_value,
        change = change
    })

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[NPC Memory]", self.inst.prefab, "relationship with", player.name or "player", "changed by", change, "to", new_value)
    end
end

function NPCMemory:AddMemory(player, memory_type, data)
    if not player then
        return
    end

    local player_id = player.userid or tostring(player)

    if not self.player_memories[player_id] then
        self.player_memories[player_id] = {}
    end

    local memory = {
        type = memory_type,
        data = data or {},
        timestamp = GetTime(),
        importance = self:CalculateMemoryImportance(memory_type, data),
    }

    table.insert(self.player_memories[player_id], memory)

    -- Limit memory count per player
    local max_memories = 20
    if #self.player_memories[player_id] > max_memories then
        -- Remove oldest, least important memories
        table.sort(self.player_memories[player_id], function(a, b)
            return (a.importance + (GetTime() - a.timestamp) * 0.001) > (b.importance + (GetTime() - b.timestamp) * 0.001)
        end)

        while #self.player_memories[player_id] > max_memories do
            table.remove(self.player_memories[player_id])
        end
    end
end

function NPCMemory:CalculateMemoryImportance(memory_type, data)
    local importance_values = {
        first_meeting = 5,
        gift_received = 3,
        trade_completed = 2,
        attacked_by = 8,
        helped_by = 6,
        relationship_change = math.abs(data.change or 0) * 0.5,
        quest_completed = 7,
        spell_learned = 4,
        sect_interaction = 5,
    }

    return importance_values[memory_type] or 1
end

function NPCMemory:GetMemories(player, memory_type)
    if not player then
        return {}
    end

    local player_id = player.userid or tostring(player)
    local memories = self.player_memories[player_id] or {}

    if memory_type then
        local filtered = {}
        for _, memory in ipairs(memories) do
            if memory.type == memory_type then
                table.insert(filtered, memory)
            end
        end
        return filtered
    end

    return memories
end

function NPCMemory:HasMemoryOf(player, memory_type)
    local memories = self:GetMemories(player, memory_type)
    return #memories > 0
end

function NPCMemory:GetMostRecentMemory(player, memory_type)
    local memories = self:GetMemories(player, memory_type)

    if #memories == 0 then
        return nil
    end

    -- Sort by timestamp, most recent first
    table.sort(memories, function(a, b) return a.timestamp > b.timestamp end)
    return memories[1]
end

function NPCMemory:AddGossip(about_player, gossip_type, data)
    if not about_player then
        return
    end

    local player_id = about_player.userid or tostring(about_player)

    if not self.gossip[player_id] then
        self.gossip[player_id] = {}
    end

    local gossip_entry = {
        type = gossip_type,
        data = data or {},
        timestamp = GetTime(),
        source = "observed", -- Could be "told_by_other_npc", "observed", etc.
    }

    table.insert(self.gossip[player_id], gossip_entry)

    -- Limit gossip entries
    if #self.gossip[player_id] > 10 then
        table.remove(self.gossip[player_id], 1) -- Remove oldest
    end
end

function NPCMemory:GetGossip(about_player, gossip_type)
    if not about_player then
        return {}
    end

    local player_id = about_player.userid or tostring(about_player)
    local gossip_list = self.gossip[player_id] or {}

    if gossip_type then
        local filtered = {}
        for _, gossip in ipairs(gossip_list) do
            if gossip.type == gossip_type then
                table.insert(filtered, gossip)
            end
        end
        return filtered
    end

    return gossip_list
end

function NPCMemory:ShareGossipWith(other_npc, about_player)
    -- NPCs can share information about players
    if not other_npc or not other_npc.components.npc_memory or not about_player then
        return
    end

    local my_gossip = self:GetGossip(about_player)
    local my_relationship = self:GetRelationship(about_player)

    -- Share some gossip
    for _, gossip in ipairs(my_gossip) do
        if math.random() < 0.3 then -- 30% chance to share each piece
            other_npc.components.npc_memory:AddGossip(about_player, gossip.type, gossip.data)
        end
    end

    -- Share relationship opinion (with some bias)
    if math.abs(my_relationship) > 20 then
        local shared_opinion = my_relationship * (0.3 + math.random() * 0.4) -- 30-70% of original opinion
        other_npc.components.npc_memory:ModifyRelationship(about_player, shared_opinion * 0.1)
    end
end

function NPCMemory:GetDialogueModifier(player)
    -- Returns dialogue modifiers based on memory
    local relationship = self:GetRelationship(player)
    local memories = self:GetMemories(player)

    local modifiers = {
        relationship_level = "neutral",
        recent_interactions = {},
        special_memories = {},
    }

    -- Relationship level
    if relationship > 50 then
        modifiers.relationship_level = "close_friend"
    elseif relationship > 20 then
        modifiers.relationship_level = "friend"
    elseif relationship > 0 then
        modifiers.relationship_level = "acquaintance"
    elseif relationship > -20 then
        modifiers.relationship_level = "neutral"
    elseif relationship > -50 then
        modifiers.relationship_level = "dislike"
    else
        modifiers.relationship_level = "enemy"
    end

    -- Recent interactions (last 3)
    local recent = {}
    for i = math.max(1, #memories - 2), #memories do
        if memories[i] then
            table.insert(recent, memories[i])
        end
    end
    modifiers.recent_interactions = recent

    -- Special memories
    for _, memory in ipairs(memories) do
        if memory.importance >= 5 then
            table.insert(modifiers.special_memories, memory)
        end
    end

    return modifiers
end

function NPCMemory:StartMemoryDecay()
    -- Gradually decay memories and relationships over time
    self.decay_task = self.inst:DoPeriodicTask(TUNING.TOTAL_DAY_TIME, function() -- Once per day
        self:DecayMemories()
        self:DecayRelationships()
    end)
end

function NPCMemory:DecayMemories()
    local current_time = GetTime()

    for player_id, memories in pairs(self.player_memories) do
        for i = #memories, 1, -1 do
            local memory = memories[i]
            local age_days = (current_time - memory.timestamp) / TUNING.TOTAL_DAY_TIME

            -- Remove very old, unimportant memories
            if age_days > 30 and memory.importance < 3 then
                table.remove(memories, i)
            elseif age_days > 60 and memory.importance < 5 then
                table.remove(memories, i)
            end
        end

        -- Clean up empty memory lists
        if #memories == 0 then
            self.player_memories[player_id] = nil
        end
    end
end

function NPCMemory:DecayRelationships()
    local current_time = GetTime()

    for player_id, relationship in pairs(self.player_relationships) do
        local last_interaction = self.last_interactions[player_id] or 0
        local days_since_interaction = (current_time - last_interaction) / TUNING.TOTAL_DAY_TIME

        -- Decay relationships toward neutral over time
        if days_since_interaction > 7 then -- After a week of no interaction
            local decay_amount = self.relationship_decay_rate * days_since_interaction

            if relationship > 0 then
                self.player_relationships[player_id] = math.max(0, relationship - decay_amount)
            elseif relationship < 0 then
                self.player_relationships[player_id] = math.min(0, relationship + decay_amount)
            end
        end
    end
end

function NPCMemory:OnSave()
    return {
        player_relationships = self.player_relationships,
        player_memories = self.player_memories,
        gossip = self.gossip,
        last_interactions = self.last_interactions,
        memory_decay_rate = self.memory_decay_rate,
        relationship_decay_rate = self.relationship_decay_rate,
    }
end

function NPCMemory:OnLoad(data)
    if data then
        self.player_relationships = data.player_relationships or {}
        self.player_memories = data.player_memories or {}
        self.gossip = data.gossip or {}
        self.last_interactions = data.last_interactions or {}
        self.memory_decay_rate = data.memory_decay_rate or 0.1
        self.relationship_decay_rate = data.relationship_decay_rate or 0.05

        -- Restart decay task
        self:StartMemoryDecay()
    end
end

function NPCMemory:OnRemoveEntity()
    if self.decay_task then
        self.decay_task:Cancel()
        self.decay_task = nil
    end
end

return NPCMemory
