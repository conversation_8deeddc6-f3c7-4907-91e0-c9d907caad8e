-- NPC Cultivator Component
-- Handles NPC cultivation level, alignment, and behaviors

local ALIGNMENT_TYPES = {
    "friendly",   -- <PERSON><PERSON><PERSON> th<PERSON>ện - Will help players
    "neutral",    -- Trung lập - Depends on player actions
    "hostile",    -- <PERSON><PERSON><PERSON> đ<PERSON>ch - Will attack players
    "righteous",  -- <PERSON><PERSON><PERSON> đ<PERSON> - Helps good karma players
    "demonic",    -- <PERSON> đ<PERSON>o - Helps evil karma players
}

local function OnAlignmentChanged(self, alignment)
    if alignment then
        self.inst:AddTag("npc_" .. alignment)

        -- Remove old alignment tags
        for _, old_alignment in ipairs(ALIGNMENT_TYPES) do
            if old_alignment ~= alignment then
                self.inst:RemoveTag("npc_" .. old_alignment)
            end
        end

        -- Update behavior based on alignment
        self:UpdateBehavior()
    end
end

local NPCCultivator = Class(function(self, inst)
    self.inst = inst

    -- Cultivation properties
    self.level = 1
    self.sect = nil
    self.alignment = "neutral"

    -- Personality traits
    self.personality = {
        aggression = 0.5,    -- 0 = peaceful, 1 = aggressive
        generosity = 0.5,    -- 0 = greedy, 1 = generous
        wisdom = 0.5,        -- 0 = simple, 1 = wise
        patience = 0.5,      -- 0 = impatient, 1 = patient
    }

    -- Relationship with player factions
    self.faction_standing = {
        righteous = 0,  -- -100 to 100
        demonic = 0,
        neutral = 0,
    }

    -- Trading preferences
    self.trade_preferences = {}
    self.trade_inventory = {}

    -- Dialogue state
    self.dialogue_state = "greeting"
    self.last_interaction_time = 0

    -- Combat preferences
    self.combat_style = "defensive" -- defensive, aggressive, supportive

end, nil, {
    alignment = OnAlignmentChanged,
})

function NPCCultivator:SetLevel(level)
    self.level = math.max(1, math.min(8, level or 1))

    -- Apply level-based stats
    if self.inst.components.health then
        local base_health = 100
        local health = base_health + (self.level - 1) * 50
        self.inst.components.health:SetMaxHealth(health)
    end

    if self.inst.components.combat then
        local base_damage = 20
        local damage = base_damage + (self.level - 1) * 10
        self.inst.components.combat:SetDefaultDamage(damage)
    end
end

function NPCCultivator:SetAlignment(alignment)
    if alignment and table.contains(ALIGNMENT_TYPES, alignment) then
        self.alignment = alignment
    end
end

function NPCCultivator:SetSect(sect_name)
    self.sect = sect_name

    -- Add sect-specific behaviors
    if sect_name then
        self.inst:AddTag("sect_" .. sect_name)
    end
end

function NPCCultivator:SetPersonality(trait, value)
    if self.personality[trait] then
        self.personality[trait] = math.max(0, math.min(1, value or 0.5))
        self:UpdateBehavior()
    end
end

function NPCCultivator:UpdateBehavior()
    -- Update NPC behavior based on alignment and personality
    local alignment = self.alignment
    local personality = self.personality

    -- Combat behavior
    if self.inst.components.combat then
        if alignment == "hostile" or alignment == "demonic" then
            self.inst.components.combat:SetRetargetFunction(3, function(inst)
                return FindEntity(inst, 8, function(guy)
                    return guy:HasTag("player") and inst.components.combat:CanTarget(guy)
                end)
            end)
        elseif alignment == "friendly" or alignment == "righteous" then
            self.inst.components.combat:SetRetargetFunction(1, function() return nil end)
        end
    end

    -- Trading behavior
    if self.inst.components.trader then
        local generosity = personality.generosity
        -- More generous NPCs give better trades
        self.trade_generosity_modifier = 0.5 + generosity * 0.5
    end
end

function NPCCultivator:GetRelationshipWithPlayer(player)
    if not player or not self.inst.components.npc_memory then
        return 0
    end

    return self.inst.components.npc_memory:GetRelationship(player)
end

function NPCCultivator:CanInteractWithPlayer(player)
    if not player then
        return false
    end

    local relationship = self:GetRelationshipWithPlayer(player)

    -- Hostile NPCs won't interact if relationship is too low
    if self.alignment == "hostile" and relationship < -20 then
        return false
    end

    -- Demonic NPCs prefer players with evil karma
    if self.alignment == "demonic" and player.components.cultivation then
        local evil_karma = player.components.cultivation.evil_karma or 0
        local righteous_karma = player.components.cultivation.righteous_karma or 0
        if righteous_karma > evil_karma + 20 then
            return false
        end
    end

    -- Righteous NPCs prefer players with good karma
    if self.alignment == "righteous" and player.components.cultivation then
        local evil_karma = player.components.cultivation.evil_karma or 0
        local righteous_karma = player.components.cultivation.righteous_karma or 0
        if evil_karma > righteous_karma + 20 then
            return false
        end
    end

    return true
end

function NPCCultivator:GetDialogueOptions(player)
    if not self:CanInteractWithPlayer(player) then
        return {
            {text = "...", response = GetMessage("npc_no_talk") or "I don't want to talk to you.", action = "end"}
        }
    end

    local relationship = self:GetRelationshipWithPlayer(player)
    local options = {}

    -- Basic greeting
    table.insert(options, {
        text = GetUIText("hello") or "Hello",
        response = self:GetGreetingResponse(player, relationship),
        action = "greeting"
    })

    -- Trading option
    if self.inst.components.trader and relationship > -10 then
        table.insert(options, {
            text = GetUIText("want_to_trade") or "I want to trade",
            response = GetMessage("npc_trade_response") or "Let's see what you have.",
            action = "trade"
        })
    end

    -- Cultivation advice
    if relationship > 10 then
        table.insert(options, {
            text = GetUIText("ask_cultivation_advice") or "Can you give me cultivation advice?",
            response = self:GetAdviceResponse(player),
            action = "advice"
        })
    end

    -- Sect information
    if self.sect and relationship > 20 then
        table.insert(options, {
            text = GetMessage("ask_about_sect", {sect = GetSectName(self.sect) or self.sect}) or "Tell me about " .. self.sect,
            response = self:GetSectInfoResponse(),
            action = "sect_info"
        })
    end

    return options
end

function NPCCultivator:GetGreetingResponse(player, relationship)
    local responses = {}

    if relationship > 50 then
        responses = {
            GetMessage("npc_greeting_friend_1") or "Old friend, we meet again!",
            GetMessage("npc_greeting_friend_2") or "Good to see you again.",
            GetMessage("npc_greeting_friend_3") or "Your cultivation has improved."
        }
    elseif relationship > 0 then
        responses = {
            GetMessage("npc_greeting_neutral_1") or "Hello, young cultivator.",
            GetMessage("npc_greeting_neutral_2") or "Welcome here.",
            GetMessage("npc_greeting_neutral_3") or "May your cultivation go smoothly."
        }
    elseif relationship > -20 then
        responses = {
            GetMessage("npc_greeting_suspicious_1") or "Who are you?",
            GetMessage("npc_greeting_suspicious_2") or "Strangers are not welcome here.",
            GetMessage("npc_greeting_suspicious_3") or "Watch your words and actions."
        }
    else
        responses = {
            GetMessage("npc_greeting_hostile_1") or "Get lost!",
            GetMessage("npc_greeting_hostile_2") or "I don't want to see you!",
            GetMessage("npc_greeting_hostile_3") or "You troublemaker!"
        }
    end

    return responses[math.random(#responses)]
end

function NPCCultivator:GetAdviceResponse(player)
    local player_level = 1
    if player.components.cultivation then
        player_level = player.components.cultivation.level
    end

    local advice = {
        [1] = GetMessage("npc_advice_level_1") or "The cultivation path begins with meditation, focus on inner peace.",
        [2] = GetMessage("npc_advice_level_2") or "Foundation Building requires solid foundation, don't be hasty.",
        [3] = GetMessage("npc_advice_level_3") or "Golden Core is an important turning point, requires much spiritual energy.",
        [4] = GetMessage("npc_advice_level_4") or "Nascent Soul begins to touch advanced spells, be careful with control.",
        [5] = GetMessage("npc_advice_level_5") or "Spirit Transform requires understanding the way of heaven and earth.",
        [6] = GetMessage("npc_advice_level_6") or "Void Refining is close to immortal realm, need to transcend worldly matters.",
        [7] = GetMessage("npc_advice_level_7") or "Integration requires unity with heaven and earth, needs high comprehension.",
        [8] = GetMessage("npc_advice_level_8") or "Great Vehicle is immortal realm, I can only look up to it.",
    }

    return advice[player_level] or GetMessage("npc_advice_high_level") or "Your cultivation is already high, I have little to teach."
end

function NPCCultivator:GetSectInfoResponse()
    local sect_info = {
        peach_blossom_sect = GetMessage("npc_sect_info_peach_blossom") or "Peach Blossom Sect is known for healing and natural way.",
        heaven_sword_sect = GetMessage("npc_sect_info_heaven_sword") or "Heavenly Sword Sect is the first righteous sect, famous for unparalleled sword techniques.",
        fire_cloud_sect = GetMessage("npc_sect_info_fire_cloud") or "Fire Cloud Sect controls the power of fire, disciples are all fire magic experts.",
        demonic_path_sect = GetMessage("npc_sect_info_demonic") or "Demonic sects... those people walk the evil path, be careful.",
        default = GetMessage("npc_sect_info_default") or "My sect has a long history and deep foundation."
    }

    return sect_info[self.sect] or sect_info.default
end

function NPCCultivator:OnPlayerAction(player, action_type, context)
    -- React to player actions
    if not player or not self.inst.components.npc_memory then
        return
    end

    local relationship_change = 0
    local response = nil

    if action_type == "gift" then
        relationship_change = 5
        response = GetMessage("npc_thanks_gift") or "Thank you for the gift."
    elseif action_type == "attack" then
        relationship_change = -20
        response = GetMessage("npc_angry_attack") or "How dare you attack me!"
    elseif action_type == "help" then
        relationship_change = 10
        response = GetMessage("npc_thanks_help") or "Thank you for your help."
    elseif action_type == "trade" then
        relationship_change = 2
        response = GetMessage("npc_trade_pleasant") or "Pleasant trade."
    end

    if relationship_change ~= 0 then
        self.inst.components.npc_memory:ModifyRelationship(player, relationship_change)
    end

    if response and self.inst.components.talker then
        self.inst.components.talker:Say(response, 2)
    end
end

function NPCCultivator:OnSave()
    return {
        level = self.level,
        sect = self.sect,
        alignment = self.alignment,
        personality = self.personality,
        faction_standing = self.faction_standing,
        trade_preferences = self.trade_preferences,
        dialogue_state = self.dialogue_state,
        combat_style = self.combat_style,
    }
end

function NPCCultivator:OnLoad(data)
    if data then
        self.level = data.level or 1
        self.sect = data.sect
        self.alignment = data.alignment or "neutral"
        self.personality = data.personality or self.personality
        self.faction_standing = data.faction_standing or self.faction_standing
        self.trade_preferences = data.trade_preferences or {}
        self.dialogue_state = data.dialogue_state or "greeting"
        self.combat_style = data.combat_style or "defensive"

        -- Apply loaded settings
        self:SetLevel(self.level)
        self:UpdateBehavior()
    end
end

return NPCCultivator
