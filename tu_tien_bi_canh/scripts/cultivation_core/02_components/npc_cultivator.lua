-- NPC Cultivator Component
-- Handles NPC cultivation level, alignment, and behaviors

local ALIGNMENT_TYPES = {
    "friendly",   -- 友善 - Will help players
    "neutral",    -- 中立 - Depends on player actions
    "hostile",    -- 敌对 - Will attack players
    "righteous",  -- 正道 - Helps good karma players
    "demonic",    -- 魔道 - Helps evil karma players
}

local function OnAlignmentChanged(self, alignment)
    if alignment then
        self.inst:AddTag("npc_" .. alignment)

        -- Remove old alignment tags
        for _, old_alignment in ipairs(ALIGNMENT_TYPES) do
            if old_alignment ~= alignment then
                self.inst:RemoveTag("npc_" .. old_alignment)
            end
        end

        -- Update behavior based on alignment
        self:UpdateBehavior()
    end
end

local NPCCultivator = Class(function(self, inst)
    self.inst = inst

    -- Cultivation properties
    self.level = 1
    self.sect = nil
    self.alignment = "neutral"

    -- Personality traits
    self.personality = {
        aggression = 0.5,    -- 0 = peaceful, 1 = aggressive
        generosity = 0.5,    -- 0 = greedy, 1 = generous
        wisdom = 0.5,        -- 0 = simple, 1 = wise
        patience = 0.5,      -- 0 = impatient, 1 = patient
    }

    -- Relationship with player factions
    self.faction_standing = {
        righteous = 0,  -- -100 to 100
        demonic = 0,
        neutral = 0,
    }

    -- Trading preferences
    self.trade_preferences = {}
    self.trade_inventory = {}

    -- Dialogue state
    self.dialogue_state = "greeting"
    self.last_interaction_time = 0

    -- Combat preferences
    self.combat_style = "defensive" -- defensive, aggressive, supportive

end, nil, {
    alignment = OnAlignmentChanged,
})

function NPCCultivator:SetLevel(level)
    self.level = math.max(1, math.min(8, level or 1))

    -- Apply level-based stats
    if self.inst.components.health then
        local base_health = 100
        local health = base_health + (self.level - 1) * 50
        self.inst.components.health:SetMaxHealth(health)
    end

    if self.inst.components.combat then
        local base_damage = 20
        local damage = base_damage + (self.level - 1) * 10
        self.inst.components.combat:SetDefaultDamage(damage)
    end
end

function NPCCultivator:SetAlignment(alignment)
    if alignment and table.contains(ALIGNMENT_TYPES, alignment) then
        self.alignment = alignment
    end
end

function NPCCultivator:SetSect(sect_name)
    self.sect = sect_name

    -- Add sect-specific behaviors
    if sect_name then
        self.inst:AddTag("sect_" .. sect_name)
    end
end

function NPCCultivator:SetPersonality(trait, value)
    if self.personality[trait] then
        self.personality[trait] = math.max(0, math.min(1, value or 0.5))
        self:UpdateBehavior()
    end
end

function NPCCultivator:UpdateBehavior()
    -- Update NPC behavior based on alignment and personality
    local alignment = self.alignment
    local personality = self.personality

    -- Combat behavior
    if self.inst.components.combat then
        if alignment == "hostile" or alignment == "demonic" then
            self.inst.components.combat:SetRetargetFunction(3, function(inst)
                return FindEntity(inst, 8, function(guy)
                    return guy:HasTag("player") and inst.components.combat:CanTarget(guy)
                end)
            end)
        elseif alignment == "friendly" or alignment == "righteous" then
            self.inst.components.combat:SetRetargetFunction(1, function() return nil end)
        end
    end

    -- Trading behavior
    if self.inst.components.trader then
        local generosity = personality.generosity
        -- More generous NPCs give better trades
        self.trade_generosity_modifier = 0.5 + generosity * 0.5
    end
end

function NPCCultivator:GetRelationshipWithPlayer(player)
    if not player or not self.inst.components.npc_memory then
        return 0
    end

    return self.inst.components.npc_memory:GetRelationship(player)
end

function NPCCultivator:CanInteractWithPlayer(player)
    if not player then
        return false
    end

    local relationship = self:GetRelationshipWithPlayer(player)

    -- Hostile NPCs won't interact if relationship is too low
    if self.alignment == "hostile" and relationship < -20 then
        return false
    end

    -- Demonic NPCs prefer players with evil karma
    if self.alignment == "demonic" and player.components.cultivation then
        local evil_karma = player.components.cultivation.evil_karma or 0
        local righteous_karma = player.components.cultivation.righteous_karma or 0
        if righteous_karma > evil_karma + 20 then
            return false
        end
    end

    -- Righteous NPCs prefer players with good karma
    if self.alignment == "righteous" and player.components.cultivation then
        local evil_karma = player.components.cultivation.evil_karma or 0
        local righteous_karma = player.components.cultivation.righteous_karma or 0
        if evil_karma > righteous_karma + 20 then
            return false
        end
    end

    return true
end

function NPCCultivator:GetDialogueOptions(player)
    if not self:CanInteractWithPlayer(player) then
        return {
            {text = "...", response = "我不想和你说话。", action = "end"}
        }
    end

    local relationship = self:GetRelationshipWithPlayer(player)
    local options = {}

    -- Basic greeting
    table.insert(options, {
        text = "你好",
        response = self:GetGreetingResponse(player, relationship),
        action = "greeting"
    })

    -- Trading option
    if self.inst.components.trader and relationship > -10 then
        table.insert(options, {
            text = "我想交易",
            response = "看看你有什么好东西吧。",
            action = "trade"
        })
    end

    -- Cultivation advice
    if relationship > 10 then
        table.insert(options, {
            text = "能给我一些修炼建议吗？",
            response = self:GetAdviceResponse(player),
            action = "advice"
        })
    end

    -- Sect information
    if self.sect and relationship > 20 then
        table.insert(options, {
            text = "告诉我关于" .. self.sect .. "的事情",
            response = self:GetSectInfoResponse(),
            action = "sect_info"
        })
    end

    return options
end

function NPCCultivator:GetGreetingResponse(player, relationship)
    local responses = {}

    if relationship > 50 then
        responses = {
            "老朋友，又见面了！",
            "很高兴再次见到你。",
            "你的修为又有进步了。"
        }
    elseif relationship > 0 then
        responses = {
            "你好，年轻的修士。",
            "欢迎来到这里。",
            "愿你修行顺利。"
        }
    elseif relationship > -20 then
        responses = {
            "你是谁？",
            "这里不欢迎陌生人。",
            "小心你的言行。"
        }
    else
        responses = {
            "滚开！",
            "我不想见到你！",
            "你这个麻烦制造者！"
        }
    end

    return responses[math.random(#responses)]
end

function NPCCultivator:GetAdviceResponse(player)
    local player_level = 1
    if player.components.cultivation then
        player_level = player.components.cultivation.level
    end

    local advice = {
        [1] = "修炼之路始于冥想，多多静心感悟。",
        [2] = "筑基期需要稳固根基，不可急躁。",
        [3] = "金丹期是一个重要的转折点，需要大量的灵气。",
        [4] = "元婴期开始接触更高深的法术，要小心控制。",
        [5] = "化神期需要领悟天地之道，多观察自然。",
        [6] = "炼虚期已经接近仙人境界，需要超脱世俗。",
        [7] = "合体期需要与天地合一，这需要极高的悟性。",
        [8] = "大乘期已是仙人境界，我也只能仰望了。",
    }

    return advice[player_level] or "你的修为已经很高了，我能教你的不多。"
end

function NPCCultivator:GetSectInfoResponse()
    local sect_info = {
        peach_blossom_sect = "桃花宗以治愈和自然之道闻名，我们相信和谐与平衡。",
        heaven_sword_sect = "天剑宗是正道第一大派，以剑法无双著称。",
        fire_cloud_sect = "火云派掌控烈火之力，门下弟子都是火法高手。",
        demonic_path_sect = "魔道宗门...那些人走的是邪恶之路，小心为上。",
        default = "我的宗门有着悠久的历史和深厚的底蕴。"
    }

    return sect_info[self.sect] or sect_info.default
end

function NPCCultivator:OnPlayerAction(player, action_type, context)
    -- React to player actions
    if not player or not self.inst.components.npc_memory then
        return
    end

    local relationship_change = 0
    local response = nil

    if action_type == "gift" then
        relationship_change = 5
        response = "谢谢你的礼物。"
    elseif action_type == "attack" then
        relationship_change = -20
        response = "你竟敢攻击我！"
    elseif action_type == "help" then
        relationship_change = 10
        response = "感谢你的帮助。"
    elseif action_type == "trade" then
        relationship_change = 2
        response = "交易愉快。"
    end

    if relationship_change ~= 0 then
        self.inst.components.npc_memory:ModifyRelationship(player, relationship_change)
    end

    if response and self.inst.components.talker then
        self.inst.components.talker:Say(response, 2)
    end
end

function NPCCultivator:OnSave()
    return {
        level = self.level,
        sect = self.sect,
        alignment = self.alignment,
        personality = self.personality,
        faction_standing = self.faction_standing,
        trade_preferences = self.trade_preferences,
        dialogue_state = self.dialogue_state,
        combat_style = self.combat_style,
    }
end

function NPCCultivator:OnLoad(data)
    if data then
        self.level = data.level or 1
        self.sect = data.sect
        self.alignment = data.alignment or "neutral"
        self.personality = data.personality or self.personality
        self.faction_standing = data.faction_standing or self.faction_standing
        self.trade_preferences = data.trade_preferences or {}
        self.dialogue_state = data.dialogue_state or "greeting"
        self.combat_style = data.combat_style or "defensive"

        -- Apply loaded settings
        self:SetLevel(self.level)
        self:UpdateBehavior()
    end
end

return NPCCultivator
