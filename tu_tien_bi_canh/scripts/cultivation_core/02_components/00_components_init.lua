-- Components System Initialization

-- Load core cultivation component
modimport("scripts/cultivation_core/02_components/cultivation.lua")

-- Load realm manager component
modimport("scripts/cultivation_core/02_components/realm_manager.lua")

-- Load spell caster component
modimport("scripts/cultivation_core/02_components/spell_caster.lua")

-- Load NPC components
modimport("scripts/cultivation_core/02_components/npc_cultivator.lua")
modimport("scripts/cultivation_core/02_components/npc_memory.lua")
modimport("scripts/cultivation_core/02_components/npc_dialogue.lua")

-- Load item components
modimport("scripts/cultivation_core/02_components/spiritual_item.lua")
modimport("scripts/cultivation_core/02_components/cultivation_pill.lua")

-- Load sect system components
modimport("scripts/cultivation_core/02_components/sect_member.lua")

-- Load alchemy components
modimport("scripts/cultivation_core/02_components/alchemy_furnace.lua")

-- Load quest system components
modimport("scripts/cultivation_core/02_components/quest_manager.lua")

-- Load world event system
modimport("scripts/cultivation_core/07_world_events/world_event_manager.lua")

-- Load UI component
modimport("scripts/cultivation_core/02_components/cultivation_ui.lua")

print("[Tu Tiên Bí Cảnh] Components loaded")
