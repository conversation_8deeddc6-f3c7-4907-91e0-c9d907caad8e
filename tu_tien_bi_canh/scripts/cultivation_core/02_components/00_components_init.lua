-- Components System Initialization

-- Load core cultivation component
modimport("scripts/cultivation_core/02_components/cultivation.lua")

-- Load realm manager component
modimport("scripts/cultivation_core/02_components/realm_manager.lua")

-- Load spell caster component
modimport("scripts/cultivation_core/02_components/spell_caster.lua")

-- Load NPC components
modimport("scripts/cultivation_core/02_components/npc_cultivator.lua")
modimport("scripts/cultivation_core/02_components/npc_memory.lua")
modimport("scripts/cultivation_core/02_components/npc_dialogue.lua")

-- Load item components
modimport("scripts/cultivation_core/02_components/spiritual_item.lua")
modimport("scripts/cultivation_core/02_components/cultivation_pill.lua")

-- Load sect system components
modimport("scripts/cultivation_core/02_components/sect_member.lua")

print("[Tu Tiên Bí Cảnh] Components loaded")
