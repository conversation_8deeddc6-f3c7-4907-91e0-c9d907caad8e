-- Safe Cultivation Component
-- Simplified version that won't crash if tuning is missing

local Cultivation = Class(function(self, inst)
    self.inst = inst
    
    -- Basic cultivation stats
    self.level = 1
    self.experience = 0
    self.spiritual_energy = 50
    self.max_spiritual_energy = 100
    self.meditation_active = false
    
    -- Level names
    self.level_names = {
        [1] = "Qi Refining",
        [2] = "Foundation Building", 
        [3] = "Golden Core",
        [4] = "Nascent Soul",
        [5] = "Soul Formation",
        [6] = "Void Refinement",
        [7] = "Body Integration",
        [8] = "Great Ascension"
    }
    
    -- Experience requirements
    self.exp_requirements = {
        [1] = 100,   -- Qi Refining -> Foundation
        [2] = 300,   -- Foundation -> Golden Core
        [3] = 600,   -- Golden Core -> Nascent Soul
        [4] = 1000,  -- Nascent Soul -> Soul Formation
        [5] = 1500,  -- Soul Formation -> Void Refinement
        [6] = 2100,  -- Void Refinement -> Body Integration
        [7] = 2800,  -- Body Integration -> Great Ascension
        [8] = 9999   -- Max level
    }
    
    -- Meditation task
    self.meditation_task = nil
end)

function Cultivation:GetLevelName()
    return self.level_names[self.level] or "Unknown Level"
end

function Cultivation:GetExpRequiredForNextLevel()
    return self.exp_requirements[self.level] or 9999
end

function Cultivation:CanLevelUp()
    return self.experience >= self:GetExpRequiredForNextLevel() and self.level < 8
end

function Cultivation:AddExperience(amount)
    if not amount or amount <= 0 then return end
    
    local old_exp = self.experience
    self.experience = self.experience + amount
    
    -- Check for level up
    while self:CanLevelUp() do
        self:LevelUp()
    end
    
    -- Notify UI
    self.inst:PushEvent("cultivation_exp_changed", {
        old_exp = old_exp,
        new_exp = self.experience,
        required = self:GetExpRequiredForNextLevel()
    })
end

function Cultivation:LevelUp()
    if not self:CanLevelUp() then return end
    
    local old_level = self.level
    self.level = self.level + 1
    
    -- Increase max spiritual energy
    self.max_spiritual_energy = self.max_spiritual_energy + 20
    self.spiritual_energy = self.max_spiritual_energy -- Full restore on level up
    
    -- Show level up message
    if self.inst.components.talker then
        local message = string.format("Level up! %s -> %s", 
            self.level_names[old_level], 
            self.level_names[self.level])
        self.inst.components.talker:Say(message, 3)
    end
    
    -- Notify events
    self.inst:PushEvent("cultivation_level_up", {
        old_level = old_level,
        new_level = self.level,
        level_name = self:GetLevelName()
    })
    
    print(string.format("[Cultivation] %s leveled up to %s!", 
        self.inst.name or "Player", self:GetLevelName()))
end

function Cultivation:StartMeditation()
    if self.meditation_active then return end
    
    self.meditation_active = true
    
    -- Show meditation message
    if self.inst.components.talker then
        self.inst.components.talker:Say("Starting meditation...", 2)
    end
    
    -- Start meditation task
    self.meditation_task = self.inst:DoPeriodicTask(2, function()
        self:OnMeditate()
    end)
    
    print("[Cultivation] Meditation started")
end

function Cultivation:StopMeditation()
    if not self.meditation_active then return end
    
    self.meditation_active = false
    
    if self.meditation_task then
        self.meditation_task:Cancel()
        self.meditation_task = nil
    end
    
    if self.inst.components.talker then
        self.inst.components.talker:Say("Meditation ended.", 2)
    end
    
    print("[Cultivation] Meditation stopped")
end

function Cultivation:OnMeditate()
    if not self.meditation_active then return end
    
    -- Gain experience from meditation
    local exp_gain = 5 * (TUNING.CULTIVATION_MOD.CULTIVATION_SPEED or 1.0)
    self:AddExperience(exp_gain)
    
    -- Restore spiritual energy
    local energy_gain = 10 * (TUNING.CULTIVATION_MOD.SPIRITUAL_ENERGY_RATE or 1.0)
    self:AddSpiritualEnergy(energy_gain)
    
    print(string.format("[Cultivation] Meditation: +%d exp, +%d energy", exp_gain, energy_gain))
end

function Cultivation:AddSpiritualEnergy(amount)
    if not amount then return end
    
    local old_energy = self.spiritual_energy
    self.spiritual_energy = math.min(self.spiritual_energy + amount, self.max_spiritual_energy)
    
    -- Notify UI
    self.inst:PushEvent("spiritual_energy_changed", {
        old_energy = old_energy,
        new_energy = self.spiritual_energy,
        max_energy = self.max_spiritual_energy
    })
end

function Cultivation:ConsumeSpiritualEnergy(amount)
    if not amount or amount <= 0 then return true end
    
    if self.spiritual_energy >= amount then
        self.spiritual_energy = self.spiritual_energy - amount
        
        -- Notify UI
        self.inst:PushEvent("spiritual_energy_changed", {
            new_energy = self.spiritual_energy,
            max_energy = self.max_spiritual_energy
        })
        
        return true
    end
    
    return false
end

function Cultivation:GetSpiritualEnergyPercent()
    return self.spiritual_energy / self.max_spiritual_energy
end

function Cultivation:OnSave()
    return {
        level = self.level,
        experience = self.experience,
        spiritual_energy = self.spiritual_energy,
        max_spiritual_energy = self.max_spiritual_energy,
        meditation_active = self.meditation_active
    }
end

function Cultivation:OnLoad(data)
    if data then
        self.level = data.level or 1
        self.experience = data.experience or 0
        self.spiritual_energy = data.spiritual_energy or 50
        self.max_spiritual_energy = data.max_spiritual_energy or 100
        
        if data.meditation_active then
            self:StartMeditation()
        end
    end
end

function Cultivation:GetDebugString()
    return string.format("Level: %s (%d), Exp: %d/%d, Energy: %d/%d, Meditating: %s",
        self:GetLevelName(),
        self.level,
        self.experience,
        self:GetExpRequiredForNextLevel(),
        self.spiritual_energy,
        self.max_spiritual_energy,
        tostring(self.meditation_active))
end

return Cultivation
