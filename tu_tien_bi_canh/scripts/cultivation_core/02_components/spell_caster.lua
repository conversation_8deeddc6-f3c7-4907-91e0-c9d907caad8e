-- Spell Caster Component
-- Handles casting cultivation spells

local SPELL_DATA = {
    -- Five Elements - Basic Spells (Level 1-3)
    metal_spike = {
        name = "金刺术",
        element = "metal",
        level_required = 1,
        spiritual_cost = 10,
        cooldown = 5,
        damage = 25,
        range = 8,
        description = "发射锋利的金属尖刺攻击敌人",
    },

    wood_bind = {
        name = "木缚术",
        element = "wood",
        level_required = 1,
        spiritual_cost = 15,
        cooldown = 8,
        duration = 5,
        range = 6,
        description = "用藤蔓束缚敌人，使其无法移动",
    },

    water_shield = {
        name = "水盾术",
        element = "water",
        level_required = 2,
        spiritual_cost = 20,
        cooldown = 15,
        duration = 30,
        absorption = 50,
        description = "创造水盾保护自己，吸收伤害",
    },

    fire_ball = {
        name = "火球术",
        element = "fire",
        level_required = 2,
        spiritual_cost = 18,
        cooldown = 6,
        damage = 35,
        range = 10,
        description = "发射火球攻击敌人，造成火焰伤害",
    },

    earth_spike = {
        name = "土刺术",
        element = "earth",
        level_required = 3,
        spiritual_cost = 22,
        cooldown = 7,
        damage = 40,
        range = 8,
        description = "从地面升起尖锐的土刺",
    },

    -- Five Elements - Intermediate Spells (Level 3-4)
    metal_rain = {
        name = "金雨术",
        element = "metal",
        level_required = 3,
        spiritual_cost = 35,
        cooldown = 12,
        damage = 20,
        range = 12,
        area = 5,
        description = "召唤金属雨攻击大范围敌人",
    },

    wood_growth = {
        name = "催生术",
        element = "wood",
        level_required = 4,
        spiritual_cost = 30,
        cooldown = 20,
        duration = 60,
        description = "催生植物快速生长，恢复生命力",
    },

    water_prison = {
        name = "水牢术",
        element = "water",
        level_required = 4,
        spiritual_cost = 40,
        cooldown = 18,
        duration = 8,
        range = 8,
        description = "用水牢困住敌人，造成持续伤害",
    },

    fire_tornado = {
        name = "火龙卷",
        element = "fire",
        level_required = 5,
        spiritual_cost = 45,
        cooldown = 15,
        damage = 60,
        range = 10,
        duration = 5,
        description = "召唤火焰龙卷风，造成大量伤害",
    },

    earth_armor = {
        name = "土甲术",
        element = "earth",
        level_required = 5,
        spiritual_cost = 35,
        cooldown = 25,
        duration = 45,
        defense = 30,
        description = "用大地之力形成护甲，大幅提升防御",
    },

    -- Heaven Earth - Advanced Spells (Level 5-6)
    lightning_strike = {
        name = "雷击术",
        element = "heaven",
        level_required = 6,
        spiritual_cost = 50,
        cooldown = 10,
        damage = 80,
        range = 15,
        description = "召唤天雷攻击敌人，造成巨大伤害",
    },

    void_step = {
        name = "虚空步",
        element = "heaven",
        level_required = 6,
        spiritual_cost = 40,
        cooldown = 20,
        range = 20,
        description = "瞬间移动到指定位置",
    },

    gravity_well = {
        name = "重力井",
        element = "earth",
        level_required = 7,
        spiritual_cost = 60,
        cooldown = 30,
        duration = 10,
        range = 12,
        area = 6,
        description = "创造重力场，拉拽并伤害敌人",
    },

    time_slow = {
        name = "时间缓流",
        element = "heaven",
        level_required = 7,
        spiritual_cost = 70,
        cooldown = 45,
        duration = 15,
        area = 10,
        description = "减缓区域内时间流速",
    },

    -- Yin Yang - Ultimate Spells (Level 7-8)
    divine_shield = {
        name = "神圣护盾",
        element = "yang",
        level_required = 8,
        spiritual_cost = 80,
        cooldown = 60,
        duration = 20,
        description = "神圣之力护体，免疫所有伤害",
    },

    soul_burn = {
        name = "焚魂术",
        element = "yin",
        level_required = 8,
        spiritual_cost = 90,
        cooldown = 45,
        damage = 120,
        range = 8,
        description = "直接攻击敌人灵魂，造成真实伤害",
    },
}

local function OnSpiritualEnergyChanged(self, energy)
    -- Update available spells based on energy
    self:RefreshAvailableSpells()
end

local SpellCaster = Class(function(self, inst)
    self.inst = inst
    
    -- Spell cooldowns
    self.spell_cooldowns = {}
    
    -- Active spell effects
    self.active_effects = {}
    
    -- Spell casting state
    self.is_casting = false
    self.current_spell = nil
    self.cast_start_time = 0
    
    -- Listen for spiritual energy changes
    if inst.components.cultivation then
        inst:ListenForEvent("spiritual_energy_changed", function(inst, data)
            OnSpiritualEnergyChanged(self, data.energy)
        end)
    end
    
end)

function SpellCaster:CanCastSpell(spell_name)
    local spell_data = SPELL_DATA[spell_name]
    if not spell_data then
        return false, "未知法术"
    end
    
    -- Check if player has unlocked this spell
    if not self:HasSpell(spell_name) then
        return false, "法术未解锁"
    end
    
    -- Check cultivation level requirement
    if self.inst.components.cultivation then
        if self.inst.components.cultivation.level < spell_data.level_required then
            return false, "修为不足"
        end
    else
        return false, "未修炼"
    end
    
    -- Check spiritual energy cost
    if self.inst.components.cultivation then
        if self.inst.components.cultivation.spiritual_energy < spell_data.spiritual_cost then
            return false, "灵气不足"
        end
    end
    
    -- Check cooldown
    if self:IsSpellOnCooldown(spell_name) then
        local remaining = self:GetSpellCooldownRemaining(spell_name)
        return false, string.format("冷却中 (%.1f秒)", remaining)
    end
    
    -- Check if already casting
    if self.is_casting then
        return false, "正在施法"
    end
    
    return true
end

function SpellCaster:HasSpell(spell_name)
    if self.inst.components.cultivation then
        return self.inst.components.cultivation:HasSpell(spell_name)
    end
    return false
end

function SpellCaster:CastSpell(spell_name, target, position)
    local can_cast, reason = self:CanCastSpell(spell_name)
    if not can_cast then
        if self.inst.components.talker then
            self.inst.components.talker:Say(reason, 2)
        end
        return false
    end
    
    local spell_data = SPELL_DATA[spell_name]
    
    -- Start casting
    self.is_casting = true
    self.current_spell = spell_name
    self.cast_start_time = GetTime()
    
    -- Consume spiritual energy
    if self.inst.components.cultivation then
        self.inst.components.cultivation:ConsumeSpiritualEnergy(spell_data.spiritual_cost)
    end
    
    -- Visual casting effect
    self:StartCastingEffects(spell_name)
    
    -- Cast time (most spells are instant for now)
    local cast_time = spell_data.cast_time or 0.5
    
    self.inst:DoTaskInTime(cast_time, function()
        self:ExecuteSpell(spell_name, target, position)
        self:FinishCasting(spell_name)
    end)
    
    return true
end

function SpellCaster:ExecuteSpell(spell_name, target, position)
    local spell_data = SPELL_DATA[spell_name]
    if not spell_data then
        return
    end
    
    -- Execute spell based on type
    if spell_name == "metal_spike" then
        self:CastMetalSpike(target, position)
    elseif spell_name == "wood_bind" then
        self:CastWoodBind(target)
    elseif spell_name == "water_shield" then
        self:CastWaterShield()
    elseif spell_name == "fire_ball" then
        self:CastFireBall(target, position)
    elseif spell_name == "earth_spike" then
        self:CastEarthSpike(target, position)
    end
    
    -- Sound effect
    if self.inst.SoundEmitter then
        self.inst.SoundEmitter:PlaySound("cultivation/spell_cast")
    end
    
    -- Add experience for casting
    if self.inst.components.cultivation then
        self.inst.components.cultivation:AddExperience(2)
    end
end

function SpellCaster:CastMetalSpike(target, position)
    local spell_data = SPELL_DATA.metal_spike
    
    if target and target:IsValid() then
        -- Create projectile
        local projectile = SpawnPrefab("cultivation_metal_spike")
        if projectile then
            projectile.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
            
            -- Set target and damage
            if projectile.components.projectile then
                projectile.components.projectile:Throw(self.inst, target, self.inst)
            end
            
            if projectile.components.weapon then
                projectile.components.weapon:SetDamage(spell_data.damage)
            end
        end
    end
end

function SpellCaster:CastWoodBind(target)
    local spell_data = SPELL_DATA.wood_bind
    
    if target and target:IsValid() and target.components.locomotor then
        -- Bind target
        target.components.locomotor:Stop()
        target:AddTag("cultivation_bound")
        
        -- Visual effect
        local fx = SpawnPrefab("cultivation_wood_bind_fx")
        if fx then
            fx.Transform:SetPosition(target.Transform:GetWorldPosition())
        end
        
        -- Remove bind after duration
        target:DoTaskInTime(spell_data.duration, function()
            if target:IsValid() then
                target:RemoveTag("cultivation_bound")
            end
        end)
        
        if target.components.talker then
            target.components.talker:Say("被藤蔓束缚了！", 2)
        end
    end
end

function SpellCaster:CastWaterShield()
    local spell_data = SPELL_DATA.water_shield
    
    -- Add shield effect
    if not self.inst.components.cultivation_shield then
        self.inst:AddComponent("cultivation_shield")
    end
    
    self.inst.components.cultivation_shield:AddShield("water", spell_data.absorption, spell_data.duration)
    
    -- Visual effect
    local fx = SpawnPrefab("cultivation_water_shield_fx")
    if fx then
        fx.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
        fx.entity:SetParent(self.inst.entity)
    end
    
    if self.inst.components.talker then
        self.inst.components.talker:Say("水盾护体！", 2)
    end
end

function SpellCaster:CastFireBall(target, position)
    local spell_data = SPELL_DATA.fire_ball
    
    local target_pos = position
    if target and target:IsValid() then
        target_pos = target:GetPosition()
    end
    
    if target_pos then
        -- Create fireball projectile
        local fireball = SpawnPrefab("cultivation_fireball")
        if fireball then
            fireball.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
            
            if fireball.components.projectile then
                fireball.components.projectile:Throw(self.inst, target_pos, self.inst)
            end
            
            if fireball.components.weapon then
                fireball.components.weapon:SetDamage(spell_data.damage)
            end
        end
    end
end

function SpellCaster:CastEarthSpike(target, position)
    local spell_data = SPELL_DATA.earth_spike
    
    local target_pos = position
    if target and target:IsValid() then
        target_pos = target:GetPosition()
    end
    
    if target_pos then
        -- Create earth spike at target location
        local spike = SpawnPrefab("cultivation_earth_spike")
        if spike then
            spike.Transform:SetPosition(target_pos:Get())
            
            -- Damage nearby enemies
            local ents = TheSim:FindEntities(target_pos.x, target_pos.y, target_pos.z, 2, {"_combat"}, {"player", "cultivation_bound"})
            for _, ent in ipairs(ents) do
                if ent.components.combat then
                    ent.components.combat:GetAttacked(self.inst, spell_data.damage)
                end
            end
        end
    end
end

function SpellCaster:StartCastingEffects(spell_name)
    -- Visual casting animation
    if self.inst.AnimState then
        self.inst.AnimState:PlayAnimation("spell_cast")
    end
    
    -- Casting particle effect
    local fx = SpawnPrefab("cultivation_casting_fx")
    if fx then
        fx.Transform:SetPosition(self.inst.Transform:GetWorldPosition())
        fx.entity:SetParent(self.inst.entity)
    end
end

function SpellCaster:FinishCasting(spell_name)
    self.is_casting = false
    self.current_spell = nil
    
    -- Start cooldown
    self:StartSpellCooldown(spell_name)
    
    -- Trigger spell cast event
    self.inst:PushEvent("spell_cast", {spell = spell_name})
end

function SpellCaster:StartSpellCooldown(spell_name)
    local spell_data = SPELL_DATA[spell_name]
    if spell_data and spell_data.cooldown then
        self.spell_cooldowns[spell_name] = GetTime() + spell_data.cooldown
    end
end

function SpellCaster:IsSpellOnCooldown(spell_name)
    local cooldown_end = self.spell_cooldowns[spell_name]
    return cooldown_end and GetTime() < cooldown_end
end

function SpellCaster:GetSpellCooldownRemaining(spell_name)
    local cooldown_end = self.spell_cooldowns[spell_name]
    if cooldown_end then
        return math.max(0, cooldown_end - GetTime())
    end
    return 0
end

function SpellCaster:GetSpellData(spell_name)
    return SPELL_DATA[spell_name]
end

function SpellCaster:GetAllSpellData()
    return SPELL_DATA
end

function SpellCaster:RefreshAvailableSpells()
    -- This could be used to update UI or other systems
    self.inst:PushEvent("spells_refreshed")
end

function SpellCaster:OnSave()
    return {
        spell_cooldowns = self.spell_cooldowns,
        active_effects = self.active_effects,
    }
end

function SpellCaster:OnLoad(data)
    if data then
        self.spell_cooldowns = data.spell_cooldowns or {}
        self.active_effects = data.active_effects or {}
    end
end

return SpellCaster
