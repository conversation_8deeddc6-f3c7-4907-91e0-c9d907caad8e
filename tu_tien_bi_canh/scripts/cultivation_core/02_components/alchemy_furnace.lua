-- Alchemy Furnace Component
-- Handles pill crafting and alchemy mechanics

local PILL_RECIPES = {
    -- Basic Pills (Level 1-2)
    basic_healing_pill = {
        ingredients = {
            {item = "peach_blossom_flower", count = 3},
            {item = "spiritual_stone", count = 2},
        },
        result = "basic_healing_pill",
        result_count = 2,
        success_rate = 0.8,
        cultivation_level_required = 1,
        crafting_time = 30,
    },
    
    spiritual_energy_pill = {
        ingredients = {
            {item = "cloud_talisman_grass", count = 2},
            {item = "butterfly_essence", count = 1},
            {item = "spiritual_stone", count = 3},
        },
        result = "spiritual_energy_pill",
        result_count = 1,
        success_rate = 0.7,
        cultivation_level_required = 2,
        crafting_time = 45,
    },
    
    -- Intermediate Pills (Level 3-4)
    foundation_pill = {
        ingredients = {
            {item = "heaven_ginseng", count = 1},
            {item = "spring_water", count = 2},
            {item = "fire_crystal", count = 1},
            {item = "spiritual_stone", count = 5},
        },
        result = "foundation_pill",
        result_count = 1,
        success_rate = 0.6,
        cultivation_level_required = 3,
        crafting_time = 120,
    },
    
    strength_enhancement_pill = {
        ingredients = {
            {item = "mountain_crystal", count = 2},
            {item = "fire_crystal", count = 1},
            {item = "spiritual_stone", count = 4},
        },
        result = "strength_enhancement_pill",
        result_count = 2,
        success_rate = 0.7,
        cultivation_level_required = 3,
        crafting_time = 60,
    },
    
    -- Advanced Pills (Level 5+)
    golden_core_pill = {
        ingredients = {
            {item = "celestial_dew", count = 1},
            {item = "heaven_ginseng", count = 2},
            {item = "mountain_crystal", count = 3},
            {item = "spiritual_stone", count = 10},
        },
        result = "golden_core_pill",
        result_count = 1,
        success_rate = 0.4,
        cultivation_level_required = 5,
        crafting_time = 300,
    },
}

local AlchemyFurnace = Class(function(self, inst)
    self.inst = inst
    
    -- Furnace state
    self.is_active = false
    self.current_recipe = nil
    self.crafting_progress = 0
    self.crafting_task = nil
    
    -- Furnace properties
    self.furnace_level = 1 -- 1-5, affects success rate
    self.temperature = 0 -- 0-100, affects crafting
    self.stability = 100 -- 100-0, decreases during crafting
    
    -- Ingredients storage
    self.ingredients = {}
    
    -- Crafting history
    self.successful_crafts = 0
    self.failed_crafts = 0
    
end)

function AlchemyFurnace:CanCraftRecipe(recipe_name)
    local recipe = PILL_RECIPES[recipe_name]
    if not recipe then
        return false, GetMessage("unknown_recipe") or "Unknown recipe"
    end
    
    -- Check if furnace is busy
    if self.is_active then
        return false, GetMessage("furnace_in_use") or "Furnace is in use"
    end
    
    -- Check ingredients
    for _, ingredient in ipairs(recipe.ingredients) do
        local available = self:GetIngredientCount(ingredient.item)
        if available < ingredient.count then
            local item_name = GetItemName(ingredient.item) or ingredient.item
            local message = GetMessage("insufficient_materials", {item = item_name}) or "Insufficient materials: " .. item_name
            return false, message
        end
    end
    
    return true
end

function AlchemyFurnace:StartCrafting(recipe_name, crafter)
    local can_craft, reason = self:CanCraftRecipe(recipe_name)
    if not can_craft then
        return false, reason
    end
    
    local recipe = PILL_RECIPES[recipe_name]
    
    -- Check crafter's cultivation level
    if crafter and crafter.components.cultivation then
        if crafter.components.cultivation.level < recipe.cultivation_level_required then
            return false, GetMessage("cultivation_insufficient_for_alchemy") or "Cultivation insufficient for this pill"
        end
    end
    
    -- Consume ingredients
    for _, ingredient in ipairs(recipe.ingredients) do
        self:ConsumeIngredient(ingredient.item, ingredient.count)
    end
    
    -- Start crafting process
    self.is_active = true
    self.current_recipe = recipe_name
    self.crafting_progress = 0
    self.temperature = 50 -- Start at medium temperature
    self.stability = 100
    
    -- Visual and audio effects
    self.inst:PushEvent("alchemy_start", {recipe = recipe_name, crafter = crafter})
    
    -- Start crafting task
    self.crafting_task = self.inst:DoPeriodicTask(1, function()
        self:UpdateCrafting(crafter)
    end)
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Alchemy] Started crafting:", recipe_name)
    end
    
    return true
end

function AlchemyFurnace:UpdateCrafting(crafter)
    if not self.is_active or not self.current_recipe then
        return
    end
    
    local recipe = PILL_RECIPES[self.current_recipe]
    if not recipe then
        self:FailCrafting(GetMessage("recipe_error") or "Recipe error")
        return
    end
    
    -- Update progress
    local progress_rate = 1 / recipe.crafting_time -- Progress per second
    self.crafting_progress = self.crafting_progress + progress_rate
    
    -- Update temperature and stability
    self:UpdateTemperature()
    self:UpdateStability()
    
    -- Check for failure conditions
    if self.temperature > 90 or self.temperature < 10 then
        self:FailCrafting(GetMessage("temperature_out_of_control") or "Temperature out of control")
        return
    end
    
    if self.stability < 20 then
        self:FailCrafting(GetMessage("furnace_unstable") or "Furnace unstable")
        return
    end
    
    -- Check if crafting is complete
    if self.crafting_progress >= 1.0 then
        self:CompleteCrafting(crafter)
    end
    
    -- Send progress update
    self.inst:PushEvent("alchemy_progress", {
        progress = self.crafting_progress,
        temperature = self.temperature,
        stability = self.stability
    })
end

function AlchemyFurnace:UpdateTemperature()
    -- Temperature fluctuates naturally
    local temp_change = (math.random() - 0.5) * 5 -- -2.5 to +2.5
    self.temperature = math.max(0, math.min(100, self.temperature + temp_change))
end

function AlchemyFurnace:UpdateStability()
    -- Stability decreases over time, faster at extreme temperatures
    local stability_loss = 1
    
    if self.temperature > 80 or self.temperature < 20 then
        stability_loss = stability_loss + 2
    end
    
    -- Higher furnace level = more stable
    stability_loss = stability_loss * (1 - (self.furnace_level - 1) * 0.1)
    
    self.stability = math.max(0, self.stability - stability_loss)
end

function AlchemyFurnace:CompleteCrafting(crafter)
    if not self.current_recipe then
        return
    end
    
    local recipe = PILL_RECIPES[self.current_recipe]
    local success_rate = recipe.success_rate
    
    -- Modify success rate based on furnace level and crafter skill
    success_rate = success_rate + (self.furnace_level - 1) * 0.1
    
    if crafter and crafter.components.cultivation then
        local skill_bonus = (crafter.components.cultivation.level - recipe.cultivation_level_required) * 0.05
        success_rate = success_rate + skill_bonus
    end
    
    -- Temperature and stability affect success rate
    local temp_penalty = math.abs(self.temperature - 50) * 0.01 -- Optimal temp is 50
    local stability_bonus = (self.stability - 50) * 0.002
    success_rate = success_rate - temp_penalty + stability_bonus
    
    success_rate = math.max(0.1, math.min(0.95, success_rate)) -- Clamp between 10% and 95%
    
    -- Determine success
    local success = math.random() < success_rate
    
    if success then
        self:SucceedCrafting(crafter)
    else
        self:FailCrafting(GetMessage("crafting_failed") or "Crafting failed")
    end
end

function AlchemyFurnace:SucceedCrafting(crafter)
    local recipe = PILL_RECIPES[self.current_recipe]
    
    -- Create result items
    for i = 1, recipe.result_count do
        local item = SpawnPrefab(recipe.result)
        if item then
            -- Drop item near furnace
            local x, y, z = self.inst.Transform:GetWorldPosition()
            item.Transform:SetPosition(x + math.random(-1, 1), y, z + math.random(-1, 1))
        end
    end
    
    -- Give experience to crafter
    if crafter and crafter.components.cultivation then
        local exp_gain = recipe.cultivation_level_required * 10
        crafter.components.cultivation:AddExperience(exp_gain)
    end
    
    -- Update statistics
    self.successful_crafts = self.successful_crafts + 1
    
    -- Effects
    self.inst:PushEvent("alchemy_success", {
        recipe = self.current_recipe,
        crafter = crafter,
        result = recipe.result,
        count = recipe.result_count
    })
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Alchemy] Successfully crafted:", self.current_recipe)
    end
    
    self:ResetCrafting()
end

function AlchemyFurnace:FailCrafting(reason)
    -- Update statistics
    self.failed_crafts = self.failed_crafts + 1
    
    -- Effects
    self.inst:PushEvent("alchemy_failure", {
        recipe = self.current_recipe,
        reason = reason
    })
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[Alchemy] Crafting failed:", self.current_recipe, "Reason:", reason)
    end
    
    self:ResetCrafting()
end

function AlchemyFurnace:ResetCrafting()
    self.is_active = false
    self.current_recipe = nil
    self.crafting_progress = 0
    self.temperature = 0
    self.stability = 100
    
    if self.crafting_task then
        self.crafting_task:Cancel()
        self.crafting_task = nil
    end
end

function AlchemyFurnace:AddIngredient(item_name, count)
    count = count or 1
    self.ingredients[item_name] = (self.ingredients[item_name] or 0) + count
end

function AlchemyFurnace:GetIngredientCount(item_name)
    return self.ingredients[item_name] or 0
end

function AlchemyFurnace:ConsumeIngredient(item_name, count)
    local available = self:GetIngredientCount(item_name)
    local consumed = math.min(available, count)
    self.ingredients[item_name] = available - consumed
    
    if self.ingredients[item_name] <= 0 then
        self.ingredients[item_name] = nil
    end
    
    return consumed
end

function AlchemyFurnace:GetAvailableRecipes()
    local available = {}
    
    for recipe_name, recipe in pairs(PILL_RECIPES) do
        local can_craft = self:CanCraftRecipe(recipe_name)
        if can_craft then
            table.insert(available, recipe_name)
        end
    end
    
    return available
end

function AlchemyFurnace:GetRecipeData(recipe_name)
    return PILL_RECIPES[recipe_name]
end

function AlchemyFurnace:GetAllRecipes()
    return PILL_RECIPES
end

function AlchemyFurnace:OnSave()
    return {
        furnace_level = self.furnace_level,
        ingredients = self.ingredients,
        successful_crafts = self.successful_crafts,
        failed_crafts = self.failed_crafts,
        is_active = self.is_active,
        current_recipe = self.current_recipe,
        crafting_progress = self.crafting_progress,
        temperature = self.temperature,
        stability = self.stability,
    }
end

function AlchemyFurnace:OnLoad(data)
    if data then
        self.furnace_level = data.furnace_level or 1
        self.ingredients = data.ingredients or {}
        self.successful_crafts = data.successful_crafts or 0
        self.failed_crafts = data.failed_crafts or 0
        
        -- Don't restore active crafting state to avoid issues
        self.is_active = false
        self.current_recipe = nil
        self.crafting_progress = 0
        self.temperature = data.temperature or 0
        self.stability = data.stability or 100
    end
end

return AlchemyFurnace
