-- Safe Spell Caster Component
-- Simplified version that won't crash if spell prefabs are missing

local SpellCaster = Class(function(self, inst)
    self.inst = inst
    
    -- Available spells
    self.known_spells = {}
    self.spell_cooldowns = {}
    
    -- Basic spells that don't require prefabs
    self.basic_spells = {
        heal = {
            name = "Healing Light",
            cost = 20,
            cooldown = 5,
            description = "Restore health using spiritual energy"
        },
        energy_burst = {
            name = "Energy Burst", 
            cost = 15,
            cooldown = 3,
            description = "Release spiritual energy for damage"
        },
        meditation_boost = {
            name = "Meditation Boost",
            cost = 10,
            cooldown = 10,
            description = "Temporarily increase meditation effectiveness"
        },
        spiritual_shield = {
            name = "Spiritual Shield",
            cost = 25,
            cooldown = 15,
            description = "Create protective barrier"
        }
    }
    
    -- Learn basic spells
    for spell_id, spell_data in pairs(self.basic_spells) do
        self.known_spells[spell_id] = true
    end
end)

function SpellCaster:CanCastSpell(spell_id)
    if not spell_id then return false end
    
    -- Check if spell is known
    if not self.known_spells[spell_id] then
        return false, "Spell not learned"
    end
    
    -- Check if spell exists
    local spell_data = self.basic_spells[spell_id]
    if not spell_data then
        return false, "Spell not found"
    end
    
    -- Check spiritual energy
    if not self.inst.components.cultivation then
        return false, "No cultivation component"
    end
    
    if self.inst.components.cultivation.spiritual_energy < spell_data.cost then
        return false, "Not enough spiritual energy"
    end
    
    -- Check cooldown
    local current_time = GetTime()
    if self.spell_cooldowns[spell_id] and current_time < self.spell_cooldowns[spell_id] then
        return false, "Spell on cooldown"
    end
    
    return true
end

function SpellCaster:CastSpell(spell_id, target)
    local can_cast, reason = self:CanCastSpell(spell_id)
    if not can_cast then
        if self.inst.components.talker then
            self.inst.components.talker:Say(reason or "Cannot cast spell", 2)
        end
        return false
    end
    
    local spell_data = self.basic_spells[spell_id]
    if not spell_data then return false end
    
    -- Consume spiritual energy
    if not self.inst.components.cultivation:ConsumeSpiritualEnergy(spell_data.cost) then
        return false
    end
    
    -- Set cooldown
    self.spell_cooldowns[spell_id] = GetTime() + spell_data.cooldown
    
    -- Cast the spell
    self:ExecuteSpell(spell_id, target or self.inst)
    
    -- Show cast message
    if self.inst.components.talker then
        self.inst.components.talker:Say("Cast " .. spell_data.name .. "!", 2)
    end
    
    print(string.format("[SpellCaster] %s cast %s", self.inst.name or "Player", spell_data.name))
    return true
end

function SpellCaster:ExecuteSpell(spell_id, target)
    if spell_id == "heal" then
        self:CastHeal(target)
    elseif spell_id == "energy_burst" then
        self:CastEnergyBurst(target)
    elseif spell_id == "meditation_boost" then
        self:CastMeditationBoost(target)
    elseif spell_id == "spiritual_shield" then
        self:CastSpiritualShield(target)
    end
end

function SpellCaster:CastHeal(target)
    if not target or not target.components.health then return end
    
    local heal_amount = 30 * (TUNING.CULTIVATION_MOD.SPELL_POWER or 1.0)
    target.components.health:DoDelta(heal_amount)
    
    -- Visual effect
    if target.AnimState then
        target.AnimState:SetMultColour(0.5, 1.0, 0.5, 1.0) -- Green healing glow
        target:DoTaskInTime(1, function()
            target.AnimState:SetMultColour(1, 1, 1, 1) -- Back to normal
        end)
    end
    
    print(string.format("[Spell] Healed %s for %d health", target.name or "target", heal_amount))
end

function SpellCaster:CastEnergyBurst(target)
    -- Damage nearby enemies
    local pos = self.inst:GetPosition()
    local ents = TheSim:FindEntities(pos.x, pos.y, pos.z, 5, {"_combat"}, {"player", "companion"})
    
    local damage = 25 * (TUNING.CULTIVATION_MOD.SPELL_POWER or 1.0)
    
    for _, ent in ipairs(ents) do
        if ent.components.combat then
            ent.components.combat:GetAttacked(self.inst, damage)
            
            -- Visual effect
            if ent.AnimState then
                ent.AnimState:SetMultColour(1.0, 0.5, 0.5, 1.0) -- Red damage glow
                ent:DoTaskInTime(0.5, function()
                    if ent.AnimState then
                        ent.AnimState:SetMultColour(1, 1, 1, 1)
                    end
                end)
            end
        end
    end
    
    print(string.format("[Spell] Energy burst hit %d enemies for %d damage each", #ents, damage))
end

function SpellCaster:CastMeditationBoost(target)
    if not target or not target.components.cultivation then return end
    
    -- Boost meditation for 30 seconds
    local old_speed = TUNING.CULTIVATION_MOD.CULTIVATION_SPEED
    TUNING.CULTIVATION_MOD.CULTIVATION_SPEED = old_speed * 2
    
    target:DoTaskInTime(30, function()
        TUNING.CULTIVATION_MOD.CULTIVATION_SPEED = old_speed
        if target.components.talker then
            target.components.talker:Say("Meditation boost ended.", 2)
        end
    end)
    
    print("[Spell] Meditation boost applied for 30 seconds")
end

function SpellCaster:CastSpiritualShield(target)
    if not target then return end
    
    -- Add temporary damage reduction
    local old_damage_mult = target.components.combat and target.components.combat.damagemultiplier or 1
    if target.components.combat then
        target.components.combat.damagemultiplier = old_damage_mult * 0.5 -- 50% damage reduction
    end
    
    -- Visual effect
    if target.AnimState then
        target.AnimState:SetMultColour(0.5, 0.5, 1.0, 1.0) -- Blue shield glow
    end
    
    -- Remove shield after 20 seconds
    target:DoTaskInTime(20, function()
        if target.components.combat then
            target.components.combat.damagemultiplier = old_damage_mult
        end
        if target.AnimState then
            target.AnimState:SetMultColour(1, 1, 1, 1)
        end
        if target.components.talker then
            target.components.talker:Say("Spiritual shield faded.", 2)
        end
    end)
    
    print("[Spell] Spiritual shield applied for 20 seconds")
end

function SpellCaster:GetKnownSpells()
    local spells = {}
    for spell_id, _ in pairs(self.known_spells) do
        if self.basic_spells[spell_id] then
            spells[spell_id] = self.basic_spells[spell_id]
        end
    end
    return spells
end

function SpellCaster:GetSpellCooldown(spell_id)
    local current_time = GetTime()
    if self.spell_cooldowns[spell_id] and current_time < self.spell_cooldowns[spell_id] then
        return self.spell_cooldowns[spell_id] - current_time
    end
    return 0
end

function SpellCaster:OnSave()
    return {
        known_spells = self.known_spells,
        spell_cooldowns = self.spell_cooldowns
    }
end

function SpellCaster:OnLoad(data)
    if data then
        self.known_spells = data.known_spells or {}
        self.spell_cooldowns = data.spell_cooldowns or {}
    end
end

function SpellCaster:GetDebugString()
    local known_count = 0
    for _ in pairs(self.known_spells) do
        known_count = known_count + 1
    end
    
    return string.format("Known spells: %d, Available: %s", 
        known_count, 
        table.concat(self:GetAvailableSpells(), ", "))
end

function SpellCaster:GetAvailableSpells()
    local available = {}
    for spell_id, _ in pairs(self.known_spells) do
        local can_cast, _ = self:CanCastSpell(spell_id)
        if can_cast then
            table.insert(available, spell_id)
        end
    end
    return available
end

return SpellCaster
