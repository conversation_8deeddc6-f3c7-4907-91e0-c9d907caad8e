-- NPC Dialogue Component
-- Handles complex dialogue trees and conversation systems

local NPCDialogue = Class(function(self, inst)
    self.inst = inst
    
    -- Dialogue trees
    self.dialogue_trees = {}
    
    -- Current conversation state
    self.current_conversation = nil
    self.conversation_partner = nil
    
    -- Dialogue history
    self.dialogue_history = {}
    
    -- Conversation cooldowns
    self.conversation_cooldowns = {}
    
end)

function NPCDialogue:AddDialogue(dialogue_id, dialogue_data)
    self.dialogue_trees[dialogue_id] = dialogue_data
end

function NPCDialogue:StartConversation(player, initial_dialogue_id)
    if not player or not self.dialogue_trees[initial_dialogue_id] then
        return false
    end
    
    -- Check cooldown
    local player_id = player.userid or tostring(player)
    local last_conversation = self.conversation_cooldowns[player_id] or 0
    local cooldown_time = 30 -- 30 seconds between conversations
    
    if GetTime() - last_conversation < cooldown_time then
        if self.inst.components.talker then
            self.inst.components.talker:Say("我们刚才不是说过了吗？", 2)
        end
        return false
    end
    
    -- Check if NPC can interact with this player
    if self.inst.components.npc_cultivator and not self.inst.components.npc_cultivator:CanInteractWithPlayer(player) then
        if self.inst.components.talker then
            self.inst.components.talker:Say("我不想和你说话。", 2)
        end
        return false
    end
    
    self.current_conversation = initial_dialogue_id
    self.conversation_partner = player
    self.conversation_cooldowns[player_id] = GetTime()
    
    -- Show dialogue to player
    self:ShowDialogue(player, initial_dialogue_id)
    
    return true
end

function NPCDialogue:ShowDialogue(player, dialogue_id)
    local dialogue = self.dialogue_trees[dialogue_id]
    if not dialogue or not player then
        return
    end
    
    -- Get dialogue text (can be function for dynamic content)
    local text = dialogue.text
    if type(text) == "function" then
        text = text(self.inst, player)
    end
    
    -- Apply memory-based modifications
    if self.inst.components.npc_memory then
        local modifiers = self.inst.components.npc_memory:GetDialogueModifier(player)
        text = self:ApplyDialogueModifiers(text, modifiers, player)
    end
    
    -- Show text
    if self.inst.components.talker then
        self.inst.components.talker:Say(text, 4)
    end
    
    -- Get available options
    local options = self:GetDialogueOptions(player, dialogue)
    
    -- Send dialogue data to player (this would integrate with UI system)
    player:PushEvent("npc_dialogue", {
        npc = self.inst,
        text = text,
        options = options,
        dialogue_id = dialogue_id
    })
    
    -- Record this interaction
    if self.inst.components.npc_memory then
        self.inst.components.npc_memory:AddMemory(player, "dialogue", {
            dialogue_id = dialogue_id,
            text = text
        })
    end
end

function NPCDialogue:GetDialogueOptions(player, dialogue)
    local options = {}
    
    if dialogue.options then
        for _, option in ipairs(dialogue.options) do
            -- Check if option is available
            if self:IsOptionAvailable(player, option) then
                local option_data = {
                    text = option.text,
                    action = option.action,
                    response = option.response,
                    next_dialogue = option.next_dialogue,
                    requirements = option.requirements,
                }
                
                table.insert(options, option_data)
            end
        end
    end
    
    -- Add default options based on NPC type
    if self.inst.components.trader then
        table.insert(options, {
            text = "我想交易",
            action = "trade",
            response = "看看你有什么好东西。"
        })
    end
    
    -- Always add goodbye option
    table.insert(options, {
        text = "再见",
        action = "end",
        response = "愿你修行顺利。"
    })
    
    return options
end

function NPCDialogue:IsOptionAvailable(player, option)
    if not option.requirements then
        return true
    end
    
    local req = option.requirements
    
    -- Check cultivation level requirement
    if req.min_cultivation_level then
        if not player.components.cultivation or player.components.cultivation.level < req.min_cultivation_level then
            return false
        end
    end
    
    -- Check relationship requirement
    if req.min_relationship then
        if not self.inst.components.npc_memory then
            return false
        end
        local relationship = self.inst.components.npc_memory:GetRelationship(player)
        if relationship < req.min_relationship then
            return false
        end
    end
    
    -- Check item requirement
    if req.has_item then
        if not player.components.inventory or not player.components.inventory:Has(req.has_item, 1) then
            return false
        end
    end
    
    -- Check quest requirement
    if req.quest_completed then
        -- This would check quest system when implemented
        return false
    end
    
    -- Check karma requirement
    if req.karma_type then
        if not player.components.cultivation then
            return false
        end
        
        local righteous = player.components.cultivation.righteous_karma or 0
        local evil = player.components.cultivation.evil_karma or 0
        
        if req.karma_type == "righteous" and righteous <= evil then
            return false
        elseif req.karma_type == "evil" and evil <= righteous then
            return false
        end
    end
    
    return true
end

function NPCDialogue:ApplyDialogueModifiers(text, modifiers, player)
    local modified_text = text
    
    -- Modify based on relationship level
    local relationship_prefixes = {
        close_friend = "老朋友，",
        friend = "朋友，",
        acquaintance = "",
        neutral = "",
        dislike = "",
        enemy = "你这个..."
    }
    
    local prefix = relationship_prefixes[modifiers.relationship_level] or ""
    if prefix ~= "" then
        modified_text = prefix .. modified_text
    end
    
    -- Add references to recent interactions
    if #modifiers.recent_interactions > 0 then
        local recent = modifiers.recent_interactions[#modifiers.recent_interactions]
        
        if recent.type == "gift_received" then
            modified_text = modified_text .. " 谢谢你之前的礼物。"
        elseif recent.type == "trade_completed" then
            modified_text = modified_text .. " 上次的交易很愉快。"
        elseif recent.type == "helped_by" then
            modified_text = modified_text .. " 我还记得你帮助过我。"
        end
    end
    
    return modified_text
end

function NPCDialogue:HandlePlayerResponse(player, option_index, dialogue_id)
    local dialogue = self.dialogue_trees[dialogue_id]
    if not dialogue or not dialogue.options or not dialogue.options[option_index] then
        return
    end
    
    local option = dialogue.options[option_index]
    
    -- Show NPC response
    if option.response then
        local response = option.response
        if type(response) == "function" then
            response = response(self.inst, player)
        end
        
        if self.inst.components.talker then
            self.inst.components.talker:Say(response, 3)
        end
    end
    
    -- Handle action
    if option.action then
        self:HandleDialogueAction(player, option.action, option)
    end
    
    -- Continue to next dialogue or end conversation
    if option.next_dialogue then
        self:ShowDialogue(player, option.next_dialogue)
    else
        self:EndConversation(player)
    end
end

function NPCDialogue:HandleDialogueAction(player, action, option)
    if action == "trade" then
        -- Open trade interface
        if self.inst.components.trader then
            self.inst.components.trader:Enable()
        end
        
    elseif action == "teach_spell" then
        -- Teach a spell to the player
        if option.spell_name and player.components.cultivation then
            player.components.cultivation:UnlockSpell(option.spell_name)
            if player.components.talker then
                player.components.talker:Say("学会了新的法术！", 2)
            end
        end
        
    elseif action == "give_item" then
        -- Give item to player
        if option.item_name and player.components.inventory then
            local item = SpawnPrefab(option.item_name)
            if item then
                player.components.inventory:GiveItem(item)
            end
        end
        
    elseif action == "start_quest" then
        -- Start a quest (would integrate with quest system)
        if self.inst.components.talker then
            self.inst.components.talker:Say("任务系统尚未实现。", 2)
        end
        
    elseif action == "join_sect" then
        -- Allow player to join sect
        if option.sect_name and player.components.sect_member then
            player.components.sect_member:JoinSect(option.sect_name)
            if player.components.talker then
                player.components.talker:Say("欢迎加入" .. option.sect_name .. "！", 3)
            end
        end
        
    elseif action == "meditation_lesson" then
        -- Give meditation bonus
        if player.components.cultivation then
            player.components.cultivation:AddExperience(20)
            if player.components.talker then
                player.components.talker:Say("获得了修炼感悟！", 2)
            end
        end
        
    elseif action == "end" then
        -- End conversation
        self:EndConversation(player)
    end
    
    -- Update relationship based on action
    if self.inst.components.npc_memory then
        local relationship_changes = {
            trade = 1,
            teach_spell = 5,
            give_item = 3,
            start_quest = 2,
            join_sect = 10,
            meditation_lesson = 3,
        }
        
        local change = relationship_changes[action] or 0
        if change > 0 then
            self.inst.components.npc_memory:ModifyRelationship(player, change)
        end
    end
end

function NPCDialogue:EndConversation(player)
    self.current_conversation = nil
    self.conversation_partner = nil
    
    -- Record conversation end
    if self.inst.components.npc_memory then
        self.inst.components.npc_memory:AddMemory(player, "conversation_ended", {
            timestamp = GetTime()
        })
    end
    
    -- Notify player that conversation ended
    player:PushEvent("npc_dialogue_end", {npc = self.inst})
end

function NPCDialogue:GetRandomAmbientDialogue()
    -- Return random ambient dialogue based on NPC type and current state
    local ambient_dialogues = {
        peach_fairy = {
            "桃花飞舞，春意盎然...",
            "感受这里的治愈之力吧。",
            "修仙路漫漫，需要一颗平静的心。",
            "这里的灵气特别纯净。",
        },
        flower_spirit = {
            "花儿在歌唱...",
            "自然的力量无处不在。",
            "小心不要踩到花朵。",
        },
        default = {
            "...",
            "修炼不易啊。",
            "这里的灵气不错。",
        }
    }
    
    local npc_type = self.inst.prefab or "default"
    local dialogues = ambient_dialogues[npc_type] or ambient_dialogues.default
    
    return dialogues[math.random(#dialogues)]
end

function NPCDialogue:OnSave()
    return {
        dialogue_trees = self.dialogue_trees,
        dialogue_history = self.dialogue_history,
        conversation_cooldowns = self.conversation_cooldowns,
    }
end

function NPCDialogue:OnLoad(data)
    if data then
        self.dialogue_trees = data.dialogue_trees or {}
        self.dialogue_history = data.dialogue_history or {}
        self.conversation_cooldowns = data.conversation_cooldowns or {}
    end
end

return NPCDialogue
