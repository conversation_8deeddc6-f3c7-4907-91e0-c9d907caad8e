-- NPC System Initialization

-- Load NPC components (already loaded in components init)
-- modimport("scripts/cultivation_core/02_components/npc_cultivator.lua")
-- modimport("scripts/cultivation_core/02_components/npc_memory.lua")

-- Load NPC prefabs
modimport("scripts/cultivation_core/05_npc_system/01_npc_prefabs.lua")

-- Load NPC behaviors
modimport("scripts/cultivation_core/05_npc_system/02_npc_behaviors.lua")

print("[Tu T<PERSON>ên Bí <PERSON>nh] NPC system loaded")
