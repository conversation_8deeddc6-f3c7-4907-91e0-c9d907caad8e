-- NPC Prefabs for Cultivation Realms

-- Load NPC components first
modimport("scripts/cultivation_core/02_components/npc_cultivator.lua")
modimport("scripts/cultivation_core/02_components/npc_memory.lua")

-- Peach Fairy (桃花仙子) - Friendly NPC in Peach Blossom Realm
local function peach_fairy_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddDynamicShadow()
    inst.entity:AddMiniMapEntity()
    inst.entity:AddNetwork()

    -- Physics
    MakeCharacterPhysics(inst, 30, 0.5)

    -- Animation
    inst.AnimState:SetBank("peach_fairy")
    inst.AnimState:SetBuild("peach_fairy")
    inst.AnimState:PlayAnimation("idle_loop", true)
    inst.AnimState:Hide("ARM_carry")
    inst.AnimState:Hide("hat")
    inst.AnimState:Hide("hat_hair")

    -- Shadow
    inst.DynamicShadow:SetSize(1.3, 0.6)

    -- Minimap
    inst.MiniMapEntity:SetIcon("peach_fairy.png")
    inst.MiniMapEntity:SetPriority(5)

    -- Tags
    inst:AddTag("character")
    inst:AddTag("npc")
    inst:AddTag("cultivation_npc")
    inst:AddTag("peach_fairy")
    inst:AddTag("realm_npc")
    inst:AddTag("trader")

    -- Network
    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Health
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(200)
    inst.components.health.nofadeout = true

    -- Combat (defensive only)
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(0)
    inst.components.combat:SetRetargetFunction(1, function() return nil end)
    inst.components.combat.hiteffectsymbol = "torso"

    -- Locomotor
    inst:AddComponent("locomotor")
    inst.components.locomotor.walkspeed = 2
    inst.components.locomotor.runspeed = 4

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("一位美丽的桃花仙子，散发着春天的气息。")

    -- NPC Cultivator component
    inst:AddComponent("npc_cultivator")
    inst.components.npc_cultivator:SetAlignment("friendly")
    inst.components.npc_cultivator:SetLevel(5) -- Mid-level cultivator
    inst.components.npc_cultivator:SetSect("peach_blossom_sect")

    -- Memory component
    inst:AddComponent("npc_memory")

    -- Trader component
    inst:AddComponent("trader")
    inst.components.trader:SetAcceptTest(function(inst, item, giver)
        -- Accepts spiritual items and flowers
        return item:HasTag("spiritual_herb") or item:HasTag("spiritual_stone") or item:HasTag("flower")
    end)

    inst.components.trader:SetOnAccept(function(inst, giver, item)
        -- Give rewards based on item value
        local rewards = {
            "peach_blossom_flower",
            "spring_water",
            "healing_dew",
            "cultivation_pill",
        }

        local reward_count = 1
        if item:HasTag("spiritual_stone") then
            reward_count = item.components.stackable and math.min(3, math.floor(item.components.stackable:StackSize() / 5)) or 1
        end

        for i = 1, reward_count do
            local reward = SpawnPrefab(rewards[math.random(#rewards)])
            if reward then
                giver.components.inventory:GiveItem(reward)
            end
        end

        -- Improve relationship
        if inst.components.npc_memory then
            inst.components.npc_memory:ModifyRelationship(giver, 10)
        end

        if inst.components.talker then
            local messages = {
                "谢谢你的礼物，这些桃花的祝福给你。",
                "你的善意让桃花更加绚烂。",
                "愿春天的力量与你同在。",
            }
            inst.components.talker:Say(messages[math.random(#messages)], 3)
        end
    end)

    -- Talker component
    inst:AddComponent("talker")
    inst.components.talker.fontsize = 35
    inst.components.talker.font = CHATFONT
    inst.components.talker.colour = Vector3(1, 0.7, 0.8) -- Pink color

    -- Dialogue system
    inst:AddComponent("npc_dialogue")
    inst.components.npc_dialogue:AddDialogue("greeting", {
        text = "欢迎来到桃花秘境，年轻的修士。",
        options = {
            {text = "这里真美丽", response = "是的，这里是春天永驻的仙境。桃花的力量能治愈一切创伤。"},
            {text = "你是谁？", response = "我是桃花仙子，守护着这片净土。你看起来像是刚踏上修仙之路的新人。"},
            {text = "能教我一些东西吗？", response = "当然，修仙之路需要耐心和善心。多多冥想，感受自然的力量。"},
        }
    })

    -- Periodic behaviors
    inst:DoPeriodicTask(10, function()
        -- Random movement
        if math.random() < 0.3 then
            local angle = math.random() * 2 * PI
            local dist = 2 + math.random() * 3
            local x, y, z = inst.Transform:GetWorldPosition()
            local new_x = x + math.cos(angle) * dist
            local new_z = z + math.sin(angle) * dist

            if inst.components.locomotor then
                inst.components.locomotor:GoToPoint(Vector3(new_x, y, new_z))
            end
        end

        -- Random dialogue
        if math.random() < 0.1 then
            local ambient_messages = {
                "桃花飞舞，春意盎然...",
                "感受这里的治愈之力吧。",
                "修仙路漫漫，需要一颗平静的心。",
            }
            if inst.components.talker then
                inst.components.talker:Say(ambient_messages[math.random(#ambient_messages)], 2)
            end
        end
    end)

    -- Healing aura for nearby players
    inst:DoPeriodicTask(5, function()
        local x, y, z = inst.Transform:GetWorldPosition()
        local players = TheSim:FindEntities(x, y, z, 8, {"player"})

        for _, player in ipairs(players) do
            if player.components.health and not player.components.health:IsDead() then
                player.components.health:DoDelta(3) -- Small healing

                if player.components.cultivation then
                    player.components.cultivation:AddSpiritualEnergy(2)
                end
            end
        end
    end)

    return inst
end

-- Flower Spirit (花灵) - Neutral creature in Peach Blossom Realm
local function flower_spirit_fn()
    local inst = CreateEntity()

    inst.entity:AddTransform()
    inst.entity:AddAnimState()
    inst.entity:AddSoundEmitter()
    inst.entity:AddDynamicShadow()
    inst.entity:AddMiniMapEntity()
    inst.entity:AddNetwork()

    -- Physics
    MakeCharacterPhysics(inst, 10, 0.3)

    -- Animation
    inst.AnimState:SetBank("flower_spirit")
    inst.AnimState:SetBuild("flower_spirit")
    inst.AnimState:PlayAnimation("idle_loop", true)

    -- Shadow
    inst.DynamicShadow:SetSize(0.8, 0.4)

    -- Minimap
    inst.MiniMapEntity:SetIcon("flower_spirit.png")

    -- Tags
    inst:AddTag("character")
    inst:AddTag("npc")
    inst:AddTag("flower_spirit")
    inst:AddTag("realm_creature")

    inst.entity:SetPristine()

    if not TheWorld.ismastersim then
        return inst
    end

    -- Health
    inst:AddComponent("health")
    inst.components.health:SetMaxHealth(80)

    -- Combat (will flee if attacked)
    inst:AddComponent("combat")
    inst.components.combat:SetDefaultDamage(15)
    inst.components.combat:SetRange(2)

    -- Locomotor
    inst:AddComponent("locomotor")
    inst.components.locomotor.walkspeed = 3
    inst.components.locomotor.runspeed = 6

    -- Inspectable
    inst:AddComponent("inspectable")
    inst.components.inspectable:SetDescription("一个由花朵凝聚而成的精灵，充满了自然的魔力。")

    -- Loot dropper
    inst:AddComponent("lootdropper")
    inst.components.lootdropper:SetLoot({"peach_blossom_flower", "butterfly_essence"})
    inst.components.lootdropper:SetChanceLootTable("flower_spirit_loot")

    -- Peaceful behavior - will only attack if provoked
    inst:AddComponent("peaceful")
    inst.components.peaceful.onhitfn = function()
        -- Become aggressive temporarily
        inst:RemoveComponent("peaceful")
        inst:DoTaskInTime(30, function() -- Calm down after 30 seconds
            if inst:IsValid() then
                inst:AddComponent("peaceful")
            end
        end)
    end

    -- Random flower spawning
    inst:DoPeriodicTask(20, function()
        if math.random() < 0.3 then
            local x, y, z = inst.Transform:GetWorldPosition()
            local flower = SpawnPrefab("peach_blossom_flower")
            if flower then
                local angle = math.random() * 2 * PI
                local dist = 1 + math.random() * 2
                flower.Transform:SetPosition(x + math.cos(angle) * dist, y, z + math.sin(angle) * dist)
            end
        end
    end)

    return inst
end

-- Register prefabs
local prefabs = {
    Prefab("peach_fairy", peach_fairy_fn),
    Prefab("flower_spirit", flower_spirit_fn),
}

print("[Tu Tiên Bí Cảnh] NPC prefabs loaded:", #prefabs)
