-- Fire Realm Generation
-- 地火秘境 - Volcanic realm with fire cultivation bonuses

local FIRE_REALM = {
    name = "fire_realm",
    display_name = "地火秘境",
    description = "烈火焚天，岩浆翻滚的炼狱之地",
    
    -- Environment settings
    environment = {
        season = "summer",
        temperature_modifier = 1.5, -- Very hot
        moisture_modifier = 0.3, -- Very dry
        light_modifier = 1.2, -- Bright from fires
        fire_immunity_required = false,
        heat_damage = true,
    },
    
    -- Spawn settings
    spawn_requirements = {
        min_day = 25,
        min_player_level = 3,
        spawn_weight = 2,
    },
    
    -- Resources unique to this realm
    unique_resources = {
        "fire_crystal",
        "lava_stone",
        "phoenix_feather",
        "flame_flower",
        "molten_core",
    },
    
    -- NPCs that can spawn here
    npc_types = {
        {name = "fire_spirit", chance = 0.7, alignment = "neutral"},
        {name = "lava_golem", chance = 0.5, alignment = "hostile"},
        {name = "phoenix_guardian", chance = 0.2, alignment = "friendly"},
    },
    
    -- Creatures in this realm
    creatures = {
        {name = "fire_salamander", chance = 0.8, count = {2, 4}},
        {name = "lava_worm", chance = 0.6, count = {1, 3}},
        {name = "flame_wisp", chance = 0.9, count = {3, 6}},
    },
    
    -- Special mechanics
    special_mechanics = {
        heat_zones = true,
        fire_enhancement = true,
        lava_flows = true,
        forge_stations = true,
        fire_immunity_training = true,
    },
}

function GenerateFireRealm(portal)
    if not portal or not portal:IsValid() then
        return false
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Apply fire environment
    ApplyFireEnvironment(portal, FIRE_REALM.environment)
    
    -- Spawn fire resources
    SpawnFireResources(portal_pos, FIRE_REALM.unique_resources)
    
    -- Spawn fire NPCs and creatures
    SpawnFireNPCs(portal_pos, FIRE_REALM.npc_types)
    SpawnFireCreatures(portal_pos, FIRE_REALM.creatures)
    
    -- Apply fire mechanics
    ApplyFireMechanics(portal, FIRE_REALM.special_mechanics)
    
    return true
end

function ApplyFireEnvironment(portal, env_settings)
    local radius = 20
    local portal_pos = portal:GetPosition()
    
    -- Create fire aura
    local aura = SpawnPrefab("fire_aura")
    if aura then
        aura.Transform:SetPosition(portal_pos:Get())
        aura.components.aura:SetRadius(radius)
        aura.components.aura:SetAuraType("fire")
        portal.realm_aura = aura
    end
    
    -- Spawn volcanic decorations
    for i = 1, 12 do
        local angle = (i / 12) * 2 * PI
        local dist = 8 + math.random() * 10
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local decorations = {"lava_pool", "fire_geyser", "volcanic_rock", "flame_pillar"}
        local decoration = SpawnPrefab(decorations[math.random(#decorations)])
        if decoration then
            decoration.Transform:SetPosition(x, 0, z)
        end
    end
    
    -- Create fire effects
    for i = 1, 10 do
        local angle = math.random() * 2 * PI
        local dist = 5 + math.random() * 12
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local fire_fx = SpawnPrefab("fire_realm_fx")
        if fire_fx then
            fire_fx.Transform:SetPosition(x, 0, z)
        end
    end
end

function SpawnFireResources(center_pos, resource_list)
    for _, resource_name in ipairs(resource_list) do
        local count = math.random(2, 4)
        
        for i = 1, count do
            local angle = math.random() * 2 * PI
            local dist = 8 + math.random() * 10
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local resource = SpawnPrefab(resource_name)
            if resource then
                resource.Transform:SetPosition(x, 0, z)
                resource:AddTag("realm_resource")
                resource.realm_type = "fire_realm"
            end
        end
    end
end

function SpawnFireNPCs(center_pos, npc_list)
    for _, npc_data in ipairs(npc_list) do
        if math.random() < npc_data.chance then
            local angle = math.random() * 2 * PI
            local dist = 10 + math.random() * 6
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local npc = SpawnPrefab(npc_data.name)
            if npc then
                npc.Transform:SetPosition(x, 0, z)
                
                if npc.components.npc_cultivator then
                    npc.components.npc_cultivator:SetAlignment(npc_data.alignment)
                end
                
                npc:AddTag("realm_npc")
                npc.realm_type = "fire_realm"
            end
        end
    end
end

function SpawnFireCreatures(center_pos, creature_list)
    for _, creature_data in ipairs(creature_list) do
        if math.random() < creature_data.chance then
            local count = math.random(creature_data.count[1], creature_data.count[2])
            
            for i = 1, count do
                local angle = math.random() * 2 * PI
                local dist = 6 + math.random() * 12
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist
                
                local creature = SpawnPrefab(creature_data.name)
                if creature then
                    creature.Transform:SetPosition(x, 0, z)
                    creature:AddTag("realm_creature")
                    creature.realm_type = "fire_realm"
                end
            end
        end
    end
end

function ApplyFireMechanics(portal, mechanics)
    local portal_pos = portal:GetPosition()
    
    if mechanics.heat_zones then
        -- Create heat damage zones
        portal.heat_task = portal:DoPeriodicTask(2, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                if player.components.temperature then
                    player.components.temperature:DoDelta(5) -- Increase temperature
                end
                
                -- Heat damage if too hot
                if player.components.temperature and player.components.temperature:GetCurrent() > 70 then
                    if player.components.health and math.random() < 0.3 then
                        player.components.health:DoDelta(-3)
                        if player.components.talker then
                            player.components.talker:Say("太热了！", 1)
                        end
                    end
                end
            end
        end)
    end
    
    if mechanics.fire_enhancement then
        -- Enhance fire spells
        portal.fire_enhancement_task = portal:DoPeriodicTask(1, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                if player.components.spell_caster then
                    -- Fire spells cost less and do more damage
                    player.fire_realm_bonus = true
                end
                
                if player.components.cultivation then
                    -- Fire cultivation bonus
                    if player.components.cultivation.is_meditating and math.random() < 0.2 then
                        player.components.cultivation:AddExperience(2)
                    end
                end
            end
        end)
    end
    
    if mechanics.lava_flows then
        -- Periodic lava eruptions
        portal.lava_flow_task = portal:DoPeriodicTask(45, function() -- Every 45 seconds
            if math.random() < 0.4 then
                local angle = math.random() * 2 * PI
                local dist = 8 + math.random() * 8
                local x = portal_pos.x + math.cos(angle) * dist
                local z = portal_pos.z + math.sin(angle) * dist
                
                -- Create lava eruption
                local eruption = SpawnPrefab("lava_eruption_fx")
                if eruption then
                    eruption.Transform:SetPosition(x, 0, z)
                end
                
                -- Damage nearby entities
                local nearby = TheSim:FindEntities(x, 0, z, 3, nil, {"fire_immune"})
                for _, entity in ipairs(nearby) do
                    if entity.components.health then
                        entity.components.health:DoDelta(-20)
                    end
                end
            end
        end)
    end
    
    if mechanics.forge_stations then
        -- Spawn fire forges for weapon crafting
        for i = 1, 2 do
            local angle = math.random() * 2 * PI
            local dist = 12 + math.random() * 4
            local x = portal_pos.x + math.cos(angle) * dist
            local z = portal_pos.z + math.sin(angle) * dist
            
            local forge = SpawnPrefab("fire_forge")
            if forge then
                forge.Transform:SetPosition(x, 0, z)
                forge:AddTag("realm_structure")
                forge.realm_type = "fire_realm"
            end
        end
    end
    
    if mechanics.fire_immunity_training then
        -- Players can gradually build fire resistance
        portal.immunity_training_task = portal:DoPeriodicTask(10, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                if not player.fire_resistance_training then
                    player.fire_resistance_training = 0
                end
                
                player.fire_resistance_training = player.fire_resistance_training + 1
                
                -- After enough training, gain fire resistance
                if player.fire_resistance_training >= 30 then -- 5 minutes
                    player:AddTag("fire_resistant")
                    if player.components.talker then
                        player.components.talker:Say("获得了火焰抗性！", 3)
                    end
                    player.fire_resistance_training = 0
                    
                    -- Temporary fire resistance
                    player:DoTaskInTime(300, function() -- 5 minutes duration
                        if player:IsValid() then
                            player:RemoveTag("fire_resistant")
                        end
                    end)
                end
            end
        end)
    end
end

function CleanupFireRealm(portal)
    if not portal or not portal:IsValid() then
        return
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Remove fire bonuses from players
    local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
    for _, player in ipairs(players) do
        player.fire_realm_bonus = nil
        player.fire_resistance_training = nil
        player:RemoveTag("fire_resistant")
    end
    
    -- Remove realm-specific entities
    local realm_entities = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, 
        {"realm_resource", "realm_npc", "realm_creature", "realm_structure"})
    
    for _, entity in ipairs(realm_entities) do
        if entity.realm_type == "fire_realm" then
            entity:Remove()
        end
    end
    
    -- Clean up aura
    if portal.realm_aura and portal.realm_aura:IsValid() then
        portal.realm_aura:Remove()
    end
    
    -- Cancel tasks
    local tasks = {"heat_task", "fire_enhancement_task", "lava_flow_task", "immunity_training_task"}
    for _, task_name in ipairs(tasks) do
        if portal[task_name] then
            portal[task_name]:Cancel()
            portal[task_name] = nil
        end
    end
end

-- Register realm type
GLOBAL.CULTIVATION_REALMS = GLOBAL.CULTIVATION_REALMS or {}
GLOBAL.CULTIVATION_REALMS["fire_realm"] = {
    data = FIRE_REALM,
    generate = GenerateFireRealm,
    cleanup = CleanupFireRealm,
}

print("[Tu Tiên Bí Cảnh] Fire Realm system loaded")
