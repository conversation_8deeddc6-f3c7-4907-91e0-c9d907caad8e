-- Realm Generation System Initialization

-- Load realm generation components
modimport("scripts/cultivation_core/03_realm_generation/01_realm_generator.lua")

-- Load specific realm types
modimport("scripts/cultivation_core/03_realm_generation/02_peach_blossom_realm.lua")
modimport("scripts/cultivation_core/03_realm_generation/03_heavenly_mountain_realm.lua")
modimport("scripts/cultivation_core/03_realm_generation/04_ancient_battlefield_realm.lua")
modimport("scripts/cultivation_core/03_realm_generation/05_fire_realm.lua")
modimport("scripts/cultivation_core/03_realm_generation/06_moon_realm.lua")
modimport("scripts/cultivation_core/03_realm_generation/07_thunder_realm.lua")
modimport("scripts/cultivation_core/03_realm_generation/08_medicine_valley_realm.lua")

print("[Tu Tiên Bí Cảnh] Realm generation system loaded")
