-- Thunder Realm Generation
-- 雷电秘境 - Stormy realm with lightning cultivation and electrical hazards

local THUNDER_REALM = {
    name = "thunder_realm",
    display_name = "雷电秘境",
    description = "雷霆万钧，电闪雷鸣的天域",
    
    -- Environment settings
    environment = {
        season = "spring",
        temperature_modifier = 0.9,
        moisture_modifier = 1.3, -- Very humid, stormy
        light_modifier = 0.9, -- Dark storm clouds
        weather = "storm",
        electrical_hazards = true,
    },
    
    -- Spawn settings
    spawn_requirements = {
        min_day = 35,
        min_player_level = 4,
        spawn_weight = 1.5,
        preferred_weather = "rain", -- More likely during storms
    },
    
    -- Resources unique to this realm
    unique_resources = {
        "thunder_stone",
        "lightning_crystal",
        "storm_essence",
        "electric_flower",
        "sky_metal",
    },
    
    -- NPCs that can spawn here
    npc_types = {
        {name = "thunder_master", chance = 0.5, alignment = "neutral"},
        {name = "storm_spirit", chance = 0.7, alignment = "friendly"},
        {name = "lightning_elemental", chance = 0.4, alignment = "hostile"},
    },
    
    -- Creatures in this realm
    creatures = {
        {name = "electric_bird", chance = 0.8, count = {2, 4}},
        {name = "storm_drake", chance = 0.5, count = {1, 2}},
        {name = "thunder_beast", chance = 0.3, count = {1, 1}},
    },
    
    -- Special mechanics
    special_mechanics = {
        lightning_strikes = true,
        electrical_cultivation = true,
        storm_weather = true,
        lightning_rods = true,
        thunder_tribulation = true,
    },
}

function GenerateThunderRealm(portal)
    if not portal or not portal:IsValid() then
        return false
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Apply thunder environment
    ApplyThunderEnvironment(portal, THUNDER_REALM.environment)
    
    -- Spawn thunder resources
    SpawnThunderResources(portal_pos, THUNDER_REALM.unique_resources)
    
    -- Spawn thunder NPCs and creatures
    SpawnThunderNPCs(portal_pos, THUNDER_REALM.npc_types)
    SpawnThunderCreatures(portal_pos, THUNDER_REALM.creatures)
    
    -- Apply thunder mechanics
    ApplyThunderMechanics(portal, THUNDER_REALM.special_mechanics)
    
    return true
end

function ApplyThunderEnvironment(portal, env_settings)
    local radius = 22
    local portal_pos = portal:GetPosition()
    
    -- Create storm aura
    local aura = SpawnPrefab("storm_aura")
    if aura then
        aura.Transform:SetPosition(portal_pos:Get())
        aura.components.aura:SetRadius(radius)
        aura.components.aura:SetAuraType("thunder")
        portal.realm_aura = aura
    end
    
    -- Spawn storm decorations
    for i = 1, 8 do
        local angle = (i / 8) * 2 * PI
        local dist = 10 + math.random() * 8
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local decorations = {"lightning_rod", "storm_cloud", "electric_pillar", "thunder_altar"}
        local decoration = SpawnPrefab(decorations[math.random(#decorations)])
        if decoration then
            decoration.Transform:SetPosition(x, 0, z)
        end
    end
    
    -- Create storm effects
    for i = 1, 12 do
        local angle = math.random() * 2 * PI
        local dist = 5 + math.random() * 15
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local storm_fx = SpawnPrefab("storm_fx")
        if storm_fx then
            storm_fx.Transform:SetPosition(x, 3, z)
        end
    end
end

function SpawnThunderResources(center_pos, resource_list)
    for _, resource_name in ipairs(resource_list) do
        local count = math.random(1, 3) -- Rare but powerful
        
        for i = 1, count do
            local angle = math.random() * 2 * PI
            local dist = 8 + math.random() * 12
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local resource = SpawnPrefab(resource_name)
            if resource then
                resource.Transform:SetPosition(x, 0, z)
                resource:AddTag("realm_resource")
                resource.realm_type = "thunder_realm"
            end
        end
    end
end

function SpawnThunderNPCs(center_pos, npc_list)
    for _, npc_data in ipairs(npc_list) do
        if math.random() < npc_data.chance then
            local angle = math.random() * 2 * PI
            local dist = 12 + math.random() * 6
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local npc = SpawnPrefab(npc_data.name)
            if npc then
                npc.Transform:SetPosition(x, 0, z)
                
                if npc.components.npc_cultivator then
                    npc.components.npc_cultivator:SetAlignment(npc_data.alignment)
                    npc.components.npc_cultivator:SetLevel(math.random(4, 6)) -- High level
                end
                
                npc:AddTag("realm_npc")
                npc.realm_type = "thunder_realm"
            end
        end
    end
end

function SpawnThunderCreatures(center_pos, creature_list)
    for _, creature_data in ipairs(creature_list) do
        if math.random() < creature_data.chance then
            local count = math.random(creature_data.count[1], creature_data.count[2])
            
            for i = 1, count do
                local angle = math.random() * 2 * PI
                local dist = 8 + math.random() * 12
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist
                
                local creature = SpawnPrefab(creature_data.name)
                if creature then
                    creature.Transform:SetPosition(x, 0, z)
                    creature:AddTag("realm_creature")
                    creature.realm_type = "thunder_realm"
                end
            end
        end
    end
end

function ApplyThunderMechanics(portal, mechanics)
    local portal_pos = portal:GetPosition()
    
    if mechanics.lightning_strikes then
        -- Random lightning strikes
        portal.lightning_task = portal:DoPeriodicTask(8, function() -- Every 8 seconds
            if math.random() < 0.4 then
                local angle = math.random() * 2 * PI
                local dist = math.random() * 20
                local x = portal_pos.x + math.cos(angle) * dist
                local z = portal_pos.z + math.sin(angle) * dist
                
                -- Lightning strike effect
                TheWorld:PushEvent("ms_sendlightningstrike", Vector3(x, 0, z))
                
                -- Damage entities at strike location
                local nearby = TheSim:FindEntities(x, 0, z, 3, nil, {"lightning_immune"})
                for _, entity in ipairs(nearby) do
                    if entity.components.health then
                        entity.components.health:DoDelta(-25)
                        
                        -- Chance to gain electrical charge
                        if entity:HasTag("player") and math.random() < 0.3 then
                            entity:AddTag("electrically_charged")
                            entity:DoTaskInTime(30, function()
                                if entity:IsValid() then
                                    entity:RemoveTag("electrically_charged")
                                end
                            end)
                        end
                    end
                end
            end
        end)
    end
    
    if mechanics.electrical_cultivation then
        -- Lightning cultivation bonus
        portal.electrical_cultivation_task = portal:DoPeriodicTask(2, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 22, {"player"})
            for _, player in ipairs(players) do
                if player.components.cultivation then
                    -- Lightning cultivation bonus
                    if player.components.cultivation.is_meditating then
                        if math.random() < 0.25 then
                            player.components.cultivation:AddExperience(3)
                            player.components.cultivation:AddSpiritualEnergy(5)
                            
                            -- Visual lightning effect
                            local fx = SpawnPrefab("lightning_cultivation_fx")
                            if fx then
                                fx.Transform:SetPosition(player.Transform:GetWorldPosition())
                            end
                        end
                    end
                end
                
                if player.components.spell_caster then
                    player.thunder_realm_bonus = true -- Lightning spells enhanced
                end
            end
        end)
    end
    
    if mechanics.storm_weather then
        -- Constant storm effects
        portal.storm_task = portal:DoPeriodicTask(15, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 22, {"player"})
            for _, player in ipairs(players) do
                if player.components.moisture then
                    player.components.moisture:DoDelta(10) -- Get wet from rain
                end
                
                if player.components.temperature then
                    player.components.temperature:DoDelta(-3) -- Cooling rain
                end
            end
        end)
    end
    
    if mechanics.lightning_rods then
        -- Spawn lightning rods that can be used for cultivation
        for i = 1, 3 do
            local angle = (i / 3) * 2 * PI
            local dist = 15 + math.random() * 5
            local x = portal_pos.x + math.cos(angle) * dist
            local z = portal_pos.z + math.sin(angle) * dist
            
            local rod = SpawnPrefab("cultivation_lightning_rod")
            if rod then
                rod.Transform:SetPosition(x, 0, z)
                rod:AddTag("realm_structure")
                rod.realm_type = "thunder_realm"
            end
        end
    end
    
    if mechanics.thunder_tribulation then
        -- Special tribulation events for high-level players
        portal.tribulation_task = portal:DoPeriodicTask(180, function() -- Every 3 minutes
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 22, {"player"})
            for _, player in ipairs(players) do
                if player.components.cultivation and player.components.cultivation.level >= 6 then
                    if math.random() < 0.2 then -- 20% chance for high-level players
                        -- Thunder tribulation event
                        if player.components.talker then
                            player.components.talker:Say("雷劫降临！", 3)
                        end
                        
                        -- Series of lightning strikes around player
                        for i = 1, 5 do
                            player:DoTaskInTime(i * 2, function()
                                if player:IsValid() then
                                    local angle = math.random() * 2 * PI
                                    local dist = 2 + math.random() * 4
                                    local px, py, pz = player.Transform:GetWorldPosition()
                                    local x = px + math.cos(angle) * dist
                                    local z = pz + math.sin(angle) * dist
                                    
                                    TheWorld:PushEvent("ms_sendlightningstrike", Vector3(x, 0, z))
                                end
                            end)
                        end
                        
                        -- Reward if survived
                        player:DoTaskInTime(12, function()
                            if player:IsValid() and player.components.health and not player.components.health:IsDead() then
                                player.components.cultivation:AddExperience(50)
                                if player.components.talker then
                                    player.components.talker:Say("渡过雷劫，修为大增！", 3)
                                end
                            end
                        end)
                    end
                end
            end
        end)
    end
end

function CleanupThunderRealm(portal)
    if not portal or not portal:IsValid() then
        return
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Remove thunder bonuses from players
    local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
    for _, player in ipairs(players) do
        player.thunder_realm_bonus = nil
        player:RemoveTag("electrically_charged")
    end
    
    -- Remove realm-specific entities
    local realm_entities = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 30, 
        {"realm_resource", "realm_npc", "realm_creature", "realm_structure"})
    
    for _, entity in ipairs(realm_entities) do
        if entity.realm_type == "thunder_realm" then
            entity:Remove()
        end
    end
    
    -- Clean up aura
    if portal.realm_aura and portal.realm_aura:IsValid() then
        portal.realm_aura:Remove()
    end
    
    -- Cancel tasks
    local tasks = {"lightning_task", "electrical_cultivation_task", "storm_task", "tribulation_task"}
    for _, task_name in ipairs(tasks) do
        if portal[task_name] then
            portal[task_name]:Cancel()
            portal[task_name] = nil
        end
    end
end

-- Register realm type
GLOBAL.CULTIVATION_REALMS = GLOBAL.CULTIVATION_REALMS or {}
GLOBAL.CULTIVATION_REALMS["thunder_realm"] = {
    data = THUNDER_REALM,
    generate = GenerateThunderRealm,
    cleanup = CleanupThunderRealm,
}

print("[Tu Tiên Bí Cảnh] Thunder Realm system loaded")
