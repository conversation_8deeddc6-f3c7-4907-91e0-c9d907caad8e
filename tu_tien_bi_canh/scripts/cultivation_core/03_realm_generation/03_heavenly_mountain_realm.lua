-- Heavenly Mountain Realm Generation
-- 天山秘境 - High-altitude realm with cultivation bonuses

local HEAVENLY_MOUNTAIN_REALM = {
    name = "heavenly_mountain",
    display_name = "天山秘境",
    description = "高山云雾缭绕的仙境，灵气浓郁",
    
    -- Environment settings
    environment = {
        season = "winter",
        temperature_modifier = 0.6, -- Colder
        moisture_modifier = 0.8, -- Drier
        light_modifier = 1.3, -- Brighter (closer to heaven)
        cultivation_bonus = 1.5, -- 50% more cultivation exp
    },
    
    -- Spawn settings
    spawn_requirements = {
        min_day = 15,
        min_player_level = 2,
        spawn_weight = 2,
    },
    
    -- Resources unique to this realm
    unique_resources = {
        "spirit_stone_vein",
        "heaven_ginseng",
        "cloud_talisman_grass",
        "mountain_crystal",
        "celestial_dew",
    },
    
    -- NPCs that can spawn here
    npc_types = {
        {name = "mountain_hermit", chance = 0.7, alignment = "friendly"},
        {name = "stone_guardian", chance = 0.5, alignment = "neutral"},
        {name = "celestial_crane", chance = 0.3, alignment = "friendly"},
    },
    
    -- Creatures in this realm
    creatures = {
        {name = "mountain_goat", chance = 0.8, count = {2, 4}},
        {name = "spirit_eagle", chance = 0.6, count = {1, 3}},
        {name = "stone_elemental", chance = 0.4, count = {1, 2}},
    },
    
    -- Special mechanics
    special_mechanics = {
        altitude_effects = true,
        cultivation_shrine = true,
        spirit_veins = true,
        weather_control = true,
    },
}

function GenerateHeavenlyMountainRealm(portal)
    if not portal or not portal:IsValid() then
        return false
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Apply environmental effects
    ApplyMountainEnvironment(portal, HEAVENLY_MOUNTAIN_REALM.environment)
    
    -- Spawn unique resources
    SpawnMountainResources(portal_pos, HEAVENLY_MOUNTAIN_REALM.unique_resources)
    
    -- Spawn NPCs
    SpawnMountainNPCs(portal_pos, HEAVENLY_MOUNTAIN_REALM.npc_types)
    
    -- Spawn creatures
    SpawnMountainCreatures(portal_pos, HEAVENLY_MOUNTAIN_REALM.creatures)
    
    -- Apply special mechanics
    ApplyMountainMechanics(portal, HEAVENLY_MOUNTAIN_REALM.special_mechanics)
    
    return true
end

function ApplyMountainEnvironment(portal, env_settings)
    local radius = 20 -- Larger realm
    local portal_pos = portal:GetPosition()
    
    -- Create mountain environment aura
    local aura = SpawnPrefab("mountain_aura")
    if aura then
        aura.Transform:SetPosition(portal_pos:Get())
        aura.components.aura:SetRadius(radius)
        aura.components.aura:SetAuraType("cultivation")
        
        portal.realm_aura = aura
    end
    
    -- Spawn mountain decorations
    for i = 1, 12 do
        local angle = (i / 12) * 2 * PI
        local dist = 10 + math.random() * 8
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local decoration = SpawnPrefab("mountain_rock_formation")
        if decoration then
            decoration.Transform:SetPosition(x, 0, z)
        end
    end
    
    -- Create mist effects
    for i = 1, 6 do
        local angle = math.random() * 2 * PI
        local dist = 5 + math.random() * 10
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local mist = SpawnPrefab("mountain_mist_fx")
        if mist then
            mist.Transform:SetPosition(x, 2, z)
        end
    end
end

function SpawnMountainResources(center_pos, resource_list)
    for _, resource_name in ipairs(resource_list) do
        local count = math.random(1, 3) -- Fewer but more valuable resources
        
        for i = 1, count do
            local angle = math.random() * 2 * PI
            local dist = 8 + math.random() * 10
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local resource = SpawnPrefab(resource_name)
            if resource then
                resource.Transform:SetPosition(x, 0, z)
                resource:AddTag("realm_resource")
                resource.realm_type = "heavenly_mountain"
            end
        end
    end
end

function SpawnMountainNPCs(center_pos, npc_list)
    for _, npc_data in ipairs(npc_list) do
        if math.random() < npc_data.chance then
            local angle = math.random() * 2 * PI
            local dist = 8 + math.random() * 6
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local npc = SpawnPrefab(npc_data.name)
            if npc then
                npc.Transform:SetPosition(x, 0, z)
                
                if npc.components.npc_cultivator then
                    npc.components.npc_cultivator:SetAlignment(npc_data.alignment)
                end
                
                npc:AddTag("realm_npc")
                npc.realm_type = "heavenly_mountain"
            end
        end
    end
end

function SpawnMountainCreatures(center_pos, creature_list)
    for _, creature_data in ipairs(creature_list) do
        if math.random() < creature_data.chance then
            local count = math.random(creature_data.count[1], creature_data.count[2])
            
            for i = 1, count do
                local angle = math.random() * 2 * PI
                local dist = 6 + math.random() * 12
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist
                
                local creature = SpawnPrefab(creature_data.name)
                if creature then
                    creature.Transform:SetPosition(x, 0, z)
                    creature:AddTag("realm_creature")
                    creature.realm_type = "heavenly_mountain"
                end
            end
        end
    end
end

function ApplyMountainMechanics(portal, mechanics)
    local portal_pos = portal:GetPosition()
    
    if mechanics.cultivation_shrine then
        -- Create cultivation shrine
        local shrine = SpawnPrefab("cultivation_shrine")
        if shrine then
            local angle = math.random() * 2 * PI
            local dist = 12
            local x = portal_pos.x + math.cos(angle) * dist
            local z = portal_pos.z + math.sin(angle) * dist
            shrine.Transform:SetPosition(x, 0, z)
            shrine:AddTag("realm_structure")
            shrine.realm_type = "heavenly_mountain"
        end
    end
    
    if mechanics.altitude_effects then
        -- Altitude effects - slower movement but better cultivation
        portal.altitude_task = portal:DoPeriodicTask(2, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                -- Slight movement penalty
                if player.components.locomotor then
                    player.components.locomotor:SetExternalSpeedMultiplier(player, "altitude", 0.9)
                end
                
                -- Cultivation bonus
                if player.components.cultivation and player.components.cultivation.is_meditating then
                    if math.random() < 0.4 then -- 40% chance per 2 seconds
                        player.components.cultivation:AddExperience(3)
                    end
                end
            end
        end)
    end
    
    if mechanics.spirit_veins then
        -- Spiritual energy veins that regenerate player's spiritual energy
        portal.spirit_vein_task = portal:DoPeriodicTask(10, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                if player.components.cultivation then
                    player.components.cultivation:AddSpiritualEnergy(8)
                    
                    -- Visual effect
                    local fx = SpawnPrefab("spirit_vein_fx")
                    if fx then
                        fx.Transform:SetPosition(player.Transform:GetWorldPosition())
                    end
                end
            end
        end)
    end
    
    if mechanics.weather_control then
        -- Occasional weather effects
        portal.weather_task = portal:DoPeriodicTask(60, function() -- Every minute
            if math.random() < 0.2 then -- 20% chance
                local weather_effects = {"mountain_wind", "spirit_snow", "celestial_light"}
                local effect = weather_effects[math.random(#weather_effects)]
                
                -- Apply weather effect
                local fx = SpawnPrefab(effect .. "_fx")
                if fx then
                    fx.Transform:SetPosition(portal_pos:Get())
                end
                
                -- Weather affects players
                local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
                for _, player in ipairs(players) do
                    if effect == "celestial_light" then
                        -- Healing light
                        if player.components.health then
                            player.components.health:DoDelta(10)
                        end
                    elseif effect == "spirit_snow" then
                        -- Cooling effect
                        if player.components.temperature then
                            player.components.temperature:DoDelta(-10)
                        end
                    elseif effect == "mountain_wind" then
                        -- Speed boost
                        if player.components.locomotor then
                            player.components.locomotor:SetExternalSpeedMultiplier(player, "mountain_wind", 1.2)
                            player:DoTaskInTime(30, function()
                                if player:IsValid() and player.components.locomotor then
                                    player.components.locomotor:RemoveExternalSpeedMultiplier(player, "mountain_wind")
                                end
                            end)
                        end
                    end
                end
            end
        end)
    end
end

function CleanupHeavenlyMountainRealm(portal)
    if not portal or not portal:IsValid() then
        return
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Remove realm-specific entities
    local realm_entities = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, 
        {"realm_resource", "realm_npc", "realm_creature", "realm_structure"})
    
    for _, entity in ipairs(realm_entities) do
        if entity.realm_type == "heavenly_mountain" then
            entity:Remove()
        end
    end
    
    -- Clean up aura
    if portal.realm_aura and portal.realm_aura:IsValid() then
        portal.realm_aura:Remove()
    end
    
    -- Cancel tasks
    local tasks = {"altitude_task", "spirit_vein_task", "weather_task"}
    for _, task_name in ipairs(tasks) do
        if portal[task_name] then
            portal[task_name]:Cancel()
            portal[task_name] = nil
        end
    end
    
    -- Remove player effects
    for _, player in ipairs(AllPlayers) do
        if player and player:IsValid() and player.components.locomotor then
            player.components.locomotor:RemoveExternalSpeedMultiplier(player, "altitude")
            player.components.locomotor:RemoveExternalSpeedMultiplier(player, "mountain_wind")
        end
    end
end

-- Register realm type
GLOBAL.CULTIVATION_REALMS = GLOBAL.CULTIVATION_REALMS or {}
GLOBAL.CULTIVATION_REALMS["heavenly_mountain"] = {
    data = HEAVENLY_MOUNTAIN_REALM,
    generate = GenerateHeavenlyMountainRealm,
    cleanup = CleanupHeavenlyMountainRealm,
}

print("[Tu Tiên Bí Cảnh] Heavenly Mountain Realm system loaded")
