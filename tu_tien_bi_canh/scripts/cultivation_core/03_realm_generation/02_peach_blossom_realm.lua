-- Peach Blossom Realm Generation
-- 桃花秘境 - Peaceful realm with healing properties

local PEACH_BLOSSOM_REALM = {
    name = "peach_blossom",
    display_name = "桃花秘境",
    description = "春意盎然的桃花仙境，充满治愈之力",

    -- Environment settings
    environment = {
        season = "spring",
        temperature_modifier = 0.8, -- Slightly warmer
        moisture_modifier = 1.2, -- More humid
        light_modifier = 1.1, -- Brighter
        sanity_aura = 0.5, -- Positive sanity effect
    },

    -- Spawn settings
    spawn_requirements = {
        min_day = 5,
        min_player_level = 1,
        spawn_weight = 3, -- Higher chance to spawn
    },

    -- Resources unique to this realm
    unique_resources = {
        "peach_blossom_flower",
        "spring_water",
        "healing_dew",
        "butterfly_essence",
    },

    -- NPCs that can spawn here
    npc_types = {
        {name = "peach_fairy", chance = 0.8, alignment = "friendly"},
        {name = "flower_spirit", chance = 0.6, alignment = "neutral"},
        {name = "spring_guardian", chance = 0.3, alignment = "friendly"},
    },

    -- Creatures in this realm
    creatures = {
        {name = "flower_butterfly", chance = 0.9, count = {3, 6}},
        {name = "healing_rabbit", chance = 0.7, count = {2, 4}},
        {name = "peach_tree_spirit", chance = 0.4, count = {1, 2}},
    },

    -- Special mechanics
    special_mechanics = {
        healing_aura = true,
        flower_rain = true,
        peaceful_zone = true,
        meditation_bonus = 2.0,
    },
}

-- Generate realm content when portal is activated
function GeneratePeachBlossomRealm(portal)
    if not portal or not portal:IsValid() then
        return false
    end

    local portal_pos = portal:GetPosition()

    -- Apply environmental effects
    ApplyRealmEnvironment(portal, PEACH_BLOSSOM_REALM.environment)

    -- Spawn unique resources
    SpawnRealmResources(portal_pos, PEACH_BLOSSOM_REALM.unique_resources)

    -- Spawn NPCs
    SpawnRealmNPCs(portal_pos, PEACH_BLOSSOM_REALM.npc_types)

    -- Spawn creatures
    SpawnRealmCreatures(portal_pos, PEACH_BLOSSOM_REALM.creatures)

    -- Apply special mechanics
    ApplySpecialMechanics(portal, PEACH_BLOSSOM_REALM.special_mechanics)

    return true
end

function ApplyRealmEnvironment(portal, env_settings)
    local radius = 15 -- Realm effect radius
    local portal_pos = portal:GetPosition()

    -- Create environmental aura
    local aura = SpawnPrefab("peach_blossom_aura")
    if aura then
        aura.Transform:SetPosition(portal_pos:Get())
        aura.components.aura:SetRadius(radius)
        aura.components.aura:SetAuraType("healing")

        -- Link to portal for cleanup
        portal.realm_aura = aura
    end

    -- Spawn decorative elements
    for i = 1, 8 do
        local angle = (i / 8) * 2 * PI
        local dist = 8 + math.random() * 5
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist

        local decoration = SpawnPrefab("peach_tree_decoration")
        if decoration then
            decoration.Transform:SetPosition(x, 0, z)
        end
    end
end

function SpawnRealmResources(center_pos, resource_list)
    for _, resource_name in ipairs(resource_list) do
        local count = math.random(2, 5)

        for i = 1, count do
            local angle = math.random() * 2 * PI
            local dist = 5 + math.random() * 8
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist

            local resource = SpawnPrefab(resource_name)
            if resource then
                resource.Transform:SetPosition(x, 0, z)

                -- Mark as realm resource for cleanup
                resource:AddTag("realm_resource")
                resource.realm_type = "peach_blossom"
            end
        end
    end
end

function SpawnRealmNPCs(center_pos, npc_list)
    for _, npc_data in ipairs(npc_list) do
        if math.random() < npc_data.chance then
            local angle = math.random() * 2 * PI
            local dist = 6 + math.random() * 4
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist

            local npc = SpawnPrefab(npc_data.name)
            if npc then
                npc.Transform:SetPosition(x, 0, z)

                -- Set alignment
                if npc.components.npc_cultivator then
                    npc.components.npc_cultivator:SetAlignment(npc_data.alignment)
                end

                -- Mark as realm NPC
                npc:AddTag("realm_npc")
                npc.realm_type = "peach_blossom"
            end
        end
    end
end

function SpawnRealmCreatures(center_pos, creature_list)
    for _, creature_data in ipairs(creature_list) do
        if math.random() < creature_data.chance then
            local count = math.random(creature_data.count[1], creature_data.count[2])

            for i = 1, count do
                local angle = math.random() * 2 * PI
                local dist = 4 + math.random() * 10
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist

                local creature = SpawnPrefab(creature_data.name)
                if creature then
                    creature.Transform:SetPosition(x, 0, z)

                    -- Mark as realm creature
                    creature:AddTag("realm_creature")
                    creature.realm_type = "peach_blossom"
                end
            end
        end
    end
end

function ApplySpecialMechanics(portal, mechanics)
    local portal_pos = portal:GetPosition()

    if mechanics.healing_aura then
        -- Create healing effect for players in range
        portal.healing_task = portal:DoPeriodicTask(5, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 15, {"player"})
            for _, player in ipairs(players) do
                if player.components.health and not player.components.health:IsDead() then
                    player.components.health:DoDelta(5) -- Heal 5 HP every 5 seconds

                    -- Visual effect
                    local fx = SpawnPrefab("healing_sparkle_fx")
                    if fx then
                        fx.Transform:SetPosition(player.Transform:GetWorldPosition())
                    end
                end
            end
        end)
    end

    if mechanics.flower_rain then
        -- Periodic flower rain effect
        portal.flower_rain_task = portal:DoPeriodicTask(30, function()
            for i = 1, 10 do
                local angle = math.random() * 2 * PI
                local dist = math.random() * 12
                local x = portal_pos.x + math.cos(angle) * dist
                local z = portal_pos.z + math.sin(angle) * dist

                local flower = SpawnPrefab("falling_peach_blossom")
                if flower then
                    flower.Transform:SetPosition(x, 8, z) -- Start from above
                end
            end
        end)
    end

    if mechanics.meditation_bonus then
        -- Enhanced meditation in this realm
        portal.meditation_bonus_task = portal:DoPeriodicTask(1, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 15, {"player"})
            for _, player in ipairs(players) do
                if player.components.cultivation and player.components.cultivation.is_meditating then
                    -- Extra experience gain
                    if math.random() < 0.3 then -- 30% chance per second
                        player.components.cultivation:AddExperience(2)
                    end

                    -- Extra spiritual energy
                    player.components.cultivation:AddSpiritualEnergy(1)
                end
            end
        end)
    end
end

-- Cleanup function when realm closes
function CleanupPeachBlossomRealm(portal)
    if not portal or not portal:IsValid() then
        return
    end

    local portal_pos = portal:GetPosition()

    -- Remove realm-specific entities
    local realm_entities = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20,
        {"realm_resource", "realm_npc", "realm_creature"})

    for _, entity in ipairs(realm_entities) do
        if entity.realm_type == "peach_blossom" then
            entity:Remove()
        end
    end

    -- Clean up aura
    if portal.realm_aura and portal.realm_aura:IsValid() then
        portal.realm_aura:Remove()
    end

    -- Cancel tasks
    if portal.healing_task then
        portal.healing_task:Cancel()
        portal.healing_task = nil
    end

    if portal.flower_rain_task then
        portal.flower_rain_task:Cancel()
        portal.flower_rain_task = nil
    end

    if portal.meditation_bonus_task then
        portal.meditation_bonus_task:Cancel()
        portal.meditation_bonus_task = nil
    end
end

-- Register realm type
GLOBAL.CULTIVATION_REALMS = GLOBAL.CULTIVATION_REALMS or {}
GLOBAL.CULTIVATION_REALMS["peach_blossom"] = {
    data = PEACH_BLOSSOM_REALM,
    generate = GeneratePeachBlossomRealm,
    cleanup = CleanupPeachBlossomRealm,
}

print("[Tu Tiên Bí Cảnh] Peach Blossom Realm system loaded")
