-- Medicine Valley Realm Generation
-- 药王谷 - Peaceful valley filled with medicinal herbs and alchemy masters

local MEDICINE_VALLEY_REALM = {
    name = "medicine_valley",
    display_name = "药王谷",
    description = "百草丰茂，药香阵阵的仙谷",
    
    -- Environment settings
    environment = {
        season = "spring",
        temperature_modifier = 1.0, -- Perfect temperature
        moisture_modifier = 1.2, -- Humid, good for plants
        light_modifier = 1.1, -- Bright sunlight
        healing_aura = true,
        herb_growth_bonus = true,
    },
    
    -- Spawn settings
    spawn_requirements = {
        min_day = 15,
        min_player_level = 2,
        spawn_weight = 2.5, -- Common spawn
    },
    
    -- Resources unique to this realm
    unique_resources = {
        "thousand_year_ginseng",
        "spirit_mushroom",
        "healing_lotus",
        "immortal_herb",
        "medicine_spring",
    },
    
    -- NPCs that can spawn here
    npc_types = {
        {name = "medicine_master", chance = 0.8, alignment = "friendly"},
        {name = "herb_collector", chance = 0.7, alignment = "friendly"},
        {name = "alchemy_apprentice", chance = 0.6, alignment = "neutral"},
    },
    
    -- Creatures in this realm
    creatures = {
        {name = "medicine_deer", chance = 0.9, count = {2, 4}},
        {name = "herb_spirit", chance = 0.8, count = {3, 5}},
        {name = "healing_butterfly", chance = 0.9, count = {4, 8}},
    },
    
    -- Special mechanics
    special_mechanics = {
        herb_garden = true,
        alchemy_stations = true,
        healing_springs = true,
        medicine_knowledge = true,
        herb_cultivation = true,
    },
}

function GenerateMedicineValleyRealm(portal)
    if not portal or not portal:IsValid() then
        return false
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Apply medicine valley environment
    ApplyMedicineValleyEnvironment(portal, MEDICINE_VALLEY_REALM.environment)
    
    -- Spawn medicine resources
    SpawnMedicineValleyResources(portal_pos, MEDICINE_VALLEY_REALM.unique_resources)
    
    -- Spawn medicine NPCs and creatures
    SpawnMedicineValleyNPCs(portal_pos, MEDICINE_VALLEY_REALM.npc_types)
    SpawnMedicineValleyCreatures(portal_pos, MEDICINE_VALLEY_REALM.creatures)
    
    -- Apply medicine valley mechanics
    ApplyMedicineValleyMechanics(portal, MEDICINE_VALLEY_REALM.special_mechanics)
    
    return true
end

function ApplyMedicineValleyEnvironment(portal, env_settings)
    local radius = 20
    local portal_pos = portal:GetPosition()
    
    -- Create healing aura
    local aura = SpawnPrefab("medicine_aura")
    if aura then
        aura.Transform:SetPosition(portal_pos:Get())
        aura.components.aura:SetRadius(radius)
        aura.components.aura:SetAuraType("healing")
        portal.realm_aura = aura
    end
    
    -- Spawn herb gardens
    for i = 1, 15 do
        local angle = (i / 15) * 2 * PI
        local dist = 6 + math.random() * 10
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local gardens = {"herb_patch", "flower_garden", "medicine_tree", "spirit_bamboo"}
        local garden = SpawnPrefab(gardens[math.random(#gardens)])
        if garden then
            garden.Transform:SetPosition(x, 0, z)
        end
    end
    
    -- Create peaceful effects
    for i = 1, 8 do
        local angle = math.random() * 2 * PI
        local dist = 4 + math.random() * 12
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local effect = SpawnPrefab("medicine_valley_fx")
        if effect then
            effect.Transform:SetPosition(x, 1, z)
        end
    end
end

function SpawnMedicineValleyResources(center_pos, resource_list)
    for _, resource_name in ipairs(resource_list) do
        local count = math.random(3, 6) -- Abundant herbs
        
        for i = 1, count do
            local angle = math.random() * 2 * PI
            local dist = 5 + math.random() * 12
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local resource = SpawnPrefab(resource_name)
            if resource then
                resource.Transform:SetPosition(x, 0, z)
                resource:AddTag("realm_resource")
                resource.realm_type = "medicine_valley"
            end
        end
    end
end

function SpawnMedicineValleyNPCs(center_pos, npc_list)
    for _, npc_data in ipairs(npc_list) do
        if math.random() < npc_data.chance then
            local angle = math.random() * 2 * PI
            local dist = 8 + math.random() * 8
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local npc = SpawnPrefab(npc_data.name)
            if npc then
                npc.Transform:SetPosition(x, 0, z)
                
                if npc.components.npc_cultivator then
                    npc.components.npc_cultivator:SetAlignment(npc_data.alignment)
                end
                
                npc:AddTag("realm_npc")
                npc.realm_type = "medicine_valley"
            end
        end
    end
end

function SpawnMedicineValleyCreatures(center_pos, creature_list)
    for _, creature_data in ipairs(creature_list) do
        if math.random() < creature_data.chance then
            local count = math.random(creature_data.count[1], creature_data.count[2])
            
            for i = 1, count do
                local angle = math.random() * 2 * PI
                local dist = 4 + math.random() * 14
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist
                
                local creature = SpawnPrefab(creature_data.name)
                if creature then
                    creature.Transform:SetPosition(x, 0, z)
                    creature:AddTag("realm_creature")
                    creature.realm_type = "medicine_valley"
                end
            end
        end
    end
end

function ApplyMedicineValleyMechanics(portal, mechanics)
    local portal_pos = portal:GetPosition()
    
    if mechanics.herb_garden then
        -- Herbs regrow over time
        portal.herb_regrow_task = portal:DoPeriodicTask(60, function() -- Every minute
            if math.random() < 0.5 then
                local angle = math.random() * 2 * PI
                local dist = 5 + math.random() * 12
                local x = portal_pos.x + math.cos(angle) * dist
                local z = portal_pos.z + math.sin(angle) * dist
                
                local herbs = {"spirit_mushroom", "healing_lotus", "immortal_herb"}
                local herb = SpawnPrefab(herbs[math.random(#herbs)])
                if herb then
                    herb.Transform:SetPosition(x, 0, z)
                    herb:AddTag("realm_resource")
                    herb.realm_type = "medicine_valley"
                end
            end
        end)
    end
    
    if mechanics.alchemy_stations then
        -- Spawn advanced alchemy stations
        for i = 1, 2 do
            local angle = (i / 2) * PI
            local dist = 12 + math.random() * 4
            local x = portal_pos.x + math.cos(angle) * dist
            local z = portal_pos.z + math.sin(angle) * dist
            
            local station = SpawnPrefab("advanced_alchemy_furnace")
            if station then
                station.Transform:SetPosition(x, 0, z)
                station:AddTag("realm_structure")
                station.realm_type = "medicine_valley"
                
                -- Enhanced alchemy success rate
                if station.components.alchemy_furnace then
                    station.components.alchemy_furnace.furnace_level = 3
                end
            end
        end
    end
    
    if mechanics.healing_springs then
        -- Continuous healing for all players
        portal.healing_task = portal:DoPeriodicTask(3, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                if player.components.health and not player.components.health:IsDead() then
                    player.components.health:DoDelta(8) -- Strong healing
                    
                    -- Cure poison and other ailments
                    if player.components.poisonable and player.components.poisonable:IsPoisoned() then
                        player.components.poisonable:Cure()
                    end
                    
                    -- Restore sanity
                    if player.components.sanity then
                        player.components.sanity:DoDelta(3)
                    end
                    
                    -- Visual healing effect
                    if math.random() < 0.3 then
                        local fx = SpawnPrefab("healing_spring_fx")
                        if fx then
                            fx.Transform:SetPosition(player.Transform:GetWorldPosition())
                        end
                    end
                end
            end
        end)
    end
    
    if mechanics.medicine_knowledge then
        -- Players can learn alchemy recipes
        portal.knowledge_task = portal:DoPeriodicTask(30, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                if player.components.cultivation and math.random() < 0.1 then
                    -- Learn random alchemy recipe
                    player.components.cultivation:AddExperience(10)
                    
                    if player.components.talker then
                        player.components.talker:Say("领悟了炼丹知识！", 2)
                    end
                    
                    -- Could add recipe learning system here
                end
            end
        end)
    end
    
    if mechanics.herb_cultivation then
        -- Players can plant and grow herbs faster
        portal.herb_cultivation_task = portal:DoPeriodicTask(5, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"player"})
            for _, player in ipairs(players) do
                player.medicine_valley_bonus = true -- Herb growing bonus
                
                if player.components.cultivation then
                    -- Alchemy experience bonus
                    if math.random() < 0.05 then
                        player.components.cultivation:AddExperience(1)
                    end
                end
            end
        end)
    end
    
    -- Spawn medicine springs
    for i = 1, 3 do
        local angle = (i / 3) * 2 * PI + math.random() * 0.5
        local dist = 8 + math.random() * 6
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local spring = SpawnPrefab("medicine_spring")
        if spring then
            spring.Transform:SetPosition(x, 0, z)
            spring:AddTag("realm_structure")
            spring.realm_type = "medicine_valley"
        end
    end
    
    -- Peaceful aura - no aggressive creatures spawn
    portal.peace_task = portal:DoPeriodicTask(10, function()
        local hostiles = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 20, {"hostile"})
        for _, hostile in ipairs(hostiles) do
            if not hostile:HasTag("realm_creature") then
                -- Remove non-realm hostile creatures
                hostile:Remove()
            end
        end
    end)
end

function CleanupMedicineValleyRealm(portal)
    if not portal or not portal:IsValid() then
        return
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Remove medicine valley bonuses from players
    local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
    for _, player in ipairs(players) do
        player.medicine_valley_bonus = nil
    end
    
    -- Remove realm-specific entities
    local realm_entities = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, 
        {"realm_resource", "realm_npc", "realm_creature", "realm_structure"})
    
    for _, entity in ipairs(realm_entities) do
        if entity.realm_type == "medicine_valley" then
            entity:Remove()
        end
    end
    
    -- Clean up aura
    if portal.realm_aura and portal.realm_aura:IsValid() then
        portal.realm_aura:Remove()
    end
    
    -- Cancel tasks
    local tasks = {"herb_regrow_task", "healing_task", "knowledge_task", "herb_cultivation_task", "peace_task"}
    for _, task_name in ipairs(tasks) do
        if portal[task_name] then
            portal[task_name]:Cancel()
            portal[task_name] = nil
        end
    end
end

-- Register realm type
GLOBAL.CULTIVATION_REALMS = GLOBAL.CULTIVATION_REALMS or {}
GLOBAL.CULTIVATION_REALMS["medicine_valley"] = {
    data = MEDICINE_VALLEY_REALM,
    generate = GenerateMedicineValleyRealm,
    cleanup = CleanupMedicineValleyRealm,
}

print("[Tu Tiên Bí Cảnh] Medicine Valley Realm system loaded")
