-- Ancient Battlefield Realm Generation
-- 古战场 - Dangerous PvP realm with ancient weapons and spirits

local ANCIENT_BATTLEFIELD_REALM = {
    name = "ancient_battlefield",
    display_name = "古战场",
    description = "刀光剑影，英魂不散的古代战场",
    
    -- Environment settings
    environment = {
        season = "autumn",
        temperature_modifier = 0.9,
        moisture_modifier = 0.7,
        light_modifier = 0.8, -- Darker, ominous
        danger_level = "high",
        pvp_enabled = true,
    },
    
    -- Spawn settings
    spawn_requirements = {
        min_day = 30,
        min_player_level = 4,
        spawn_weight = 1, -- Rare spawn
    },
    
    -- Resources unique to this realm
    unique_resources = {
        "ancient_weapon_fragment",
        "battle_spirit_essence",
        "blood_crystal",
        "war_banner_cloth",
        "heroic_soul_gem",
    },
    
    -- NPCs that can spawn here
    npc_types = {
        {name = "ancient_warrior_spirit", chance = 0.8, alignment = "hostile"},
        {name = "battlefield_scavenger", chance = 0.6, alignment = "neutral"},
        {name = "war_general_ghost", chance = 0.3, alignment = "hostile"},
    },
    
    -- Creatures in this realm
    creatures = {
        {name = "skeleton_warrior", chance = 0.9, count = {3, 6}},
        {name = "battle_wraith", chance = 0.7, count = {2, 4}},
        {name = "ancient_war_beast", chance = 0.4, count = {1, 2}},
    },
    
    -- Special mechanics
    special_mechanics = {
        pvp_zone = true,
        weapon_spawns = true,
        battle_aura = true,
        spirit_interference = true,
        combat_enhancement = true,
    },
}

function GenerateAncientBattlefieldRealm(portal)
    if not portal or not portal:IsValid() then
        return false
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Apply battlefield environment
    ApplyBattlefieldEnvironment(portal, ANCIENT_BATTLEFIELD_REALM.environment)
    
    -- Spawn battlefield resources
    SpawnBattlefieldResources(portal_pos, ANCIENT_BATTLEFIELD_REALM.unique_resources)
    
    -- Spawn hostile NPCs and creatures
    SpawnBattlefieldNPCs(portal_pos, ANCIENT_BATTLEFIELD_REALM.npc_types)
    SpawnBattlefieldCreatures(portal_pos, ANCIENT_BATTLEFIELD_REALM.creatures)
    
    -- Apply special battlefield mechanics
    ApplyBattlefieldMechanics(portal, ANCIENT_BATTLEFIELD_REALM.special_mechanics)
    
    return true
end

function ApplyBattlefieldEnvironment(portal, env_settings)
    local radius = 25 -- Large dangerous area
    local portal_pos = portal:GetPosition()
    
    -- Create ominous battlefield aura
    local aura = SpawnPrefab("battlefield_aura")
    if aura then
        aura.Transform:SetPosition(portal_pos:Get())
        aura.components.aura:SetRadius(radius)
        aura.components.aura:SetAuraType("battle")
        portal.realm_aura = aura
    end
    
    -- Spawn battlefield decorations
    for i = 1, 15 do
        local angle = (i / 15) * 2 * PI
        local dist = 8 + math.random() * 12
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local decorations = {"broken_weapon", "ancient_armor", "battle_standard", "skeleton_remains"}
        local decoration = SpawnPrefab(decorations[math.random(#decorations)])
        if decoration then
            decoration.Transform:SetPosition(x, 0, z)
        end
    end
    
    -- Create blood mist effects
    for i = 1, 8 do
        local angle = math.random() * 2 * PI
        local dist = 5 + math.random() * 15
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local mist = SpawnPrefab("blood_mist_fx")
        if mist then
            mist.Transform:SetPosition(x, 1, z)
        end
    end
end

function SpawnBattlefieldResources(center_pos, resource_list)
    for _, resource_name in ipairs(resource_list) do
        local count = math.random(1, 2) -- Rare but valuable
        
        for i = 1, count do
            local angle = math.random() * 2 * PI
            local dist = 10 + math.random() * 12
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local resource = SpawnPrefab(resource_name)
            if resource then
                resource.Transform:SetPosition(x, 0, z)
                resource:AddTag("realm_resource")
                resource.realm_type = "ancient_battlefield"
            end
        end
    end
end

function SpawnBattlefieldNPCs(center_pos, npc_list)
    for _, npc_data in ipairs(npc_list) do
        if math.random() < npc_data.chance then
            local angle = math.random() * 2 * PI
            local dist = 12 + math.random() * 8
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local npc = SpawnPrefab(npc_data.name)
            if npc then
                npc.Transform:SetPosition(x, 0, z)
                
                if npc.components.npc_cultivator then
                    npc.components.npc_cultivator:SetAlignment(npc_data.alignment)
                    npc.components.npc_cultivator:SetLevel(math.random(4, 6)) -- High level
                end
                
                npc:AddTag("realm_npc")
                npc.realm_type = "ancient_battlefield"
            end
        end
    end
end

function SpawnBattlefieldCreatures(center_pos, creature_list)
    for _, creature_data in ipairs(creature_list) do
        if math.random() < creature_data.chance then
            local count = math.random(creature_data.count[1], creature_data.count[2])
            
            for i = 1, count do
                local angle = math.random() * 2 * PI
                local dist = 8 + math.random() * 15
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist
                
                local creature = SpawnPrefab(creature_data.name)
                if creature then
                    creature.Transform:SetPosition(x, 0, z)
                    creature:AddTag("realm_creature")
                    creature.realm_type = "ancient_battlefield"
                end
            end
        end
    end
end

function ApplyBattlefieldMechanics(portal, mechanics)
    local portal_pos = portal:GetPosition()
    
    if mechanics.pvp_zone then
        -- Enable PvP in this realm
        portal.pvp_task = portal:DoPeriodicTask(1, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
            for _, player in ipairs(players) do
                if not player:HasTag("pvp_enabled") then
                    player:AddTag("pvp_enabled")
                    if player.components.talker then
                        player.components.talker:Say("进入了PvP区域！", 2)
                    end
                end
            end
        end)
    end
    
    if mechanics.weapon_spawns then
        -- Periodically spawn ancient weapons
        portal.weapon_spawn_task = portal:DoPeriodicTask(60, function() -- Every minute
            if math.random() < 0.3 then
                local angle = math.random() * 2 * PI
                local dist = 10 + math.random() * 10
                local x = portal_pos.x + math.cos(angle) * dist
                local z = portal_pos.z + math.sin(angle) * dist
                
                local weapons = {"ancient_sword", "battle_axe", "war_spear", "spirit_blade"}
                local weapon = SpawnPrefab(weapons[math.random(#weapons)])
                if weapon then
                    weapon.Transform:SetPosition(x, 0, z)
                    weapon:AddTag("realm_weapon")
                end
            end
        end)
    end
    
    if mechanics.battle_aura then
        -- Combat enhancement aura
        portal.battle_aura_task = portal:DoPeriodicTask(3, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
            for _, player in ipairs(players) do
                if player.components.combat then
                    -- Increase damage but also take more damage
                    player.components.combat:AddDamageModifier("battlefield", 1.3)
                    if player.components.health then
                        player.components.health:SetAbsorption(0.8) -- Take 20% more damage
                    end
                end
                
                if player.components.cultivation then
                    -- Gain experience from combat
                    if player.components.combat.target then
                        player.components.cultivation:AddExperience(1)
                    end
                end
            end
        end)
    end
    
    if mechanics.spirit_interference then
        -- Spirits interfere with spells
        portal.spirit_interference_task = portal:DoPeriodicTask(10, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
            for _, player in ipairs(players) do
                if player.components.spell_caster and math.random() < 0.2 then
                    -- Random spell cooldown increase
                    if player.components.talker then
                        player.components.talker:Say("战场怨灵干扰了法术！", 2)
                    end
                    
                    -- Add temporary spell cost increase
                    if player.components.cultivation then
                        player.components.cultivation:ConsumeSpiritualEnergy(5)
                    end
                end
            end
        end)
    end
    
    if mechanics.combat_enhancement then
        -- Enhanced combat rewards
        portal.combat_reward_task = portal:DoPeriodicTask(5, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
            for _, player in ipairs(players) do
                -- Check if player killed something recently
                if player.recent_kill_time and GetTime() - player.recent_kill_time < 5 then
                    if player.components.cultivation then
                        player.components.cultivation:AddExperience(5)
                        player.components.cultivation:AddSpiritualEnergy(3)
                    end
                    
                    -- Chance to drop battle loot
                    if math.random() < 0.3 then
                        local loot = {"battle_spirit_essence", "blood_crystal", "ancient_weapon_fragment"}
                        local item = SpawnPrefab(loot[math.random(#loot)])
                        if item then
                            item.Transform:SetPosition(player.Transform:GetWorldPosition())
                        end
                    end
                    
                    player.recent_kill_time = nil
                end
            end
        end)
    end
end

function CleanupAncientBattlefieldRealm(portal)
    if not portal or not portal:IsValid() then
        return
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Remove PvP tags from players
    local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 30, {"player"})
    for _, player in ipairs(players) do
        player:RemoveTag("pvp_enabled")
        
        if player.components.combat then
            player.components.combat:RemoveDamageModifier("battlefield")
        end
        
        if player.components.health then
            player.components.health:SetAbsorption(1.0) -- Reset damage absorption
        end
        
        if player.components.talker then
            player.components.talker:Say("离开了PvP区域。", 2)
        end
    end
    
    -- Remove realm-specific entities
    local realm_entities = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 30, 
        {"realm_resource", "realm_npc", "realm_creature", "realm_weapon"})
    
    for _, entity in ipairs(realm_entities) do
        if entity.realm_type == "ancient_battlefield" or entity:HasTag("realm_weapon") then
            entity:Remove()
        end
    end
    
    -- Clean up aura
    if portal.realm_aura and portal.realm_aura:IsValid() then
        portal.realm_aura:Remove()
    end
    
    -- Cancel tasks
    local tasks = {"pvp_task", "weapon_spawn_task", "battle_aura_task", "spirit_interference_task", "combat_reward_task"}
    for _, task_name in ipairs(tasks) do
        if portal[task_name] then
            portal[task_name]:Cancel()
            portal[task_name] = nil
        end
    end
end

-- Register realm type
GLOBAL.CULTIVATION_REALMS = GLOBAL.CULTIVATION_REALMS or {}
GLOBAL.CULTIVATION_REALMS["ancient_battlefield"] = {
    data = ANCIENT_BATTLEFIELD_REALM,
    generate = GenerateAncientBattlefieldRealm,
    cleanup = CleanupAncientBattlefieldRealm,
}

print("[Tu Tiên Bí Cảnh] Ancient Battlefield Realm system loaded")
