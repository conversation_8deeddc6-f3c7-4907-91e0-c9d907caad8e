-- Moon Realm Generation
-- 阴月秘境 - Mysterious night realm with yin energy and dark magic

local MOON_REALM = {
    name = "moon_realm",
    display_name = "阴月秘境",
    description = "月华如水，阴气森森的神秘夜境",
    
    -- Environment settings
    environment = {
        season = "winter",
        temperature_modifier = 0.7, -- Cold
        moisture_modifier = 1.1, -- Humid
        light_modifier = 0.4, -- Very dark, only moonlight
        time_lock = "night", -- Always night
        yin_energy = true,
    },
    
    -- Spawn settings
    spawn_requirements = {
        min_day = 20,
        min_player_level = 3,
        spawn_weight = 2,
        preferred_time = "night", -- More likely to spawn at night
    },
    
    -- Resources unique to this realm
    unique_resources = {
        "moon_flower",
        "yin_crystal",
        "shadow_essence",
        "lunar_dew",
        "night_pearl",
    },
    
    -- NPCs that can spawn here
    npc_types = {
        {name = "moon_priestess", chance = 0.6, alignment = "neutral"},
        {name = "shadow_cultivator", chance = 0.5, alignment = "demonic"},
        {name = "lunar_spirit", chance = 0.4, alignment = "friendly"},
    },
    
    -- Creatures in this realm
    creatures = {
        {name = "shadow_wolf", chance = 0.8, count = {2, 4}},
        {name = "moon_rabbit", chance = 0.7, count = {1, 3}},
        {name = "night_phantom", chance = 0.5, count = {1, 2}},
    },
    
    -- Special mechanics
    special_mechanics = {
        yin_cultivation = true,
        shadow_magic = true,
        lunar_cycles = true,
        sanity_effects = true,
        night_vision = true,
    },
}

function GenerateMoonRealm(portal)
    if not portal or not portal:IsValid() then
        return false
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Apply moon environment
    ApplyMoonEnvironment(portal, MOON_REALM.environment)
    
    -- Spawn moon resources
    SpawnMoonResources(portal_pos, MOON_REALM.unique_resources)
    
    -- Spawn moon NPCs and creatures
    SpawnMoonNPCs(portal_pos, MOON_REALM.npc_types)
    SpawnMoonCreatures(portal_pos, MOON_REALM.creatures)
    
    -- Apply moon mechanics
    ApplyMoonMechanics(portal, MOON_REALM.special_mechanics)
    
    return true
end

function ApplyMoonEnvironment(portal, env_settings)
    local radius = 18
    local portal_pos = portal:GetPosition()
    
    -- Create moon aura
    local aura = SpawnPrefab("moon_aura")
    if aura then
        aura.Transform:SetPosition(portal_pos:Get())
        aura.components.aura:SetRadius(radius)
        aura.components.aura:SetAuraType("yin")
        portal.realm_aura = aura
    end
    
    -- Spawn moonlit decorations
    for i = 1, 10 do
        local angle = (i / 10) * 2 * PI
        local dist = 6 + math.random() * 8
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local decorations = {"moon_altar", "shadow_tree", "lunar_pond", "yin_stone"}
        local decoration = SpawnPrefab(decorations[math.random(#decorations)])
        if decoration then
            decoration.Transform:SetPosition(x, 0, z)
        end
    end
    
    -- Create moonbeam effects
    for i = 1, 6 do
        local angle = math.random() * 2 * PI
        local dist = 4 + math.random() * 10
        local x = portal_pos.x + math.cos(angle) * dist
        local z = portal_pos.z + math.sin(angle) * dist
        
        local moonbeam = SpawnPrefab("moonbeam_fx")
        if moonbeam then
            moonbeam.Transform:SetPosition(x, 0, z)
        end
    end
end

function SpawnMoonResources(center_pos, resource_list)
    for _, resource_name in ipairs(resource_list) do
        local count = math.random(2, 3)
        
        for i = 1, count do
            local angle = math.random() * 2 * PI
            local dist = 6 + math.random() * 10
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local resource = SpawnPrefab(resource_name)
            if resource then
                resource.Transform:SetPosition(x, 0, z)
                resource:AddTag("realm_resource")
                resource.realm_type = "moon_realm"
            end
        end
    end
end

function SpawnMoonNPCs(center_pos, npc_list)
    for _, npc_data in ipairs(npc_list) do
        if math.random() < npc_data.chance then
            local angle = math.random() * 2 * PI
            local dist = 8 + math.random() * 6
            local x = center_pos.x + math.cos(angle) * dist
            local z = center_pos.z + math.sin(angle) * dist
            
            local npc = SpawnPrefab(npc_data.name)
            if npc then
                npc.Transform:SetPosition(x, 0, z)
                
                if npc.components.npc_cultivator then
                    npc.components.npc_cultivator:SetAlignment(npc_data.alignment)
                end
                
                npc:AddTag("realm_npc")
                npc.realm_type = "moon_realm"
            end
        end
    end
end

function SpawnMoonCreatures(center_pos, creature_list)
    for _, creature_data in ipairs(creature_list) do
        if math.random() < creature_data.chance then
            local count = math.random(creature_data.count[1], creature_data.count[2])
            
            for i = 1, count do
                local angle = math.random() * 2 * PI
                local dist = 5 + math.random() * 12
                local x = center_pos.x + math.cos(angle) * dist
                local z = center_pos.z + math.sin(angle) * dist
                
                local creature = SpawnPrefab(creature_data.name)
                if creature then
                    creature.Transform:SetPosition(x, 0, z)
                    creature:AddTag("realm_creature")
                    creature.realm_type = "moon_realm"
                end
            end
        end
    end
end

function ApplyMoonMechanics(portal, mechanics)
    local portal_pos = portal:GetPosition()
    
    if mechanics.yin_cultivation then
        -- Yin energy cultivation bonus
        portal.yin_cultivation_task = portal:DoPeriodicTask(3, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 18, {"player"})
            for _, player in ipairs(players) do
                if player.components.cultivation then
                    -- Yin cultivation bonus
                    if player.components.cultivation.is_meditating then
                        if math.random() < 0.3 then
                            player.components.cultivation:AddExperience(2)
                            player.components.cultivation:AddSpiritualEnergy(3)
                        end
                    end
                    
                    -- Increase evil karma slightly
                    player.components.cultivation.evil_karma = (player.components.cultivation.evil_karma or 0) + 0.1
                end
            end
        end)
    end
    
    if mechanics.shadow_magic then
        -- Enhance shadow/yin spells
        portal.shadow_magic_task = portal:DoPeriodicTask(1, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 18, {"player"})
            for _, player in ipairs(players) do
                if player.components.spell_caster then
                    player.moon_realm_bonus = true -- Shadow spells enhanced
                end
            end
        end)
    end
    
    if mechanics.lunar_cycles then
        -- Simulate lunar phases affecting power
        portal.lunar_cycle_task = portal:DoPeriodicTask(120, function() -- Every 2 minutes
            local phase = math.random(1, 4) -- New, Waxing, Full, Waning
            local phase_names = {"新月", "上弦月", "满月", "下弦月"}
            
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 18, {"player"})
            for _, player in ipairs(players) do
                if player.components.talker then
                    player.components.talker:Say("月相变化：" .. phase_names[phase], 2)
                end
                
                if player.components.cultivation then
                    if phase == 3 then -- Full moon
                        player.components.cultivation:AddSpiritualEnergy(20)
                    elseif phase == 1 then -- New moon
                        player.components.cultivation:ConsumeSpiritualEnergy(5)
                    end
                end
            end
        end)
    end
    
    if mechanics.sanity_effects then
        -- Moon realm affects sanity
        portal.sanity_task = portal:DoPeriodicTask(5, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 18, {"player"})
            for _, player in ipairs(players) do
                if player.components.sanity then
                    -- Gradual sanity drain
                    player.components.sanity:DoDelta(-2)
                    
                    -- But also chance for insight
                    if math.random() < 0.1 then
                        player.components.sanity:DoDelta(10)
                        if player.components.cultivation then
                            player.components.cultivation:AddExperience(3)
                        end
                        if player.components.talker then
                            player.components.talker:Say("月光下获得了感悟！", 2)
                        end
                    end
                end
            end
        end)
    end
    
    if mechanics.night_vision then
        -- Grant temporary night vision
        portal.night_vision_task = portal:DoPeriodicTask(1, function()
            local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 18, {"player"})
            for _, player in ipairs(players) do
                if not player:HasTag("night_vision") then
                    player:AddTag("night_vision")
                    if player.components.talker then
                        player.components.talker:Say("获得了夜视能力。", 2)
                    end
                end
                
                -- Enhanced vision in darkness
                if player.components.vision then
                    player.components.vision:SetGhostVision(true)
                end
            end
        end)
    end
    
    -- Spawn shadow portals occasionally
    portal.shadow_portal_task = portal:DoPeriodicTask(90, function()
        if math.random() < 0.3 then
            local angle = math.random() * 2 * PI
            local dist = 10 + math.random() * 6
            local x = portal_pos.x + math.cos(angle) * dist
            local z = portal_pos.z + math.sin(angle) * dist
            
            local shadow_portal = SpawnPrefab("shadow_portal")
            if shadow_portal then
                shadow_portal.Transform:SetPosition(x, 0, z)
                shadow_portal:AddTag("realm_structure")
                shadow_portal.realm_type = "moon_realm"
                
                -- Portal disappears after 30 seconds
                shadow_portal:DoTaskInTime(30, function()
                    if shadow_portal:IsValid() then
                        shadow_portal:Remove()
                    end
                end)
            end
        end
    end)
end

function CleanupMoonRealm(portal)
    if not portal or not portal:IsValid() then
        return
    end
    
    local portal_pos = portal:GetPosition()
    
    -- Remove moon bonuses from players
    local players = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, {"player"})
    for _, player in ipairs(players) do
        player.moon_realm_bonus = nil
        player:RemoveTag("night_vision")
        
        if player.components.vision then
            player.components.vision:SetGhostVision(false)
        end
    end
    
    -- Remove realm-specific entities
    local realm_entities = TheSim:FindEntities(portal_pos.x, portal_pos.y, portal_pos.z, 25, 
        {"realm_resource", "realm_npc", "realm_creature", "realm_structure"})
    
    for _, entity in ipairs(realm_entities) do
        if entity.realm_type == "moon_realm" then
            entity:Remove()
        end
    end
    
    -- Clean up aura
    if portal.realm_aura and portal.realm_aura:IsValid() then
        portal.realm_aura:Remove()
    end
    
    -- Cancel tasks
    local tasks = {"yin_cultivation_task", "shadow_magic_task", "lunar_cycle_task", "sanity_task", "night_vision_task", "shadow_portal_task"}
    for _, task_name in ipairs(tasks) do
        if portal[task_name] then
            portal[task_name]:Cancel()
            portal[task_name] = nil
        end
    end
end

-- Register realm type
GLOBAL.CULTIVATION_REALMS = GLOBAL.CULTIVATION_REALMS or {}
GLOBAL.CULTIVATION_REALMS["moon_realm"] = {
    data = MOON_REALM,
    generate = GenerateMoonRealm,
    cleanup = CleanupMoonRealm,
}

print("[Tu Tiên Bí Cảnh] Moon Realm system loaded")
