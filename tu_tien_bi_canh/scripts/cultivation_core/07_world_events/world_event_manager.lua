-- World Event Manager
-- Handles seasonal events, PvP events, and special world occurrences

local WORLD_EVENTS = {
    -- Seasonal Events
    spring_awakening = {
        name_key = "spring_awakening",
        description_key = "spring_awakening",
        type = "seasonal",
        season = "spring",
        duration = 3, -- days
        frequency = 30, -- every 30 days
        effects = {
            cultivation_bonus = 1.5,
            spiritual_energy_regen = 1.3,
            herb_growth_rate = 2.0,
        },
    },
    
    summer_trials = {
        name_key = "summer_trials",
        description_key = "summer_trials",
        type = "seasonal",
        season = "summer",
        duration = 2,
        frequency = 25,
        effects = {
            fire_spell_bonus = 1.4,
            heat_resistance = true,
            fire_realm_spawn_rate = 2.0,
        },
    },

    autumn_harvest = {
        name_key = "autumn_harvest",
        description_key = "autumn_harvest",
        type = "seasonal",
        season = "autumn",
        duration = 4,
        frequency = 35,
        effects = {
            alchemy_success_bonus = 1.3,
            herb_yield_bonus = 1.5,
            trading_bonus = 1.2,
        },
    },

    winter_meditation = {
        name_key = "winter_meditation",
        description_key = "winter_meditation",
        type = "seasonal",
        season = "winter",
        duration = 5,
        frequency = 40,
        effects = {
            meditation_bonus = 2.0,
            spiritual_energy_capacity = 1.2,
            ice_spell_bonus = 1.3,
        },
    },
    
    -- Special Events
    blood_moon = {
        name_key = "blood_moon",
        description_key = "blood_moon",
        type = "special",
        duration = 1,
        frequency = 60, -- rare event
        effects = {
            evil_karma_bonus = 2.0,
            dark_spell_bonus = 1.5,
            moon_realm_spawn_rate = 3.0,
            pvp_enabled = true,
        },
    },

    celestial_convergence = {
        name_key = "celestial_convergence",
        description_key = "celestial_convergence",
        type = "special",
        duration = 0.5, -- 12 hours
        frequency = 90, -- very rare
        effects = {
            all_realms_spawn = true,
            cultivation_bonus = 2.0,
            spell_cooldown_reduction = 0.5,
        },
    },

    sect_war = {
        name_key = "sect_war",
        description_key = "sect_war",
        type = "pvp",
        duration = 2,
        frequency = 45,
        effects = {
            pvp_enabled = true,
            sect_point_bonus = 2.0,
            combat_bonus = 1.3,
            ancient_battlefield_spawn_rate = 5.0,
        },
    },

    immortal_ascension = {
        name_key = "immortal_ascension",
        description_key = "immortal_ascension",
        type = "special",
        duration = 1,
        frequency = 120, -- extremely rare
        requirements = {
            min_players_level_7 = 3, -- Need at least 3 level 7+ players
        },
        effects = {
            high_level_bonus = 3.0,
            thunder_realm_spawn_rate = 10.0,
            ultimate_spell_unlock = true,
        },
    },
}

local WorldEventManager = Class(function(self, inst)
    self.inst = inst -- TheWorld
    
    -- Active events
    self.active_events = {}
    
    -- Event history
    self.event_history = {}
    
    -- Event timers
    self.event_timers = {}
    
    -- Last event times
    self.last_event_times = {}
    
    -- Initialize event checking
    self:StartEventChecking()
    
end)

function WorldEventManager:StartEventChecking()
    -- Check for events every game day
    self.event_check_task = self.inst:DoPeriodicTask(TUNING.TOTAL_DAY_TIME, function()
        self:CheckForEvents()
    end)
    
    -- Update active events every minute
    self.event_update_task = self.inst:DoPeriodicTask(60, function()
        self:UpdateActiveEvents()
    end)
end

function WorldEventManager:CheckForEvents()
    local current_time = GetTime()
    local current_season = TheWorld.state.season
    
    for event_id, event_data in pairs(WORLD_EVENTS) do
        if not self:IsEventActive(event_id) then
            if self:ShouldEventTrigger(event_id, event_data, current_time, current_season) then
                self:StartEvent(event_id)
            end
        end
    end
end

function WorldEventManager:ShouldEventTrigger(event_id, event_data, current_time, current_season)
    -- Check frequency
    local last_time = self.last_event_times[event_id] or 0
    local time_since_last = (current_time - last_time) / TUNING.TOTAL_DAY_TIME
    
    if time_since_last < event_data.frequency then
        return false
    end
    
    -- Check season requirement
    if event_data.season and event_data.season ~= current_season then
        return false
    end
    
    -- Check special requirements
    if event_data.requirements then
        if not self:MeetsEventRequirements(event_data.requirements) then
            return false
        end
    end
    
    -- Random chance (base 20% per check if all conditions met)
    local base_chance = 0.2
    
    -- Increase chance based on how long it's been since last event
    local time_bonus = math.min(0.3, (time_since_last - event_data.frequency) * 0.01)
    local total_chance = base_chance + time_bonus
    
    return math.random() < total_chance
end

function WorldEventManager:MeetsEventRequirements(requirements)
    if requirements.min_players_level_7 then
        local high_level_players = 0
        for _, player in ipairs(AllPlayers) do
            if player.components.cultivation and player.components.cultivation.level >= 7 then
                high_level_players = high_level_players + 1
            end
        end
        
        if high_level_players < requirements.min_players_level_7 then
            return false
        end
    end
    
    return true
end

function WorldEventManager:StartEvent(event_id)
    local event_data = WORLD_EVENTS[event_id]
    if not event_data then
        return false
    end
    
    -- Record event start
    local event_info = {
        id = event_id,
        start_time = GetTime(),
        duration = event_data.duration * TUNING.TOTAL_DAY_TIME,
        effects = event_data.effects,
    }
    
    self.active_events[event_id] = event_info
    self.last_event_times[event_id] = GetTime()
    
    -- Apply event effects
    self:ApplyEventEffects(event_id, event_data.effects)
    
    -- Announce event to all players
    self:AnnounceEvent(event_id, event_data)
    
    -- Schedule event end
    self.event_timers[event_id] = self.inst:DoTaskInTime(event_info.duration, function()
        self:EndEvent(event_id)
    end)
    
    -- Record in history
    table.insert(self.event_history, {
        id = event_id,
        name = event_data.name,
        start_time = GetTime(),
        duration = event_data.duration,
    })
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[World Events] Started event:", event_id)
    end
    
    return true
end

function WorldEventManager:EndEvent(event_id)
    local event_info = self.active_events[event_id]
    if not event_info then
        return
    end
    
    -- Remove event effects
    self:RemoveEventEffects(event_id, event_info.effects)
    
    -- Clean up
    self.active_events[event_id] = nil
    
    if self.event_timers[event_id] then
        self.event_timers[event_id]:Cancel()
        self.event_timers[event_id] = nil
    end
    
    -- Announce event end
    local event_data = WORLD_EVENTS[event_id]
    if event_data then
        for _, player in ipairs(AllPlayers) do
            if player.components.talker then
                local event_name = GetWorldEventName(event_id)
                local message = GetMessage("world_event_ended", {event = event_name})
                player.components.talker:Say(message, 3)
            end
        end
    end
    
    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[World Events] Ended event:", event_id)
    end
end

function WorldEventManager:ApplyEventEffects(event_id, effects)
    for _, player in ipairs(AllPlayers) do
        self:ApplyPlayerEventEffects(player, event_id, effects)
    end
    
    -- Apply world-wide effects
    if effects.all_realms_spawn then
        -- Force spawn all realm types
        if TheWorld.components.realm_manager then
            for realm_type, _ in pairs(GLOBAL.CULTIVATION_REALMS or {}) do
                TheWorld.components.realm_manager:ForceSpawnRealm(realm_type)
            end
        end
    end
    
    if effects.pvp_enabled then
        -- Enable global PvP
        for _, player in ipairs(AllPlayers) do
            player:AddTag("pvp_enabled")
        end
    end
end

function WorldEventManager:ApplyPlayerEventEffects(player, event_id, effects)
    if not player or not player:IsValid() then
        return
    end
    
    -- Store event effects on player for removal later
    if not player.active_world_events then
        player.active_world_events = {}
    end
    player.active_world_events[event_id] = effects
    
    -- Apply cultivation bonuses
    if effects.cultivation_bonus and player.components.cultivation then
        player.components.cultivation.world_event_exp_multiplier = effects.cultivation_bonus
    end
    
    if effects.spiritual_energy_regen and player.components.cultivation then
        player.components.cultivation.world_event_energy_multiplier = effects.spiritual_energy_regen
    end
    
    if effects.meditation_bonus and player.components.cultivation then
        player.components.cultivation.world_event_meditation_multiplier = effects.meditation_bonus
    end
    
    -- Apply spell bonuses
    if effects.fire_spell_bonus and player.components.spell_caster then
        player.fire_spell_world_bonus = effects.fire_spell_bonus
    end
    
    if effects.dark_spell_bonus and player.components.spell_caster then
        player.dark_spell_world_bonus = effects.dark_spell_bonus
    end
    
    if effects.spell_cooldown_reduction and player.components.spell_caster then
        player.spell_cooldown_world_reduction = effects.spell_cooldown_reduction
    end
    
    -- Apply karma bonuses
    if effects.evil_karma_bonus and player.components.cultivation then
        player.components.cultivation.world_event_evil_karma_multiplier = effects.evil_karma_bonus
    end
    
    -- Apply combat bonuses
    if effects.combat_bonus and player.components.combat then
        player.components.combat:AddDamageModifier("world_event", effects.combat_bonus)
    end
    
    -- Apply sect bonuses
    if effects.sect_point_bonus and player.components.sect_member then
        player.sect_point_world_multiplier = effects.sect_point_bonus
    end
    
    -- Apply special effects
    if effects.heat_resistance then
        player:AddTag("heat_resistant")
    end
    
    if effects.ultimate_spell_unlock and player.components.cultivation then
        if player.components.cultivation.level >= 7 then
            player.components.cultivation:UnlockSpell("divine_shield")
            player.components.cultivation:UnlockSpell("soul_burn")
        end
    end
end

function WorldEventManager:RemoveEventEffects(event_id, effects)
    for _, player in ipairs(AllPlayers) do
        self:RemovePlayerEventEffects(player, event_id, effects)
    end
    
    -- Remove world-wide effects
    if effects.pvp_enabled then
        for _, player in ipairs(AllPlayers) do
            player:RemoveTag("pvp_enabled")
        end
    end
end

function WorldEventManager:RemovePlayerEventEffects(player, event_id, effects)
    if not player or not player:IsValid() then
        return
    end
    
    -- Remove stored event effects
    if player.active_world_events then
        player.active_world_events[event_id] = nil
    end
    
    -- Remove cultivation bonuses
    if effects.cultivation_bonus and player.components.cultivation then
        player.components.cultivation.world_event_exp_multiplier = nil
    end
    
    if effects.spiritual_energy_regen and player.components.cultivation then
        player.components.cultivation.world_event_energy_multiplier = nil
    end
    
    if effects.meditation_bonus and player.components.cultivation then
        player.components.cultivation.world_event_meditation_multiplier = nil
    end
    
    -- Remove spell bonuses
    if effects.fire_spell_bonus then
        player.fire_spell_world_bonus = nil
    end
    
    if effects.dark_spell_bonus then
        player.dark_spell_world_bonus = nil
    end
    
    if effects.spell_cooldown_reduction then
        player.spell_cooldown_world_reduction = nil
    end
    
    -- Remove karma bonuses
    if effects.evil_karma_bonus and player.components.cultivation then
        player.components.cultivation.world_event_evil_karma_multiplier = nil
    end
    
    -- Remove combat bonuses
    if effects.combat_bonus and player.components.combat then
        player.components.combat:RemoveDamageModifier("world_event")
    end
    
    -- Remove sect bonuses
    if effects.sect_point_bonus then
        player.sect_point_world_multiplier = nil
    end
    
    -- Remove special effects
    if effects.heat_resistance then
        player:RemoveTag("heat_resistant")
    end
end

function WorldEventManager:UpdateActiveEvents()
    local current_time = GetTime()
    
    for event_id, event_info in pairs(self.active_events) do
        local elapsed = current_time - event_info.start_time
        local remaining = event_info.duration - elapsed
        
        -- Check if event should end
        if remaining <= 0 then
            self:EndEvent(event_id)
        end
    end
end

function WorldEventManager:AnnounceEvent(event_id, event_data)
    -- Announce to all players
    for _, player in ipairs(AllPlayers) do
        if player.components.talker then
            local event_name = GetWorldEventName(event_id)
            local message = GetMessage("world_event_started", {event = event_name})
            player.components.talker:Say(message, 5)
        end
    end

    -- Could add more elaborate announcement system here
    local event_name = GetWorldEventName(event_id)
    local event_desc = GetWorldEventDescription(event_id)
    print("[World Event] " .. event_name .. " - " .. event_desc)
end

function WorldEventManager:IsEventActive(event_id)
    return self.active_events[event_id] ~= nil
end

function WorldEventManager:GetActiveEvents()
    return self.active_events
end

function WorldEventManager:GetEventHistory()
    return self.event_history
end

function WorldEventManager:ForceStartEvent(event_id)
    return self:StartEvent(event_id)
end

function WorldEventManager:ForceEndEvent(event_id)
    self:EndEvent(event_id)
end

function WorldEventManager:OnSave()
    return {
        active_events = self.active_events,
        event_history = self.event_history,
        last_event_times = self.last_event_times,
    }
end

function WorldEventManager:OnLoad(data)
    if data then
        self.active_events = data.active_events or {}
        self.event_history = data.event_history or {}
        self.last_event_times = data.last_event_times or {}
        
        -- Restart active events
        for event_id, event_info in pairs(self.active_events) do
            local remaining_time = event_info.duration - (GetTime() - event_info.start_time)
            if remaining_time > 0 then
                -- Reapply effects
                self:ApplyEventEffects(event_id, event_info.effects)
                
                -- Reschedule end
                self.event_timers[event_id] = self.inst:DoTaskInTime(remaining_time, function()
                    self:EndEvent(event_id)
                end)
            else
                -- Event should have ended, clean up
                self.active_events[event_id] = nil
            end
        end
    end
end

function WorldEventManager:OnRemoveEntity()
    -- Clean up tasks
    if self.event_check_task then
        self.event_check_task:Cancel()
    end
    
    if self.event_update_task then
        self.event_update_task:Cancel()
    end
    
    for _, timer in pairs(self.event_timers) do
        if timer then
            timer:Cancel()
        end
    end
end

return WorldEventManager
