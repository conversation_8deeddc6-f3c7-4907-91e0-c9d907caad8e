-- Cultivation UI System
-- Main UI interface for cultivation mod

local CultivationUI = Class(function(self, owner)
    self.owner = owner
    self.is_open = false
    self.current_tab = "cultivation"

    -- UI state
    self.ui_data = {
        cultivation = {},
        spells = {},
        sect = {},
        quests = {},
        alchemy = {},
    }

    -- Initialize UI
    self:Initialize()
end)

function CultivationUI:Initialize()
    -- Set up UI event listeners
    if self.owner then
        self.owner:ListenForEvent("cultivation_level_up", function(inst, data)
            self:RefreshCultivationTab()
        end)

        self.owner:ListenForEvent("spell_unlocked", function(inst, data)
            self:RefreshSpellsTab()
        end)

        self.owner:ListenForEvent("sect_joined", function(inst, data)
            self:RefreshSectTab()
        end)

        self.owner:ListenForEvent("quest_started", function(inst, data)
            self:RefreshQuestsTab()
        end)

        self.owner:ListenForEvent("quest_completed", function(inst, data)
            self:RefreshQuestsTab()
        end)
    end
end

function CultivationUI:Open()
    if self.is_open then
        return
    end

    self.is_open = true
    self:RefreshAllTabs()

    -- Send UI data to client
    self:SendUIData()

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[UI] Cultivation UI opened")
    end
end

function CultivationUI:Close()
    if not self.is_open then
        return
    end

    self.is_open = false

    if TUNING.CULTIVATION_MOD.DEBUG_MODE then
        print("[UI] Cultivation UI closed")
    end
end

function CultivationUI:SwitchTab(tab_name)
    if not self.is_open then
        return
    end

    self.current_tab = tab_name
    self:RefreshCurrentTab()
    self:SendUIData()
end

function CultivationUI:RefreshAllTabs()
    self:RefreshCultivationTab()
    self:RefreshSpellsTab()
    self:RefreshSectTab()
    self:RefreshQuestsTab()
    self:RefreshAlchemyTab()
end

function CultivationUI:RefreshCurrentTab()
    if self.current_tab == "cultivation" then
        self:RefreshCultivationTab()
    elseif self.current_tab == "spells" then
        self:RefreshSpellsTab()
    elseif self.current_tab == "sect" then
        self:RefreshSectTab()
    elseif self.current_tab == "quests" then
        self:RefreshQuestsTab()
    elseif self.current_tab == "alchemy" then
        self:RefreshAlchemyTab()
    end
end

function CultivationUI:RefreshCultivationTab()
    if not self.owner or not self.owner.components.cultivation then
        return
    end

    local cultivation = self.owner.components.cultivation

    self.ui_data.cultivation = {
        level = cultivation.level,
        level_name = cultivation:GetLevelName(),
        experience = cultivation.experience,
        exp_required = cultivation:GetExpRequiredForNextLevel(),
        spiritual_energy = cultivation.spiritual_energy,
        max_spiritual_energy = cultivation.max_spiritual_energy,
        righteous_karma = cultivation.righteous_karma or 0,
        evil_karma = cultivation.evil_karma or 0,
        is_meditating = cultivation.is_meditating,
        meditation_bonus = cultivation:GetMeditationBonus(),
        unlocked_spells = cultivation.unlocked_spells,
        total_spells = cultivation:GetTotalAvailableSpells(),
    }
end

function CultivationUI:RefreshSpellsTab()
    if not self.owner or not self.owner.components.spell_caster then
        return
    end

    local spell_caster = self.owner.components.spell_caster
    local cultivation = self.owner.components.cultivation

    local spells_data = {}

    if cultivation then
        for _, spell_name in ipairs(cultivation.unlocked_spells) do
            local spell_data = spell_caster:GetSpellData(spell_name)
            if spell_data then
                local can_cast, reason = spell_caster:CanCastSpell(spell_name)
                local cooldown_remaining = spell_caster:GetSpellCooldownRemaining(spell_name)

                spells_data[spell_name] = {
                    name = spell_data.name,
                    description = spell_data.description,
                    element = spell_data.element,
                    level_required = spell_data.level_required,
                    spiritual_cost = spell_data.spiritual_cost,
                    cooldown = spell_data.cooldown,
                    can_cast = can_cast,
                    reason = reason,
                    cooldown_remaining = cooldown_remaining,
                }
            end
        end
    end

    self.ui_data.spells = {
        unlocked_spells = spells_data,
        total_unlocked = cultivation and #cultivation.unlocked_spells or 0,
    }
end

function CultivationUI:RefreshSectTab()
    if not self.owner or not self.owner.components.sect_member then
        return
    end

    local sect_member = self.owner.components.sect_member

    self.ui_data.sect = {
        current_sect = sect_member.sect,
        sect_name = sect_member.sect and sect_member:GetSectData() and sect_member:GetSectData().name or "无门无派",
        rank = sect_member.rank,
        rank_name = sect_member:GetRankName(),
        sect_points = sect_member.sect_points,
        available_sects = sect_member:GetAvailableSects(),
        sect_data = sect_member:GetSectData(),
    }
end

function CultivationUI:RefreshQuestsTab()
    if not self.owner or not self.owner.components.quest_manager then
        return
    end

    local quest_manager = self.owner.components.quest_manager

    local active_quests = {}
    for quest_id, quest_info in pairs(quest_manager:GetActiveQuests()) do
        local quest_data = quest_manager:GetQuestData(quest_id)
        local progress = quest_manager:GetQuestProgress(quest_id)

        if quest_data then
            active_quests[quest_id] = {
                name = quest_data.name,
                description = quest_data.description,
                type = quest_data.type,
                objectives = quest_data.objectives,
                progress = progress,
                rewards = quest_data.rewards,
            }
        end
    end

    local available_quests = {}
    for _, quest_id in ipairs(quest_manager:GetAvailableQuests()) do
        local quest_data = quest_manager:GetQuestData(quest_id)
        if quest_data then
            available_quests[quest_id] = {
                name = quest_data.name,
                description = quest_data.description,
                type = quest_data.type,
                requirements = quest_data.requirements,
                rewards = quest_data.rewards,
            }
        end
    end

    self.ui_data.quests = {
        active_quests = active_quests,
        available_quests = available_quests,
        completed_count = table.getn(quest_manager:GetCompletedQuests()),
    }
end

function CultivationUI:RefreshAlchemyTab()
    -- Placeholder for alchemy UI data
    self.ui_data.alchemy = {
        available_recipes = {},
        ingredients = {},
        furnace_level = 1,
    }
end

function CultivationUI:SendUIData()
    if not self.owner then
        return
    end

    -- In a full implementation, this would send data to client UI
    -- For now, we'll just trigger an event
    self.owner:PushEvent("cultivation_ui_update", {
        tab = self.current_tab,
        data = self.ui_data[self.current_tab],
        is_open = self.is_open,
    })
end

function CultivationUI:HandleUIAction(action, data)
    if not self.is_open then
        return
    end

    if action == "switch_tab" then
        self:SwitchTab(data.tab)

    elseif action == "start_meditation" then
        if self.owner.components.cultivation then
            self.owner.components.cultivation:StartMeditation()
        end

    elseif action == "stop_meditation" then
        if self.owner.components.cultivation then
            self.owner.components.cultivation:StopMeditation()
        end

    elseif action == "cast_spell" then
        if self.owner.components.spell_caster and data.spell_name then
            self.owner.components.spell_caster:CastSpell(data.spell_name, data.target, data.position)
        end

    elseif action == "join_sect" then
        if self.owner.components.sect_member and data.sect_name then
            local success, reason = self.owner.components.sect_member:JoinSect(data.sect_name)
            if not success and self.owner.components.talker then
                self.owner.components.talker:Say(reason, 2)
            end
        end

    elseif action == "leave_sect" then
        if self.owner.components.sect_member then
            self.owner.components.sect_member:LeaveSect()
        end

    elseif action == "start_quest" then
        if self.owner.components.quest_manager and data.quest_id then
            local success, reason = self.owner.components.quest_manager:StartQuest(data.quest_id)
            if not success and self.owner.components.talker then
                self.owner.components.talker:Say(reason, 2)
            end
        end

    elseif action == "close_ui" then
        self:Close()
    end

    -- Refresh UI after action
    self:RefreshCurrentTab()
    self:SendUIData()
end

-- Global UI manager
GLOBAL.CULTIVATION_UI_MANAGER = GLOBAL.CULTIVATION_UI_MANAGER or {}

function GLOBAL.OpenCultivationUI(player)
    if not player then
        return
    end

    if not GLOBAL.CULTIVATION_UI_MANAGER[player] then
        GLOBAL.CULTIVATION_UI_MANAGER[player] = CultivationUI(player)
    end

    GLOBAL.CULTIVATION_UI_MANAGER[player]:Open()
end

function GLOBAL.CloseCultivationUI(player)
    if not player or not GLOBAL.CULTIVATION_UI_MANAGER[player] then
        return
    end

    GLOBAL.CULTIVATION_UI_MANAGER[player]:Close()
end

function GLOBAL.HandleCultivationUIAction(player, action, data)
    if not player or not GLOBAL.CULTIVATION_UI_MANAGER[player] then
        return
    end

    GLOBAL.CULTIVATION_UI_MANAGER[player]:HandleUIAction(action, data)
end

print("[Tu Tiên Bí Cảnh] Cultivation UI system loaded")
