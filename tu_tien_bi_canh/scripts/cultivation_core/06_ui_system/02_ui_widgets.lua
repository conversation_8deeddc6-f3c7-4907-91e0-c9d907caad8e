-- UI Widgets for Cultivation Mod
-- Custom widgets for the cultivation interface

local Widget = require "widgets/widget"
local Image = require "widgets/image"
local Text = require "widgets/text"
local Button = require "widgets/button"
local ImageButton = require "widgets/imagebutton"

-- Cultivation Progress Bar Widget
local CultivationProgressBar = Class(Widget, function(self, owner)
    Widget._ctor(self, "CultivationProgressBar")

    self.owner = owner

    -- Background
    self.bg = self:AddChild(Image("images/ui/cultivation_ui/progress_bars.xml", "cultivation_bg.tex"))
    self.bg:SetScale(1, 1)

    -- Fill bar
    self.fill = self:AddChild(Image("images/ui/cultivation_ui/progress_bars.xml", "cultivation_fill.tex"))
    self.fill:SetScale(1, 1)

    -- Text overlay
    self.text = self:AddChild(Text(CHATFONT, 24))
    self.text:SetPosition(0, 0)
    self.text:SetColour(1, 1, 1, 1)

    self:UpdateProgress(0, 100)
end)

function CultivationProgressBar:UpdateProgress(current, max)
    local progress = math.max(0, math.min(1, current / max))

    -- Update fill bar width
    self.fill:SetScale(progress, 1)

    -- Update text
    local level_name = ""
    if self.owner and self.owner.components.cultivation then
        level_name = self.owner.components.cultivation:GetLevelName()
    end

    self.text:SetString(string.format("%s: %d/%d", level_name, current, max))
end

-- Spell Icon Widget
local SpellIcon = Class(Widget, function(self, spell_id, spell_data)
    Widget._ctor(self, "SpellIcon")

    self.spell_id = spell_id
    self.spell_data = spell_data

    -- Icon background
    self.bg = self:AddChild(Image("images/ui/spell_icons/spell_bg.xml", "spell_bg.tex"))
    self.bg:SetScale(1, 1)

    -- Spell icon
    local icon_atlas = self:GetSpellIconAtlas(spell_data.element)
    local icon_tex = self:GetSpellIconTexture(spell_id)
    self.icon = self:AddChild(Image(icon_atlas, icon_tex))
    self.icon:SetScale(0.8, 0.8)

    -- Cooldown overlay
    self.cooldown = self:AddChild(Image("images/ui/spell_icons/cooldown.xml", "cooldown.tex"))
    self.cooldown:SetScale(1, 1)
    self.cooldown:Hide()

    -- Make clickable
    self:SetOnClick(function() self:OnClick() end)
end)

function SpellIcon:GetSpellIconAtlas(element)
    local atlases = {
        metal = "images/ui/spell_icons/metal_spells.xml",
        wood = "images/ui/spell_icons/wood_spells.xml",
        water = "images/ui/spell_icons/water_spells.xml",
        fire = "images/ui/spell_icons/fire_spells.xml",
        earth = "images/ui/spell_icons/earth_spells.xml",
        heaven = "images/ui/spell_icons/heaven_spells.xml",
        yang = "images/ui/spell_icons/ultimate_spells.xml",
        yin = "images/ui/spell_icons/ultimate_spells.xml",
    }
    return atlases[element] or "images/ui/spell_icons/default.xml"
end

function SpellIcon:GetSpellIconTexture(spell_id)
    return spell_id .. ".tex"
end

function SpellIcon:OnClick()
    if self.owner and self.owner.components.spell_caster then
        local can_cast, reason = self.owner.components.spell_caster:CanCastSpell(self.spell_id)
        if can_cast then
            -- Cast spell (would need target selection for some spells)
            self.owner.components.spell_caster:CastSpell(self.spell_id)
        else
            -- Show reason why can't cast
            if self.owner.components.talker then
                self.owner.components.talker:Say(reason, 2)
            end
        end
    end
end

function SpellIcon:UpdateCooldown(remaining, total)
    if remaining > 0 then
        self.cooldown:Show()
        local progress = remaining / total
        self.cooldown:SetScale(1, progress)
    else
        self.cooldown:Hide()
    end
end

print("[Tu Tiên Bí Cảnh] UI widgets loaded")
