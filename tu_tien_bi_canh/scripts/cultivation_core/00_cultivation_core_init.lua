-- Cultivation Core System Initialization
-- This file loads all core cultivation modules

-- Load basic modifications and registrations
modimport("scripts/cultivation_core/01_basic_modifications/00_basic_init.lua")

-- Load component systems
modimport("scripts/cultivation_core/02_components/00_components_init.lua")

-- Load realm generation system
modimport("scripts/cultivation_core/03_realm_generation/00_realm_init.lua")

-- Load spell system
modimport("scripts/cultivation_core/04_spell_system/00_spell_init.lua")

-- Load NPC system
modimport("scripts/cultivation_core/05_npc_system/00_npc_init.lua")

-- Load UI system
modimport("scripts/cultivation_core/06_ui_system/00_ui_init.lua")

print("[Tu Tiên Bí Cảnh] Core systems initialized")
