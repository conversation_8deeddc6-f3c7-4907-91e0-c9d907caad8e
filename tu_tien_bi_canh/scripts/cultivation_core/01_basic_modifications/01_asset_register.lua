-- Asset Registration for Cultivation Mod

-- UI Assets
local ui_assets = {
    "cultivation_ui",
    "cultivation_status",
    "realm_map",
    "spell_book",
    "sect_interface",
}

for _, asset_name in ipairs(ui_assets) do
    table.insert(Assets, Asset("ATLAS", "images/ui/" .. asset_name .. ".xml"))
    table.insert(Assets, Asset("IMAGE", "images/ui/" .. asset_name .. ".tex"))
end

-- Item Icons
local item_icons = {
    -- Cultivation items
    "spiritual_stone",
    "cultivation_pill",
    "heaven_ginseng",
    "cloud_talisman_grass",
    "fire_crystal",
    "moon_flower",
    "thunder_stone",
    "ancient_weapon",
    
    -- Spell components
    "spell_scroll",
    "magic_crystal",
    "elemental_essence",
    
    -- Sect items
    "sect_token",
    "sect_manual",
    "sect_uniform",
}

for _, icon_name in ipairs(item_icons) do
    table.insert(Assets, Asset("ATLAS", "images/inventoryimages/" .. icon_name .. ".xml"))
    table.insert(Assets, Asset("IMAGE", "images/inventoryimages/" .. icon_name .. ".tex"))
end

-- Character Animations
local character_anims = {
    "cultivation_meditate",
    "spell_casting",
    "cultivation_breakthrough",
    "npc_cultivator",
    "realm_portal",
}

for _, anim_name in ipairs(character_anims) do
    table.insert(Assets, Asset("ANIM", "anim/" .. anim_name .. ".zip"))
end

-- Sound Effects
local sound_effects = {
    "cultivation/meditation",
    "cultivation/breakthrough", 
    "cultivation/spell_cast",
    "cultivation/portal_open",
    "cultivation/portal_close",
    "cultivation/spiritual_energy",
    "cultivation/combat_hit",
    "cultivation/npc_dialogue",
}

for _, sound_name in ipairs(sound_effects) do
    table.insert(Assets, Asset("SOUND", "sound/" .. sound_name .. ".fsb"))
end

-- Particle Effects
local fx_assets = {
    "spiritual_energy_fx",
    "portal_fx",
    "spell_fx",
    "breakthrough_fx",
    "meditation_fx",
}

for _, fx_name in ipairs(fx_assets) do
    table.insert(Assets, Asset("ANIM", "anim/fx/" .. fx_name .. ".zip"))
end

-- Register inventory images for crafting UI
local inventory_images = {
    "spiritual_stone",
    "cultivation_pill", 
    "heaven_ginseng",
    "cloud_talisman_grass",
    "fire_crystal",
    "moon_flower",
    "thunder_stone",
    "ancient_weapon",
    "spell_scroll",
    "magic_crystal",
    "elemental_essence",
    "sect_token",
    "sect_manual",
    "sect_uniform",
}

for _, img_name in ipairs(inventory_images) do
    RegisterInventoryItemAtlas("images/inventoryimages/" .. img_name .. ".xml", img_name .. ".tex")
end

print("[Tu Tiên Bí Cảnh] Assets registered:", #inventory_images, "inventory images")
