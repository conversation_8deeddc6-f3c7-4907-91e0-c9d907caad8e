-- Recipe Filter for Cultivation Items

-- Add cultivation tab to crafting menu
AddRecipeFilter({
    name = "CULTIVATION",
    atlas = "images/ui/cultivation_ui.xml",
    image = "cultivation_tab.tex",
    image_size = 64,
    custom_pos = false,
})

-- Add recipes to cultivation tab
for _, recipe in pairs(AllRecipes) do
    if recipe and recipe.name then
        -- Add cultivation items to the cultivation filter
        if string.find(recipe.name, "cultivation") or 
           string.find(recipe.name, "spiritual") or
           string.find(recipe.name, "sect") then
            AddRecipeToFilter(recipe.name, "CULTIVATION")
        end
    end
end

print("[<PERSON> Tiên <PERSON>] Recipe filters configured")
