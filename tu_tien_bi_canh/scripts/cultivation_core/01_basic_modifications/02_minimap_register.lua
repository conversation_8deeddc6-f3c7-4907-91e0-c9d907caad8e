-- Minimap Icon Registration

local minimap_icons = {
    "cultivation_portal",
    "spiritual_node", 
    "sect_building",
    "alchemy_station",
    "meditation_spot",
    "cultivation_shrine",
}

for _, icon_name in ipairs(minimap_icons) do
    table.insert(Assets, Asset("IMAGE", "images/minimap/" .. icon_name .. ".tex"))
    table.insert(Assets, Asset("ATLAS", "images/minimap/" .. icon_name .. ".xml"))
    AddMinimapAtlas("images/minimap/" .. icon_name .. ".xml")
end

print("[<PERSON> T<PERSON>ê<PERSON>] Minimap icons registered:", #minimap_icons)
