-- Action Definitions for Cultivation

-- Meditation action
local CULTIVATE_MEDITATE = AddAction("CULTIVATE_MEDITATE", "Meditate", function(act)
    if act.doer and act.doer.components.cultivation then
        if act.doer.components.cultivation:StartMeditation() then
            return true
        end
    end
    return false
end)

CULTIVATE_MEDITATE.priority = 1

-- Cast spell action
local CAST_SPELL = AddAction("CAST_SPELL", "Cast Spell", function(act)
    if act.doer and act.doer.components.spell_caster and act.invobject then
        local spell_name = act.invobject.spell_name
        if spell_name then
            return act.doer.components.spell_caster:CastSpell(spell_name, act.target, act.pos)
        end
    end
    return false
end)

CAST_SPELL.priority = 1

-- Enter realm action
local ENTER_REALM = AddAction("ENTER_REALM", "Enter Realm", function(act)
    if act.doer and act.target and act.target.components.cultivation_portal then
        act.target.components.cultivation_portal:OnPlayerActivate(act.doer)
        return true
    end
    return false
end)

ENTER_REALM.priority = 2

-- Add action strings
GLOBAL.STRINGS.ACTIONS.CULTIVATE_MEDITATE = "Meditate"
GLOBAL.STRINGS.ACTIONS.CAST_SPELL = "Cast Spell"
GLOBAL.STRINGS.ACTIONS.ENTER_REALM = "Enter Realm"

print("[Tu Tiên Bí Cảnh] Actions defined")
