-- Cultivation Boss Brain - Professional AI System
-- Creates intelligent boss behavior like real game creatures

require("behaviours/wander")
require("behaviours/runaway")
require("behaviours/doaction")
require("behaviours/attackwall")
require("behaviours/panic")
require("behaviours/minperiod")
require("behaviours/leash")

local BrainCommon = require("brains/braincommon")

local CultivationBossBrain = Class(Brain, function(self, inst)
    Brain._ctor(self, inst)
end)

local function GetFaceTargetFn(inst)
    return inst.components.combat.target
end

local function KeepFaceTargetFn(inst, target)
    return inst.components.combat.target == target
end

-- Combat behavior tree
local function ShouldAttack(inst)
    return inst.components.combat:CanTarget(inst.components.combat.target)
        and inst.components.combat:InCooldown() == false
        and not (inst.components.locomotor and inst.components.locomotor:HasDestination())
end

local function GetAttackTarget(inst)
    local target = inst.components.combat.target
    if target and target:IsValid() and inst.components.combat:Can<PERSON>arget(target) then
        return target
    end
    return nil
end

-- Ability usage logic
local function ShouldUseAbility(inst)
    if not inst.cultivation_data or not inst.cultivation_data.abilities then
        return false
    end
    
    local target = inst.components.combat.target
    if not target or not target:IsValid() then
        return false
    end
    
    -- Check if any ability is ready
    local current_time = GetTime()
    for name, ability in pairs(inst.cultivation_data.abilities) do
        if ability and ability.last_used and ability.cooldown then
            if (current_time - ability.last_used) >= ability.cooldown then
                return true
            end
        end
    end
    
    return false
end

local function UseRandomAbility(inst)
    if inst.UseRandomAbility then
        inst:UseRandomAbility()
    end
    return true
end

-- Phase transition logic
local function CheckPhaseTransition(inst)
    if not inst.cultivation_data or not inst.components.health then
        return false
    end
    
    local health_percent = inst.components.health.currenthealth / inst.components.health.maxhealth
    local new_phase = 1
    
    if health_percent <= 0.75 then new_phase = 2 end
    if health_percent <= 0.5 then new_phase = 3 end
    if health_percent <= 0.25 then new_phase = 4 end
    
    if new_phase > inst.cultivation_data.phase then
        inst.cultivation_data.phase = new_phase
        local realms = {
            "金丹期 (Golden Core)", 
            "元婴期 (Nascent Soul)", 
            "化神期 (Soul Formation)", 
            "炼虚期 (Void Refinement)"
        }
        inst.cultivation_data.realm = realms[new_phase] or "Unknown"
        
        -- Phase transition effects
        if inst.OnPhaseTransition then
            inst:OnPhaseTransition(new_phase)
        end
        
        print("[Boss] Phase Transition:", new_phase, "-", inst.cultivation_data.realm)
        return true
    end
    
    return false
end

-- Taunt behavior
local function ShouldTaunt(inst)
    if not inst.cultivation_data then
        return false
    end
    
    local current_time = GetTime()
    local last_taunt = inst.cultivation_data.last_taunt or 0
    
    -- Taunt every 15-30 seconds in combat
    if inst.components.combat.target and (current_time - last_taunt) > (15 + math.random() * 15) then
        return true
    end
    
    return false
end

local function DoTaunt(inst)
    if not inst.cultivation_data then
        return false
    end
    
    local taunts = {
        "区区凡人，也敢挑战本座？",
        "你们的修为太弱了！",
        "感受真正的修仙之力！",
        "本座已修炼千年！",
        "蝼蚁般的存在！"
    }
    
    local taunt = taunts[math.random(#taunts)]
    
    -- Show taunt to nearby players
    local pos = inst:GetPosition()
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 20, {"player"})
    
    for _, player in ipairs(players) do
        if player.components.talker then
            player.components.talker:Say("[古修者]: " .. taunt, 4)
        end
    end
    
    inst.cultivation_data.last_taunt = GetTime()
    return true
end

-- Enrage behavior for low health
local function ShouldEnrage(inst)
    if not inst.components.health or not inst.cultivation_data then
        return false
    end
    
    local health_percent = inst.components.health.currenthealth / inst.components.health.maxhealth
    return health_percent <= 0.25 and not inst.cultivation_data.enraged
end

local function DoEnrage(inst)
    if not inst.cultivation_data then
        return false
    end
    
    inst.cultivation_data.enraged = true
    
    -- Increase speed and damage
    if inst.components.locomotor then
        inst.components.locomotor.walkspeed = inst.components.locomotor.walkspeed * 1.5
        inst.components.locomotor.runspeed = inst.components.locomotor.runspeed * 1.5
    end
    
    if inst.components.combat then
        inst.components.combat.defaultdamage = inst.components.combat.defaultdamage * 1.3
    end
    
    -- Visual effect
    if inst.AnimState then
        inst.AnimState:SetMultColour(1.2, 0.8, 0.8, 1.0)
    end
    
    -- Enrage taunt
    local pos = inst:GetPosition()
    local players = TheSim:FindEntities(pos.x, pos.y, pos.z, 20, {"player"})
    
    for _, player in ipairs(players) do
        if player.components.talker then
            player.components.talker:Say("[古修者]: 既然如此，本座就让你们见识真正的修仙之力！", 5)
        end
    end
    
    print("[Boss] ENRAGED! Phase 4 activated!")
    return true
end

-- Main behavior tree
function CultivationBossBrain:OnStart()
    local root = PriorityNode({
        -- Emergency behaviors
        WhileNode(function() return self.inst.components.health.takingfiredamage end, "OnFire",
            DoAction(self.inst, function() return BufferedAction(self.inst, nil, ACTIONS.STOPFIRE) end)),
            
        -- Phase transition check
        WhileNode(function() return CheckPhaseTransition(self.inst) end, "PhaseTransition",
            DoAction(self.inst, function() return true end)),
            
        -- Enrage check
        WhileNode(function() return ShouldEnrage(self.inst) end, "Enrage",
            DoAction(self.inst, function() return DoEnrage(self.inst) end)),
            
        -- Combat behaviors
        WhileNode(function() return self.inst.components.combat.target end, "HasTarget",
            PriorityNode({
                -- Use abilities
                WhileNode(function() return ShouldUseAbility(self.inst) end, "UseAbility",
                    DoAction(self.inst, function() return UseRandomAbility(self.inst) end)),
                    
                -- Taunt occasionally
                WhileNode(function() return ShouldTaunt(self.inst) end, "Taunt",
                    DoAction(self.inst, function() return DoTaunt(self.inst) end)),
                    
                -- Attack if in range
                WhileNode(function() return ShouldAttack(self.inst) end, "Attack",
                    DoAction(self.inst, function() 
                        return BufferedAction(self.inst, GetAttackTarget(self.inst), ACTIONS.ATTACK) 
                    end)),
                    
                -- Chase target
                ChaseAndAttack(self.inst, 30, 3),
            }, 0.25)),
            
        -- Idle behaviors
        Wander(self.inst, function() return self.inst:GetPosition() end, 10),
    }, 0.25)
    
    self.bt = BT(self.inst, root)
end

function CultivationBossBrain:OnInitializationComplete()
    self.inst.components.combat:SetRetargetFunction(3, function(inst)
        return FindEntity(inst, 15, function(guy)
            return guy:HasTag("player") 
                and inst.components.combat:CanTarget(guy)
                and not guy:HasTag("notarget")
        end)
    end)
end

return CultivationBossBrain
