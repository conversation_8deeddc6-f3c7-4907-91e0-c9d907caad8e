-- Cultivation Assets Registration
-- This file registers all assets needed for the cultivation mod

-- Basic mod assets
table.insert(Assets, Asset("ATLAS", "images/modicon.xml"))
table.insert(Assets, Asset("IMAGE", "images/modicon.tex"))

-- UI Assets
local ui_assets = {
    "cultivation_ui",
    "cultivation_status", 
    "realm_map",
    "spell_book",
    "sect_interface",
    "cultivation_hud",
}

for _, asset_name in ipairs(ui_assets) do
    table.insert(Assets, Asset("ATLAS", "images/ui/" .. asset_name .. ".xml"))
    table.insert(Assets, Asset("IMAGE", "images/ui/" .. asset_name .. ".tex"))
end

-- Inventory item images
local item_images = {
    -- Basic cultivation items
    "spiritual_stone",
    "cultivation_pill",
    "heaven_ginseng", 
    "cloud_talisman_grass",
    "fire_crystal",
    "moon_flower",
    "thunder_stone",
    "ancient_weapon",
    "spiritual_herb",
    "cultivation_manual",
    
    -- Spell components
    "spell_scroll",
    "magic_crystal",
    "elemental_essence",
    "spell_component",
    
    -- Sect items
    "sect_token",
    "sect_manual",
    "sect_uniform",
    "sect_badge",
    
    -- Alchemy items
    "alchemy_furnace",
    "pill_bottle",
    "herb_pouch",
    "alchemy_ingredient",
    
    -- Weapons and armor
    "cultivation_sword",
    "cultivation_robe",
    "cultivation_amulet",
    "cultivation_ring",
}

for _, img_name in ipairs(item_images) do
    table.insert(Assets, Asset("ATLAS", "images/inventoryimages/" .. img_name .. ".xml"))
    table.insert(Assets, Asset("IMAGE", "images/inventoryimages/" .. img_name .. ".tex"))
    
    -- Register for crafting UI
    RegisterInventoryItemAtlas("images/inventoryimages/" .. img_name .. ".xml", img_name .. ".tex")
end

-- Character animations
local character_anims = {
    "cultivation_meditate",
    "spell_casting",
    "cultivation_breakthrough", 
    "npc_cultivator",
    "cultivation_portal",
    "spiritual_energy_gather",
}

for _, anim_name in ipairs(character_anims) do
    table.insert(Assets, Asset("ANIM", "anim/" .. anim_name .. ".zip"))
end

-- Effect animations
local fx_anims = {
    "spiritual_energy_fx",
    "portal_fx", 
    "spell_fx",
    "breakthrough_fx",
    "meditation_fx",
    "cultivation_aura",
    "elemental_fx",
}

for _, fx_name in ipairs(fx_anims) do
    table.insert(Assets, Asset("ANIM", "anim/fx/" .. fx_name .. ".zip"))
end

-- Sound effects
local sound_files = {
    "cultivation/meditation",
    "cultivation/breakthrough",
    "cultivation/spell_cast", 
    "cultivation/portal_open",
    "cultivation/portal_close",
    "cultivation/portal_ambient",
    "cultivation/portal_enter",
    "cultivation/spiritual_energy",
    "cultivation/combat_hit",
    "cultivation/npc_dialogue",
    "cultivation/level_up",
    "cultivation/spell_unlock",
    "cultivation/alchemy_success",
    "cultivation/sect_join",
}

for _, sound_name in ipairs(sound_files) do
    table.insert(Assets, Asset("SOUND", "sound/" .. sound_name .. ".fsb"))
end

-- Minimap icons
local minimap_icons = {
    "cultivation_portal",
    "spiritual_node",
    "sect_building",
    "alchemy_station",
    "meditation_spot",
    "cultivation_shrine",
}

for _, icon_name in ipairs(minimap_icons) do
    table.insert(Assets, Asset("IMAGE", "images/minimap/" .. icon_name .. ".tex"))
    table.insert(Assets, Asset("ATLAS", "images/minimap/" .. icon_name .. ".xml"))
    AddMinimapAtlas("images/minimap/" .. icon_name .. ".xml")
end

print("[Tu Tiên Bí Cảnh] Assets loaded:", #item_images, "items,", #character_anims, "animations,", #sound_files, "sounds")
