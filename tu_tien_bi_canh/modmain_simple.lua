-- Simple Test Version of modmain.lua
-- Use this if the main modmain.lua doesn't work

print("Tu Tien Bi Canh Test Mod Loading...")

-- Basic setup
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})

-- Test function
local function TestModWorking()
    print("✓ Tu Tien Bi Canh mod is working!")
    
    if ThePlayer then
        ThePlayer.components.talker:Say("Tu Tien mod loaded!", 3)
    end
end

-- Add test when player spawns
AddPlayerPostInit(function(inst)
    print("Player spawned - mod is working")
    
    inst:DoTaskInTime(2, function()
        if inst.components.talker then
            inst.components.talker:Say("Tu Tien Bi Canh mod active!", 3)
        end
    end)
end)

-- Console command for testing
GLOBAL.TestTuTien = TestModWorking

print("Tu Tien Bi Canh Test Mod Loaded Successfully!")
