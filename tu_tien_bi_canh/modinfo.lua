-- Tu Tiên <PERSON> (Cultivation Secret Realms) Mod
-- Version 1.0.0
-- Author: <PERSON>n Development Team

name = "Tu Tiên Bí <PERSON> (Cultivation Secret Realms)"
description = [[
版本号：1.0.0

【Tu Tiên Bí Cảnh - Cultivation Secret Realms】

踏入修仙之路，探索神秘秘境！

🌟 核心特色：
• 7种独特秘境随机出现
• 完整修炼体系（8个境界）
• 24种五行法术
• 智能NPC互动系统
• 宗派系统与炼丹系统

🏔️ 秘境类型：
• 天山秘境 - 高山仙境，灵石丰富
• 桃花秘境 - 春意盎然，治愈圣地
• 古战场 - 危险重重，神兵利器
• 地火秘境 - 火焰山脉，炼器圣地
• 阴月秘境 - 神秘夜境，暗系法术
• 雷电秘境 - 雷霆万钧，雷法修炼
• 药王谷 - 百草仙谷，炼丹天堂

⚔️ 修炼境界：
练气期 → 筑基期 → 金丹期 → 元婴期 → 化神期 → 炼虚期 → 合体期 → 大乘期

🔥 法术系统：
• 五行法术：金木水火土
• 天地法术：雷电、重力
• 阴阳法术：生死、净化

加入QQ群获取更多信息和反馈：[待定]

注意：此mod需要所有玩家安装才能正常游戏！
]]

version = "1.0.0"
author = "Tu Tiên Development Team"
forumthread = ""
api_version = 10

-- Compatibility
dst_compatible = true
dont_starve_compatible = false
reign_of_giants_compatible = false
all_clients_require_mod = true

-- Priority (higher number = higher priority)
priority = -1000

-- Icon
icon_atlas = "modicon.xml"
icon = "modicon.tex"

-- Server filter tags
server_filter_tags = {
    "cultivation", "chinese", "mythology", "adventure", "magic"
}

-- Configuration options
configuration_options = {
    {
        name = "language",
        label = "Ngôn Ngữ / Language",
        hover = "Chọn ngôn ngữ hiển thị cho mod / Choose display language for the mod",
        options = {
            {description = "Tiếng Việt", data = "vietnamese"},
            {description = "English", data = "english"},
        },
        default = "vietnamese",
    },
    {
        name = "Title1",
        label = "=== Cài Đặt Cơ Bản ===",
        options = {{description = "", data = ""}},
        default = ""
    },
    {
        name = "realm_spawn_rate",
        label = "秘境出现频率",
        hover = "控制秘境出现的频率",
        options = {
            {description = "很少 (15天一次)", data = 0.5, hover = "降低秘境出现频率"},
            {description = "正常 (10天一次)", data = 1.0, hover = "默认秘境出现频率"},
            {description = "频繁 (7天一次)", data = 1.5, hover = "增加秘境出现频率"},
            {description = "很频繁 (5天一次)", data = 2.0, hover = "大幅增加秘境出现频率"}
        },
        default = 1.0,
    },
    {
        name = "cultivation_speed",
        label = "修炼速度",
        hover = "控制修炼进度的快慢",
        options = {
            {description = "缓慢", data = 0.5, hover = "修炼速度减半"},
            {description = "正常", data = 1.0, hover = "默认修炼速度"},
            {description = "快速", data = 1.5, hover = "修炼速度增加50%"},
            {description = "极快", data = 2.0, hover = "修炼速度翻倍"}
        },
        default = 1.0,
    },
    {
        name = "npc_aggression",
        label = "NPC敌意程度",
        hover = "控制NPC的敌对倾向",
        options = {
            {description = "和平", data = 0.5, hover = "大部分NPC友好"},
            {description = "正常", data = 1.0, hover = "默认NPC态度"},
            {description = "敌对", data = 1.5, hover = "更多敌对NPC"},
            {description = "极度敌对", data = 2.0, hover = "大部分NPC敌对"}
        },
        default = 1.0,
    },
    {
        name = "Title2", 
        label = "=== 高级设置 ===", 
        options = {{description = "", data = ""}}, 
        default = ""
    },
    {
        name = "enable_pvp_realms",
        label = "启用PvP秘境",
        hover = "某些秘境中玩家可以互相攻击",
        options = {
            {description = "启用", data = true, hover = "古战场等秘境启用PvP"},
            {description = "禁用", data = false, hover = "所有秘境都是PvE"}
        },
        default = true,
    },
    {
        name = "realm_difficulty",
        label = "秘境难度",
        hover = "控制秘境内怪物的强度",
        options = {
            {description = "简单", data = 0.7, hover = "怪物较弱"},
            {description = "正常", data = 1.0, hover = "默认难度"},
            {description = "困难", data = 1.3, hover = "怪物较强"},
            {description = "地狱", data = 1.6, hover = "怪物极强"}
        },
        default = 1.0,
    },
    {
        name = "enable_sect_system",
        label = "启用宗派系统",
        hover = "是否启用宗派功能",
        options = {
            {description = "启用", data = true, hover = "完整宗派体验"},
            {description = "禁用", data = false, hover = "关闭宗派功能"}
        },
        default = true,
    },
    {
        name = "debug_mode",
        label = "调试模式",
        hover = "开发者调试选项",
        options = {
            {description = "关闭", data = false, hover = "正常游戏模式"},
            {description = "开启", data = true, hover = "显示调试信息"}
        },
        default = false,
    }
}
