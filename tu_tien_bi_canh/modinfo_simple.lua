-- Simple Test Version of modinfo.lua
-- Use this if the main modinfo.lua doesn't work

name = "Tu Tien Bi Canh Test"
description = "Test version of cultivation mod"
author = "Test"
version = "1.0.0"

forumthread = ""
api_version = 10

dst_compatible = true
dont_starve_compatible = false
reign_of_giants_compatible = false
all_clients_require_mod = true

icon_atlas = "modicon.xml"
icon = "modicon.tex"

server_filter_tags = {"cultivation", "realms", "magic"}

configuration_options = {
    {
        name = "test_mode",
        label = "Test Mode",
        hover = "Enable test mode",
        options = {
            {description = "On", data = true, hover = "Test mode enabled"},
            {description = "Off", data = false, hover = "Test mode disabled"}
        },
        default = true,
    }
}
