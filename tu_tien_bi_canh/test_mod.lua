-- Test Script for Tu Tiên Bí <PERSON> Mod
-- This script can be used to test mod functionality

print("=== Tu Tiên Bí <PERSON>ảnh Mod Test ===")

-- Test 1: Check if mod is loaded
if TUNING.CULTIVATION_MOD then
    print("✓ Mod configuration loaded")
    print("  - Realm spawn rate:", TUNING.CULTIVATION_MOD.REALM_SPAWN_RATE)
    print("  - Cultivation speed:", TUNING.CULTIVATION_MOD.CULTIVATION_SPEED)
    print("  - Debug mode:", TUNING.CULTIVATION_MOD.DEBUG_MODE)
else
    print("✗ Mod configuration not found")
end

-- Test 2: Check cultivation tuning
if TUNING.CULTIVATION then
    print("✓ Cultivation tuning loaded")
    print("  - Levels:", #TUNING.CULTIVATION.LEVELS)
    print("  - Exp requirements:", #TUNING.CULTIVATION.EXP_REQUIREMENTS)
else
    print("✗ Cultivation tuning not found")
end

-- Test 3: Check if prefabs are registered
local test_prefabs = {
    "cultivation_portal",
    "spiritual_stone", 
    "cultivation_pill",
    "heaven_ginseng",
}

print("✓ Testing prefab registration:")
for _, prefab_name in ipairs(test_prefabs) do
    if Prefabs[prefab_name] then
        print("  ✓", prefab_name)
    else
        print("  ✗", prefab_name, "not found")
    end
end

-- Test 4: Check components
local test_components = {
    "cultivation",
    "realm_manager", 
    "spell_caster",
}

print("✓ Testing component registration:")
for _, component_name in ipairs(test_components) do
    -- Components are checked when added to entities
    print("  -", component_name, "(will be tested when added to entity)")
end

-- Test 5: Check actions
local test_actions = {
    "CULTIVATE_MEDITATE",
    "CAST_SPELL",
    "ENTER_REALM",
}

print("✓ Testing action registration:")
for _, action_name in ipairs(test_actions) do
    if ACTIONS[action_name] then
        print("  ✓", action_name)
    else
        print("  ✗", action_name, "not found")
    end
end

print("=== Test Complete ===")

-- Function to spawn test items (for debugging)
function SpawnCultivationTestItems()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    local player_pos = ThePlayer:GetPosition()
    
    -- Spawn some test items
    local items = {
        "spiritual_stone",
        "cultivation_pill", 
        "heaven_ginseng",
    }
    
    for i, item_name in ipairs(items) do
        local item = SpawnPrefab(item_name)
        if item then
            item.Transform:SetPosition(player_pos.x + i, 0, player_pos.z)
            print("Spawned", item_name)
        else
            print("Failed to spawn", item_name)
        end
    end
end

-- Function to test cultivation component
function TestCultivationComponent()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    if ThePlayer.components.cultivation then
        local cult = ThePlayer.components.cultivation
        print("=== Cultivation Component Test ===")
        print("Level:", cult.level, "(" .. cult:GetLevelName() .. ")")
        print("Experience:", cult.experience, "/", cult:GetExpRequiredForNextLevel())
        print("Spiritual Energy:", cult.spiritual_energy, "/", cult.max_spiritual_energy)
        print("Unlocked Spells:", #cult.unlocked_spells)
        
        -- Test adding experience
        cult:AddExperience(10)
        print("Added 10 experience")
        
        -- Test meditation
        if cult:StartMeditation() then
            print("Started meditation")
            ThePlayer:DoTaskInTime(5, function()
                cult:StopMeditation()
                print("Stopped meditation")
            end)
        end
    else
        print("Cultivation component not found on player")
    end
end

-- Function to spawn a test portal
function SpawnTestPortal()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    local player_pos = ThePlayer:GetPosition()
    local portal = SpawnPrefab("cultivation_portal")
    
    if portal then
        portal.Transform:SetPosition(player_pos.x + 5, 0, player_pos.z)
        
        if portal.components.cultivation_portal then
            portal.components.cultivation_portal:SetRealmType("peach_blossom")
            print("Spawned test portal (Peach Blossom Realm)")
        else
            print("Portal spawned but component not found")
        end
    else
        print("Failed to spawn portal")
    end
end

print("Available test functions:")
print("- SpawnCultivationTestItems()")
print("- TestCultivationComponent()")
print("- SpawnTestPortal()")
print("Use these in console for testing")
