-- Test Script for Tu Tiên Bí <PERSON> Mod
-- This script can be used to test mod functionality

print("=== Tu Tiên Bí <PERSON>ảnh Mod Test ===")

-- Test 1: Check if mod is loaded
if TUNING.CULTIVATION_MOD then
    print("✓ Mod configuration loaded")
    print("  - Realm spawn rate:", TUNING.CULTIVATION_MOD.REALM_SPAWN_RATE)
    print("  - Cultivation speed:", TUNING.CULTIVATION_MOD.CULTIVATION_SPEED)
    print("  - Debug mode:", TUNING.CULTIVATION_MOD.DEBUG_MODE)
else
    print("✗ Mod configuration not found")
end

-- Test 2: Check cultivation tuning
if TUNING.CULTIVATION then
    print("✓ Cultivation tuning loaded")
    print("  - Levels:", #TUNING.CULTIVATION.LEVELS)
    print("  - Exp requirements:", #TUNING.CULTIVATION.EXP_REQUIREMENTS)
else
    print("✗ Cultivation tuning not found")
end

-- Test 3: Check if prefabs are registered
local test_prefabs = {
    "cultivation_portal",
    "spiritual_stone", 
    "cultivation_pill",
    "heaven_ginseng",
}

print("✓ Testing prefab registration:")
for _, prefab_name in ipairs(test_prefabs) do
    if Prefabs[prefab_name] then
        print("  ✓", prefab_name)
    else
        print("  ✗", prefab_name, "not found")
    end
end

-- Test 4: Check components
local test_components = {
    "cultivation",
    "realm_manager", 
    "spell_caster",
}

print("✓ Testing component registration:")
for _, component_name in ipairs(test_components) do
    -- Components are checked when added to entities
    print("  -", component_name, "(will be tested when added to entity)")
end

-- Test 5: Check actions
local test_actions = {
    "CULTIVATE_MEDITATE",
    "CAST_SPELL",
    "ENTER_REALM",
}

print("✓ Testing action registration:")
for _, action_name in ipairs(test_actions) do
    if ACTIONS[action_name] then
        print("  ✓", action_name)
    else
        print("  ✗", action_name, "not found")
    end
end

print("=== Test Complete ===")

-- Function to spawn test items (for debugging)
function SpawnCultivationTestItems()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    local player_pos = ThePlayer:GetPosition()
    
    -- Spawn some test items
    local items = {
        "spiritual_stone",
        "cultivation_pill", 
        "heaven_ginseng",
    }
    
    for i, item_name in ipairs(items) do
        local item = SpawnPrefab(item_name)
        if item then
            item.Transform:SetPosition(player_pos.x + i, 0, player_pos.z)
            print("Spawned", item_name)
        else
            print("Failed to spawn", item_name)
        end
    end
end

-- Function to test cultivation component
function TestCultivationComponent()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    if ThePlayer.components.cultivation then
        local cult = ThePlayer.components.cultivation
        print("=== Cultivation Component Test ===")
        print("Level:", cult.level, "(" .. cult:GetLevelName() .. ")")
        print("Experience:", cult.experience, "/", cult:GetExpRequiredForNextLevel())
        print("Spiritual Energy:", cult.spiritual_energy, "/", cult.max_spiritual_energy)
        print("Unlocked Spells:", #cult.unlocked_spells)
        
        -- Test adding experience
        cult:AddExperience(10)
        print("Added 10 experience")
        
        -- Test meditation
        if cult:StartMeditation() then
            print("Started meditation")
            ThePlayer:DoTaskInTime(5, function()
                cult:StopMeditation()
                print("Stopped meditation")
            end)
        end
    else
        print("Cultivation component not found on player")
    end
end

-- Function to spawn a test portal
function SpawnTestPortal()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    local player_pos = ThePlayer:GetPosition()
    local portal = SpawnPrefab("cultivation_portal")
    
    if portal then
        portal.Transform:SetPosition(player_pos.x + 5, 0, player_pos.z)
        
        if portal.components.cultivation_portal then
            portal.components.cultivation_portal:SetRealmType("peach_blossom")
            print("Spawned test portal (Peach Blossom Realm)")
        else
            print("Portal spawned but component not found")
        end
    else
        print("Failed to spawn portal")
    end
end

-- Phase 2 Test Functions

-- Function to test spell system
function TestSpellSystem()
    if not ThePlayer then
        print("No player found")
        return
    end

    if ThePlayer.components.spell_caster then
        local spell_caster = ThePlayer.components.spell_caster
        local all_spells = spell_caster:GetAllSpellData()

        print("=== Spell System Test ===")
        print("Available spells:", table.getn(all_spells))

        for spell_name, spell_data in pairs(all_spells) do
            local can_cast, reason = spell_caster:CanCastSpell(spell_name)
            print(spell_name .. ":", can_cast and "✓" or "✗ " .. reason)
        end

        -- Test casting a basic spell
        if spell_caster:CanCastSpell("metal_spike") then
            print("Testing metal spike cast...")
            spell_caster:CastSpell("metal_spike", nil, ThePlayer:GetPosition())
        end
    else
        print("Spell caster component not found")
    end
end

-- Function to test NPC system
function TestNPCSystem()
    if not ThePlayer then
        print("No player found")
        return
    end

    local player_pos = ThePlayer:GetPosition()

    -- Spawn test NPCs
    local npcs = {"peach_fairy", "flower_spirit"}

    print("=== NPC System Test ===")
    for i, npc_name in ipairs(npcs) do
        local npc = SpawnPrefab(npc_name)
        if npc then
            npc.Transform:SetPosition(player_pos.x + i * 3, 0, player_pos.z)
            print("Spawned", npc_name)

            -- Test NPC components
            if npc.components.npc_cultivator then
                print("  - NPC Cultivator: ✓")
                print("  - Level:", npc.components.npc_cultivator.level)
                print("  - Alignment:", npc.components.npc_cultivator.alignment)
            end

            if npc.components.npc_memory then
                print("  - NPC Memory: ✓")
            end

            if npc.components.npc_dialogue then
                print("  - NPC Dialogue: ✓")
            end
        else
            print("Failed to spawn", npc_name)
        end
    end
end

-- Function to test realm resources
function TestRealmResources()
    if not ThePlayer then
        print("No player found")
        return
    end

    local player_pos = ThePlayer:GetPosition()

    -- Spawn realm resources
    local resources = {
        "peach_blossom_flower",
        "spring_water",
        "healing_dew",
        "butterfly_essence",
        "heaven_ginseng",
        "mountain_crystal",
    }

    print("=== Realm Resources Test ===")
    for i, resource_name in ipairs(resources) do
        local resource = SpawnPrefab(resource_name)
        if resource then
            resource.Transform:SetPosition(player_pos.x + (i % 3) * 2, 0, player_pos.z + math.floor(i / 3) * 2)
            print("Spawned", resource_name)

            -- Test resource properties
            if resource.components.edible then
                print("  - Edible: ✓")
            end
            if resource.components.alchemy_ingredient then
                print("  - Alchemy ingredient: ✓")
            end
        else
            print("Failed to spawn", resource_name)
        end
    end
end

-- Function to test alchemy system
function TestAlchemySystem()
    print("=== Alchemy System Test ===")

    -- Spawn alchemy furnace
    local furnace = SpawnPrefab("alchemy_furnace")
    if furnace then
        local player_pos = ThePlayer:GetPosition()
        furnace.Transform:SetPosition(player_pos.x + 5, 0, player_pos.z)
        print("Spawned alchemy furnace")

        if furnace.components.alchemy_furnace then
            local alchemy = furnace.components.alchemy_furnace

            -- Add test ingredients
            alchemy:AddIngredient("peach_blossom_flower", 5)
            alchemy:AddIngredient("spiritual_stone", 10)
            alchemy:AddIngredient("spring_water", 3)

            print("Added test ingredients")

            -- Test recipe availability
            local recipes = alchemy:GetAvailableRecipes()
            print("Available recipes:", #recipes)
            for _, recipe in ipairs(recipes) do
                print("  -", recipe)
            end

            -- Try crafting
            if #recipes > 0 then
                local success, reason = alchemy:StartCrafting(recipes[1], ThePlayer)
                print("Crafting attempt:", success and "Started" or reason)
            end
        end
    else
        print("Failed to spawn alchemy furnace")
    end
end

-- Function to test realm generation
function TestRealmGeneration()
    if not ThePlayer then
        print("No player found")
        return
    end

    print("=== Realm Generation Test ===")

    -- Test both realm types
    local realm_types = {"peach_blossom", "heavenly_mountain"}

    for i, realm_type in ipairs(realm_types) do
        local portal = SpawnPrefab("cultivation_portal")
        if portal then
            local player_pos = ThePlayer:GetPosition()
            portal.Transform:SetPosition(player_pos.x + i * 10, 0, player_pos.z + 10)

            if portal.components.cultivation_portal then
                portal.components.cultivation_portal:SetRealmType(realm_type)
                print("Created", realm_type, "realm portal")

                -- Check if realm data exists
                if GLOBAL.CULTIVATION_REALMS and GLOBAL.CULTIVATION_REALMS[realm_type] then
                    print("  - Realm data: ✓")
                    print("  - Display name:", GLOBAL.CULTIVATION_REALMS[realm_type].data.display_name)
                else
                    print("  - Realm data: ✗")
                end
            end
        else
            print("Failed to spawn portal for", realm_type)
        end
    end
end

-- Phase 3 Test Functions

-- Function to test sect system
function TestSectSystem()
    if not ThePlayer then
        print("No player found")
        return
    end

    if ThePlayer.components.sect_member then
        local sect_member = ThePlayer.components.sect_member

        print("=== Sect System Test ===")
        print("Current sect:", sect_member.sect or "None")
        print("Current rank:", sect_member:GetRankName())
        print("Sect points:", sect_member.sect_points)

        local available_sects = sect_member:GetAvailableSects()
        print("Available sects:", #available_sects)
        for _, sect in ipairs(available_sects) do
            print("  -", sect)
        end

        -- Test joining a sect
        if not sect_member.sect and #available_sects > 0 then
            local success, reason = sect_member:JoinSect(available_sects[1])
            print("Join attempt:", success and "Success" or reason)
        end
    else
        print("Sect member component not found")
    end
end

-- Function to test quest system
function TestQuestSystem()
    if not ThePlayer then
        print("No player found")
        return
    end

    if ThePlayer.components.quest_manager then
        local quest_manager = ThePlayer.components.quest_manager

        print("=== Quest System Test ===")

        local active_quests = quest_manager:GetActiveQuests()
        print("Active quests:", table.getn(active_quests))
        for quest_id, _ in pairs(active_quests) do
            local quest_data = quest_manager:GetQuestData(quest_id)
            print("  -", quest_data.name)
        end

        local available_quests = quest_manager:GetAvailableQuests()
        print("Available quests:", #available_quests)
        for _, quest_id in ipairs(available_quests) do
            local quest_data = quest_manager:GetQuestData(quest_id)
            print("  -", quest_data.name)
        end

        local completed_quests = quest_manager:GetCompletedQuests()
        print("Completed quests:", table.getn(completed_quests))

        -- Test starting a quest
        if #available_quests > 0 then
            local success, reason = quest_manager:StartQuest(available_quests[1])
            print("Start quest attempt:", success and "Success" or reason)
        end
    else
        print("Quest manager component not found")
    end
end

-- Function to test world events
function TestWorldEvents()
    if not TheWorld then
        print("No world found")
        return
    end

    if TheWorld.components.world_event_manager then
        local event_manager = TheWorld.components.world_event_manager

        print("=== World Events Test ===")

        local active_events = event_manager:GetActiveEvents()
        print("Active events:", table.getn(active_events))
        for event_id, event_info in pairs(active_events) do
            print("  -", event_id)
        end

        local event_history = event_manager:GetEventHistory()
        print("Event history:", #event_history)

        -- Test forcing an event
        local test_events = {"spring_awakening", "blood_moon", "sect_war"}
        local test_event = test_events[math.random(#test_events)]

        print("Forcing event:", test_event)
        event_manager:ForceStartEvent(test_event)
    else
        print("World event manager component not found")
    end
end

-- Function to test all realms
function TestAllRealms()
    if not ThePlayer then
        print("No player found")
        return
    end

    print("=== All Realms Test ===")

    local realm_types = {
        "peach_blossom",
        "heavenly_mountain",
        "ancient_battlefield",
        "fire_realm",
        "moon_realm",
        "thunder_realm",
        "medicine_valley"
    }

    local player_pos = ThePlayer:GetPosition()

    for i, realm_type in ipairs(realm_types) do
        local portal = SpawnPrefab("cultivation_portal")
        if portal then
            local angle = (i / #realm_types) * 2 * PI
            local dist = 15
            local x = player_pos.x + math.cos(angle) * dist
            local z = player_pos.z + math.sin(angle) * dist

            portal.Transform:SetPosition(x, 0, z)

            if portal.components.cultivation_portal then
                portal.components.cultivation_portal:SetRealmType(realm_type)
                print("Created", realm_type, "portal")

                -- Check if realm data exists
                if GLOBAL.CULTIVATION_REALMS and GLOBAL.CULTIVATION_REALMS[realm_type] then
                    print("  - Realm data: ✓")
                    print("  - Display name:", GLOBAL.CULTIVATION_REALMS[realm_type].data.display_name)
                else
                    print("  - Realm data: ✗")
                end
            end
        else
            print("Failed to spawn portal for", realm_type)
        end
    end
end

-- Function to test UI system
function TestUISystem()
    if not ThePlayer then
        print("No player found")
        return
    end

    print("=== UI System Test ===")

    -- Test opening cultivation UI
    if GLOBAL.OpenCultivationUI then
        GLOBAL.OpenCultivationUI(ThePlayer)
        print("Opened cultivation UI")

        -- Test UI actions
        if GLOBAL.HandleCultivationUIAction then
            GLOBAL.HandleCultivationUIAction(ThePlayer, "switch_tab", {tab = "spells"})
            print("Switched to spells tab")

            GLOBAL.HandleCultivationUIAction(ThePlayer, "switch_tab", {tab = "sect"})
            print("Switched to sect tab")

            GLOBAL.HandleCultivationUIAction(ThePlayer, "switch_tab", {tab = "quests"})
            print("Switched to quests tab")
        end
    else
        print("UI system not found")
    end
end

-- Function to test localization system
function TestLocalization()
    print("=== Localization System Test ===")

    -- Test cultivation level names
    print("Cultivation Levels:")
    for i = 1, 8 do
        local level_name = GetCultivationLevelName(i)
        print("  Level", i .. ":", level_name)
    end

    -- Test spell names
    print("\nSpell Names:")
    local test_spells = {"metal_spike", "fire_ball", "lightning_strike", "divine_shield"}
    for _, spell in ipairs(test_spells) do
        local spell_name = GetSpellName(spell)
        local spell_desc = GetSpellDescription(spell)
        print("  " .. spell .. ":", spell_name)
        print("    Description:", spell_desc)
    end

    -- Test sect names
    print("\nSect Names:")
    local test_sects = {"heavenly_sword_sect", "fire_cloud_sect", "blood_moon_sect", "wandering_immortals"}
    for _, sect in ipairs(test_sects) do
        local sect_name = GetSectName(sect)
        local sect_desc = GetSectDescription(sect)
        print("  " .. sect .. ":", sect_name)
        print("    Description:", sect_desc)
    end

    -- Test realm names
    print("\nRealm Names:")
    local test_realms = {"peach_blossom", "heavenly_mountain", "ancient_battlefield", "fire_realm"}
    for _, realm in ipairs(test_realms) do
        local realm_name = GetRealmName(realm)
        local realm_desc = GetRealmDescription(realm)
        print("  " .. realm .. ":", realm_name)
        print("    Description:", realm_desc)
    end

    -- Test messages
    print("\nMessages:")
    local test_messages = {"meditation_start", "level_up", "spell_on_cooldown", "insufficient_spiritual_energy"}
    for _, msg in ipairs(test_messages) do
        local message = GetMessage(msg, {level = "Test Level", spell = "Test Spell"})
        print("  " .. msg .. ":", message)
    end

    print("\n=== Localization Test Complete ===")
end

-- Comprehensive test function
function TestEverything()
    print("=== COMPREHENSIVE MOD TEST ===")

    TestLocalization()
    print("")

    TestCultivationComponent()
    print("")

    TestSpellSystem()
    print("")

    TestSectSystem()
    print("")

    TestQuestSystem()
    print("")

    TestAlchemySystem()
    print("")

    TestWorldEvents()
    print("")

    TestUISystem()
    print("")

    print("=== TEST COMPLETE ===")
end

print("Available test functions:")
print("=== Phase 1 Functions ===")
print("- SpawnCultivationTestItems()")
print("- TestCultivationComponent()")
print("- SpawnTestPortal()")
print("=== Phase 2 Functions ===")
print("- TestSpellSystem()")
print("- TestNPCSystem()")
print("- TestRealmResources()")
print("- TestAlchemySystem()")
print("- TestRealmGeneration()")
print("=== Phase 3 Functions ===")
print("- TestSectSystem()")
print("- TestQuestSystem()")
print("- TestWorldEvents()")
print("- TestAllRealms()")
print("- TestUISystem()")
print("=== Localization Test ===")
print("- TestLocalization()")
print("=== Comprehensive Test ===")
print("- TestEverything()")
print("=== UI Test ===")
print("- TestUI()")
print("=== Realm Test ===")
print("- TestRealms()")
print("- SpawnTestPortal()")
print("- TestRealmContent()")
print("Use these in console for testing")

-- Test UI functionality
function TestUI()
    print("=== UI SYSTEM TEST ===")

    local player = ThePlayer
    if not player then
        print("✗ No player found")
        return
    end

    -- Test UI component
    if player.components.cultivation_ui then
        print("✓ UI component found")

        -- Test opening UI
        print("Testing UI open...")
        player.components.cultivation_ui:OpenUI()

        -- Test closing UI after 3 seconds
        player:DoTaskInTime(3, function()
            print("Testing UI close...")
            player.components.cultivation_ui:CloseUI()
        end)

    else
        print("✗ UI component not found")
    end

    print("=== UI TEST COMPLETE ===")
end

-- Test realm system functionality
function TestRealms()
    print("=== REALM SYSTEM TEST ===")

    local player = ThePlayer
    if not player then
        print("✗ No player found")
        return
    end

    -- Test realm manager
    if TheWorld.components.realm_manager then
        print("✓ Realm manager found")

        -- Force spawn a test portal
        print("Spawning test portal...")
        SpawnTestPortal()

    else
        print("✗ Realm manager not found")
    end

    print("=== REALM TEST COMPLETE ===")
end

-- Spawn a test portal near player
function SpawnTestPortal()
    local player = ThePlayer
    if not player then
        print("No player found")
        return
    end

    local pos = player:GetPosition()
    local offset_pos = pos + Vector3(5, 0, 5)

    -- Spawn peach blossom portal for testing
    local portal = SpawnPrefab("cultivation_portal")
    if portal then
        portal.Transform:SetPosition(offset_pos:Get())

        -- Set it as peach blossom realm
        if portal.components.cultivation_portal then
            portal.components.cultivation_portal:SetRealmType("peach_blossom")
            portal.components.cultivation_portal:Activate()
        end

        print("✓ Test portal spawned at", offset_pos)
        print("Right-click the portal to enter!")

        return portal
    else
        print("✗ Failed to spawn portal")
    end
end

-- Test realm content generation
function TestRealmContent()
    print("=== REALM CONTENT TEST ===")

    local player = ThePlayer
    if not player then
        print("No player found")
        return
    end

    -- Spawn test items around player
    local pos = player:GetPosition()
    local test_items = {
        "peach_blossom_flower",
        "spring_water",
        "healing_dew",
        "spiritual_stone",
        "heaven_ginseng"
    }

    for i, item_name in ipairs(test_items) do
        local angle = (i / #test_items) * 2 * math.pi
        local dist = 3
        local x = pos.x + math.cos(angle) * dist
        local z = pos.z + math.sin(angle) * dist

        local item = SpawnPrefab(item_name)
        if item then
            item.Transform:SetPosition(x, 0, z)
            print("✓ Spawned", item_name)
        else
            print("✗ Failed to spawn", item_name)
        end
    end

    print("=== REALM CONTENT TEST COMPLETE ===")
end

-- Quick realm entry test
function QuickRealmTest()
    print("=== QUICK REALM TEST ===")

    -- Spawn portal and enter immediately
    local portal = SpawnTestPortal()
    if portal then
        local player = ThePlayer
        if player then
            -- Wait a moment then auto-enter
            player:DoTaskInTime(2, function()
                if portal.components.cultivation_portal then
                    portal.components.cultivation_portal:OnPlayerActivate(player)
                    print("✓ Auto-entered realm")
                end
            end)
        end
    end

    print("=== QUICK REALM TEST COMPLETE ===")
end
