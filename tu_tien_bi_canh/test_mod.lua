-- Test Script for Tu Tiên Bí <PERSON> Mod
-- This script can be used to test mod functionality

print("=== Tu Tiên Bí <PERSON>ảnh Mod Test ===")

-- Test 1: Check if mod is loaded
if TUNING.CULTIVATION_MOD then
    print("✓ Mod configuration loaded")
    print("  - Realm spawn rate:", TUNING.CULTIVATION_MOD.REALM_SPAWN_RATE)
    print("  - Cultivation speed:", TUNING.CULTIVATION_MOD.CULTIVATION_SPEED)
    print("  - Debug mode:", TUNING.CULTIVATION_MOD.DEBUG_MODE)
else
    print("✗ Mod configuration not found")
end

-- Test 2: Check cultivation tuning
if TUNING.CULTIVATION then
    print("✓ Cultivation tuning loaded")
    print("  - Levels:", #TUNING.CULTIVATION.LEVELS)
    print("  - Exp requirements:", #TUNING.CULTIVATION.EXP_REQUIREMENTS)
else
    print("✗ Cultivation tuning not found")
end

-- Test 3: Check if prefabs are registered
local test_prefabs = {
    "cultivation_portal",
    "spiritual_stone", 
    "cultivation_pill",
    "heaven_ginseng",
}

print("✓ Testing prefab registration:")
for _, prefab_name in ipairs(test_prefabs) do
    if Prefabs[prefab_name] then
        print("  ✓", prefab_name)
    else
        print("  ✗", prefab_name, "not found")
    end
end

-- Test 4: Check components
local test_components = {
    "cultivation",
    "realm_manager", 
    "spell_caster",
}

print("✓ Testing component registration:")
for _, component_name in ipairs(test_components) do
    -- Components are checked when added to entities
    print("  -", component_name, "(will be tested when added to entity)")
end

-- Test 5: Check actions
local test_actions = {
    "CULTIVATE_MEDITATE",
    "CAST_SPELL",
    "ENTER_REALM",
}

print("✓ Testing action registration:")
for _, action_name in ipairs(test_actions) do
    if ACTIONS[action_name] then
        print("  ✓", action_name)
    else
        print("  ✗", action_name, "not found")
    end
end

print("=== Test Complete ===")

-- Function to spawn test items (for debugging)
function SpawnCultivationTestItems()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    local player_pos = ThePlayer:GetPosition()
    
    -- Spawn some test items
    local items = {
        "spiritual_stone",
        "cultivation_pill", 
        "heaven_ginseng",
    }
    
    for i, item_name in ipairs(items) do
        local item = SpawnPrefab(item_name)
        if item then
            item.Transform:SetPosition(player_pos.x + i, 0, player_pos.z)
            print("Spawned", item_name)
        else
            print("Failed to spawn", item_name)
        end
    end
end

-- Function to test cultivation component
function TestCultivationComponent()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    if ThePlayer.components.cultivation then
        local cult = ThePlayer.components.cultivation
        print("=== Cultivation Component Test ===")
        print("Level:", cult.level, "(" .. cult:GetLevelName() .. ")")
        print("Experience:", cult.experience, "/", cult:GetExpRequiredForNextLevel())
        print("Spiritual Energy:", cult.spiritual_energy, "/", cult.max_spiritual_energy)
        print("Unlocked Spells:", #cult.unlocked_spells)
        
        -- Test adding experience
        cult:AddExperience(10)
        print("Added 10 experience")
        
        -- Test meditation
        if cult:StartMeditation() then
            print("Started meditation")
            ThePlayer:DoTaskInTime(5, function()
                cult:StopMeditation()
                print("Stopped meditation")
            end)
        end
    else
        print("Cultivation component not found on player")
    end
end

-- Function to spawn a test portal
function SpawnTestPortal()
    if not ThePlayer then
        print("No player found")
        return
    end
    
    local player_pos = ThePlayer:GetPosition()
    local portal = SpawnPrefab("cultivation_portal")
    
    if portal then
        portal.Transform:SetPosition(player_pos.x + 5, 0, player_pos.z)
        
        if portal.components.cultivation_portal then
            portal.components.cultivation_portal:SetRealmType("peach_blossom")
            print("Spawned test portal (Peach Blossom Realm)")
        else
            print("Portal spawned but component not found")
        end
    else
        print("Failed to spawn portal")
    end
end

-- Phase 2 Test Functions

-- Function to test spell system
function TestSpellSystem()
    if not ThePlayer then
        print("No player found")
        return
    end

    if ThePlayer.components.spell_caster then
        local spell_caster = ThePlayer.components.spell_caster
        local all_spells = spell_caster:GetAllSpellData()

        print("=== Spell System Test ===")
        print("Available spells:", table.getn(all_spells))

        for spell_name, spell_data in pairs(all_spells) do
            local can_cast, reason = spell_caster:CanCastSpell(spell_name)
            print(spell_name .. ":", can_cast and "✓" or "✗ " .. reason)
        end

        -- Test casting a basic spell
        if spell_caster:CanCastSpell("metal_spike") then
            print("Testing metal spike cast...")
            spell_caster:CastSpell("metal_spike", nil, ThePlayer:GetPosition())
        end
    else
        print("Spell caster component not found")
    end
end

-- Function to test NPC system
function TestNPCSystem()
    if not ThePlayer then
        print("No player found")
        return
    end

    local player_pos = ThePlayer:GetPosition()

    -- Spawn test NPCs
    local npcs = {"peach_fairy", "flower_spirit"}

    print("=== NPC System Test ===")
    for i, npc_name in ipairs(npcs) do
        local npc = SpawnPrefab(npc_name)
        if npc then
            npc.Transform:SetPosition(player_pos.x + i * 3, 0, player_pos.z)
            print("Spawned", npc_name)

            -- Test NPC components
            if npc.components.npc_cultivator then
                print("  - NPC Cultivator: ✓")
                print("  - Level:", npc.components.npc_cultivator.level)
                print("  - Alignment:", npc.components.npc_cultivator.alignment)
            end

            if npc.components.npc_memory then
                print("  - NPC Memory: ✓")
            end

            if npc.components.npc_dialogue then
                print("  - NPC Dialogue: ✓")
            end
        else
            print("Failed to spawn", npc_name)
        end
    end
end

-- Function to test realm resources
function TestRealmResources()
    if not ThePlayer then
        print("No player found")
        return
    end

    local player_pos = ThePlayer:GetPosition()

    -- Spawn realm resources
    local resources = {
        "peach_blossom_flower",
        "spring_water",
        "healing_dew",
        "butterfly_essence",
        "heaven_ginseng",
        "mountain_crystal",
    }

    print("=== Realm Resources Test ===")
    for i, resource_name in ipairs(resources) do
        local resource = SpawnPrefab(resource_name)
        if resource then
            resource.Transform:SetPosition(player_pos.x + (i % 3) * 2, 0, player_pos.z + math.floor(i / 3) * 2)
            print("Spawned", resource_name)

            -- Test resource properties
            if resource.components.edible then
                print("  - Edible: ✓")
            end
            if resource.components.alchemy_ingredient then
                print("  - Alchemy ingredient: ✓")
            end
        else
            print("Failed to spawn", resource_name)
        end
    end
end

-- Function to test alchemy system
function TestAlchemySystem()
    print("=== Alchemy System Test ===")

    -- Spawn alchemy furnace
    local furnace = SpawnPrefab("alchemy_furnace")
    if furnace then
        local player_pos = ThePlayer:GetPosition()
        furnace.Transform:SetPosition(player_pos.x + 5, 0, player_pos.z)
        print("Spawned alchemy furnace")

        if furnace.components.alchemy_furnace then
            local alchemy = furnace.components.alchemy_furnace

            -- Add test ingredients
            alchemy:AddIngredient("peach_blossom_flower", 5)
            alchemy:AddIngredient("spiritual_stone", 10)
            alchemy:AddIngredient("spring_water", 3)

            print("Added test ingredients")

            -- Test recipe availability
            local recipes = alchemy:GetAvailableRecipes()
            print("Available recipes:", #recipes)
            for _, recipe in ipairs(recipes) do
                print("  -", recipe)
            end

            -- Try crafting
            if #recipes > 0 then
                local success, reason = alchemy:StartCrafting(recipes[1], ThePlayer)
                print("Crafting attempt:", success and "Started" or reason)
            end
        end
    else
        print("Failed to spawn alchemy furnace")
    end
end

-- Function to test realm generation
function TestRealmGeneration()
    if not ThePlayer then
        print("No player found")
        return
    end

    print("=== Realm Generation Test ===")

    -- Test both realm types
    local realm_types = {"peach_blossom", "heavenly_mountain"}

    for i, realm_type in ipairs(realm_types) do
        local portal = SpawnPrefab("cultivation_portal")
        if portal then
            local player_pos = ThePlayer:GetPosition()
            portal.Transform:SetPosition(player_pos.x + i * 10, 0, player_pos.z + 10)

            if portal.components.cultivation_portal then
                portal.components.cultivation_portal:SetRealmType(realm_type)
                print("Created", realm_type, "realm portal")

                -- Check if realm data exists
                if GLOBAL.CULTIVATION_REALMS and GLOBAL.CULTIVATION_REALMS[realm_type] then
                    print("  - Realm data: ✓")
                    print("  - Display name:", GLOBAL.CULTIVATION_REALMS[realm_type].data.display_name)
                else
                    print("  - Realm data: ✗")
                end
            end
        else
            print("Failed to spawn portal for", realm_type)
        end
    end
end

print("Available test functions:")
print("- SpawnCultivationTestItems()")
print("- TestCultivationComponent()")
print("- SpawnTestPortal()")
print("=== Phase 2 Functions ===")
print("- TestSpellSystem()")
print("- TestNPCSystem()")
print("- TestRealmResources()")
print("- TestAlchemySystem()")
print("- TestRealmGeneration()")
print("Use these in console for testing")
