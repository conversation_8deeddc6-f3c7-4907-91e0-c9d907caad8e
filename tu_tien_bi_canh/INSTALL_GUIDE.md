# Tu Tiên <PERSON> - Installation & Troubleshooting Guide

## 📁 **CORRECT INSTALLATION**

### **Step 1: Locate DST Mods Folder**

**Windows:**
```
C:\Program Files (x86)\Steam\steamapps\common\Don't Starve Together\mods\
```

**Mac:**
```
~/Library/Application Support/Steam/steamapps/common/Don't Starve Together/mods/
```

**Linux:**
```
~/.steam/steam/steamapps/common/Don't Starve Together/mods/
```

### **Step 2: Copy Mod Folder**

Copy the entire `tu_tien_bi_canh` folder to the mods directory:

```
Don't Starve Together/mods/
└── tu_tien_bi_canh/          ← This folder
    ├── modinfo.lua           ← REQUIRED
    ├── modmain.lua           ← REQUIRED  
    ├── scripts/
    ├── images/
    ├── test_mod.lua
    └── [other files]
```

### **Step 3: Enable Mod in Game**

1. Launch Don't Starve Together
2. Go to **"Mods"** from main menu
3. Look for **"Tu Tiên <PERSON> (Cultivation Secret Realms)"**
4. Click **"Enable"**
5. Click **"Apply"**

---

## 🔧 **TROUBLESHOOTING**

### **❌ Problem: Mod doesn't appear in mod list**

#### **Solution 1: Check File Permissions**
- Make sure the folder is not read-only
- On Windows: Right-click folder → Properties → Uncheck "Read-only"

#### **Solution 2: Check modinfo.lua syntax**
```lua
-- Run this in DST console to check for syntax errors
dofile("mods/tu_tien_bi_canh/modinfo.lua")
```

#### **Solution 3: Restart Steam**
- Close Steam completely
- Restart Steam
- Launch DST again

#### **Solution 4: Check DST Version**
- This mod requires **Don't Starve Together** (not single-player Don't Starve)
- Make sure you have the latest version

### **❌ Problem: Mod appears but won't enable**

#### **Check Console for Errors**
1. Enable mod
2. Press `~` to open console
3. Look for red error messages
4. Common errors:

```
-- Syntax error in Lua files
[ERROR] modmain.lua:XX: syntax error

-- Missing dependencies  
[ERROR] Could not find required file

-- Asset loading issues
[ERROR] Failed to load texture
```

### **❌ Problem: Mod enables but doesn't work**

#### **Test Basic Functionality**
```lua
-- In DST console (press ~)
dofile("mods/tu_tien_bi_canh/test_mod.lua")
TestEverything()
```

#### **Check Components**
```lua
-- Check if player has cultivation components
print(ThePlayer.components.cultivation)
print(ThePlayer.components.spell_caster)
```

---

## 🧪 **TESTING CHECKLIST**

### **✅ Basic Installation Test**
- [ ] Mod appears in mod list
- [ ] Mod can be enabled without errors
- [ ] No red errors in console when loading world

### **✅ Core Functionality Test**
```lua
-- Run these in console
dofile("mods/tu_tien_bi_canh/test_mod.lua")

-- Test 1: Basic systems
TestEverything()

-- Test 2: Realm system  
TestRealms()

-- Test 3: Spawn test portal
SpawnTestPortal()
```

### **✅ Expected Results**
- Player has cultivation level display
- Can spawn test portal
- Portal can be entered
- Resources spawn in realm
- No error messages

---

## 🚨 **COMMON ISSUES & FIXES**

### **Issue 1: "Mod not compatible"**
**Cause**: DST version mismatch
**Fix**: Update DST to latest version

### **Issue 2: "Failed to load mod"**
**Cause**: Syntax error in Lua files
**Fix**: Check console for specific error line

### **Issue 3: "Missing textures"**
**Cause**: Image files not found
**Fix**: This is expected - visual assets not generated yet

### **Issue 4: "Components not found"**
**Cause**: Component loading failed
**Fix**: Check modmain.lua for component initialization

---

## 📋 **MANUAL VERIFICATION**

### **Check 1: Files Exist**
Verify these files exist:
```
tu_tien_bi_canh/
├── modinfo.lua          ✓ Required
├── modmain.lua          ✓ Required
├── scripts/
│   ├── cultivation_core/
│   ├── prefabs/
│   └── localization/
└── test_mod.lua         ✓ For testing
```

### **Check 2: modinfo.lua Content**
Should start with:
```lua
name = "Tu Tiên Bí Cảnh (Cultivation Secret Realms)"
description = [[...]]
author = "..."
version = "1.0.0"
```

### **Check 3: modmain.lua Content**
Should start with:
```lua
GLOBAL.setmetatable(env, {__index = function(t, k) return GLOBAL.rawget(GLOBAL, k) end})
```

---

## 🎯 **QUICK FIX STEPS**

### **If mod still doesn't appear:**

1. **Double-check folder location**
   ```
   [DST Install]/mods/tu_tien_bi_canh/modinfo.lua
   ```

2. **Restart everything**
   - Close DST
   - Close Steam  
   - Restart Steam
   - Launch DST

3. **Check file encoding**
   - modinfo.lua should be UTF-8 encoded
   - No special characters in file names

4. **Try minimal test**
   - Create simple test mod first
   - If that works, then check our mod files

---

## 📞 **GET HELP**

If mod still doesn't work:

1. **Check console output** (press ~ in game)
2. **Copy any error messages**
3. **Verify DST version** (should be latest)
4. **Check if other mods work**

**Most common issue**: Wrong folder location or file permissions!

---

## ✅ **SUCCESS INDICATORS**

When properly installed, you should see:

1. **In Mod Menu**: "Tu Tiên Bí Cảnh (Cultivation Secret Realms)" appears
2. **When Enabled**: No red errors in console
3. **In Game**: Can run test commands successfully
4. **Player**: Has cultivation components when spawned

**Ready to test realm system once mod loads properly!** 🌟
